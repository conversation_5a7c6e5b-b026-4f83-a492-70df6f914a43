# CLAUDE.md - Logseq Clone

Original Logseq codebase for reference and analysis.

## Quick Start

```bash
yarn install     # Install dependencies  
yarn watch       # Start dev server at localhost:3001
```

## Key Architecture

- **ClojureScript** + **Shadow-CLJS** build system
- **DataScript** in-memory database with Datalog queries
- **Rum** React components for UI
- **Electron** for desktop, **Capacitor** for mobile

## Core Structure

```
src/main/frontend/    # Main application code
src/electron/         # Desktop app process
static/              # Build outputs
deps/                # Local dependencies
```

## Purpose

Reference implementation for studying:
1. **Block-based editing** patterns
2. **Graph storage** architecture  
3. **Component structure** for FAP translation
4. **DataScript** query patterns

**Analysis docs**: See `../docs_md/architecture/` for detailed study results.

This serves as baseline for the FAP reimplementation in `../loqsec-functional/`.