# Pure JavaScript Alternatives to Excalidraw (No React)

Excalidraw is a great tool, but it depends on React. Here are several open-source, pure JavaScript libraries you can use instead — perfect for vanilla JS apps or Electron environments.

---

## 1. Literally Canvas

- **URL:** [https://github.com/literallycanvas/literallycanvas](https://github.com/literallycanvas/literallycanvas)
- **Type:** Canvas-based sketching/drawing library
- **Framework:** Pure JavaScript (no React)
- **Features:** Freeform drawing, shapes, undo/redo, serialization
- **License:** BSD

```html
<!-- Example -->
<div class="literally"></div>
<script>
  LC.init(document.querySelector(".literally"), { imageURLPrefix: "/img" });
</script>
```

---

## 2. Fabric.js

- **URL:** [http://fabricjs.com/](http://fabricjs.com/)
- **Type:** Powerful canvas drawing and object model library
- **Framework:** Pure JavaScript
- **Features:** Shapes, text, groups, image filters, interactive objects
- **License:** MIT

```js
const canvas = new fabric.Canvas('c');
const rect = new fabric.Rect({ width: 100, height: 100, fill: 'red' });
canvas.add(rect);
```

---

## 3. Konva.js

- **URL:** [https://konvajs.org/](https://konvajs.org/)
- **Type:** High-performance canvas abstraction
- **Framework:** Pure JavaScript (React bindings separate)
- **Features:** Scene graph, shapes, drag/drop, layers, events
- **License:** MIT

---

## 4. Zebra.js

- **URL:** [https://github.com/barmalei/zebra](https://github.com/barmalei/zebra)
- **Type:** Rich UI and graphics library
- **Framework:** Pure JavaScript
- **Features:** GUI toolkit + graphics, good for structured diagrams
- **License:** MIT

---

## 5. Rough.js

- **URL:** [https://github.com/rough-stuff/rough](https://github.com/rough-stuff/rough)
- **Type:** Hand-drawn sketchy-style renderer
- **Framework:** Pure JavaScript
- **Features:** Line/shape drawing with Excalidraw-style visuals
- **Note:** Rendering only — interaction/UI must be built manually
- **License:** MIT

---

## 6. Pencil.js

- **URL:** [https://pencil.js.org/](https://pencil.js.org/)
- **Type:** Modular 2D drawing library
- **Framework:** Pure JavaScript
- **Features:** Vector shapes, animations, interaction
- **License:** MIT

---

## Recommendation for Excalidraw-Like UX

To build something closer to Excalidraw:

- Use **Rough.js** for sketch-style visuals
- Combine with **Fabric.js** or **Konva.js** for interaction/state handling
- Add minimal UI controls (e.g., with your own HTML + DOM logic)

---

## Want More?

Let me know if you'd like:

- A minimal interactive drawing sample using Rough.js
- A boilerplate Excalidraw-style clone in pure JS
- Electron-compatible code snippets

