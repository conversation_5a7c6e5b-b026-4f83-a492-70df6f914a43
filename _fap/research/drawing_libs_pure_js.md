# Pure JavaScript Alternatives to Excalidraw (No React)

Excalidraw is a great tool, but it depends on React. Here are several open-source, pure JavaScript libraries you can use instead — perfect for vanilla JS apps, Electron environments, and FAP (Freedom Application Platform) implementations.

**Key Requirements for FAP:**
- ✅ Zero React dependencies
- ✅ Vanilla JavaScript or minimal dependencies
- ✅ Canvas or SVG-based rendering
- ✅ Interactive drawing capabilities
- ✅ Serialization/export support

---

## 1. Literally Canvas

- **URL:** [https://github.com/literallycanvas/literallycanvas](https://github.com/literallycanvas/literallycanvas)
- **Type:** Canvas-based sketching/drawing library
- **Framework:** Pure JavaScript (no React)
- **Features:** Freeform drawing, shapes, undo/redo, serialization
- **License:** BSD

```html
<!-- Example -->
<div class="literally"></div>
<script>
  LC.init(document.querySelector(".literally"), { imageURLPrefix: "/img" });
</script>
```

---

## 2. Fabric.js

- **URL:** [http://fabricjs.com/](http://fabricjs.com/)
- **Type:** Powerful canvas drawing and object model library
- **Framework:** Pure JavaScript
- **Features:** Shapes, text, groups, image filters, interactive objects
- **License:** MIT

```js
const canvas = new fabric.Canvas('c');
const rect = new fabric.Rect({ width: 100, height: 100, fill: 'red' });
canvas.add(rect);
```

---

## 3. Konva.js

- **URL:** [https://konvajs.org/](https://konvajs.org/)
- **Type:** High-performance canvas abstraction
- **Framework:** Pure JavaScript (React bindings separate)
- **Features:** Scene graph, shapes, drag/drop, layers, events
- **License:** MIT

---

## 4. Zebra.js

- **URL:** [https://github.com/barmalei/zebra](https://github.com/barmalei/zebra)
- **Type:** Rich UI and graphics library
- **Framework:** Pure JavaScript
- **Features:** GUI toolkit + graphics, good for structured diagrams
- **License:** MIT

---

## 5. Rough.js

- **URL:** [https://github.com/rough-stuff/rough](https://github.com/rough-stuff/rough)
- **Type:** Hand-drawn sketchy-style renderer
- **Framework:** Pure JavaScript
- **Features:** Line/shape drawing with Excalidraw-style visuals
- **Note:** Rendering only — interaction/UI must be built manually
- **License:** MIT

---

## 6. Pencil.js

- **URL:** [https://pencil.js.org/](https://pencil.js.org/)
- **Type:** Modular 2D drawing library
- **Framework:** Pure JavaScript
- **Features:** Vector shapes, animations, interaction
- **License:** MIT

---

## 7. Paper.js ⭐ **HIGHLY RECOMMENDED**

- **URL:** [http://paperjs.org/](http://paperjs.org/)
- **Type:** Vector graphics scripting framework (Canvas + SVG)
- **Framework:** Pure JavaScript
- **Features:**
  - Powerful vector graphics API
  - Path manipulation and boolean operations
  - Interactive drawing tools
  - SVG import/export
  - Excellent documentation and examples
  - Used by professional design tools
- **License:** MIT
- **FAP Rating:** ⭐⭐⭐⭐⭐ (Perfect for Excalidraw replacement)

```js
// Paper.js example - very clean API
var path = new paper.Path();
path.strokeColor = 'black';
path.add(new paper.Point(30, 25));
path.add(new paper.Point(25, 25));
```

---

## 8. Perfect Freehand ⭐ **EXCELLENT FOR HANDWRITING**

- **URL:** [https://github.com/steveruizok/perfect-freehand](https://github.com/steveruizok/perfect-freehand)
- **Type:** Pressure-sensitive freehand drawing
- **Framework:** Pure JavaScript (no dependencies)
- **Features:**
  - Perfect smooth freehand lines
  - Pressure sensitivity simulation
  - Works with Canvas, SVG, or any renderer
  - Tiny size (~10KB)
  - Used by tldraw and other professional tools
- **License:** MIT
- **FAP Rating:** ⭐⭐⭐⭐⭐ (Perfect for natural drawing)

```js
import { getStroke } from 'perfect-freehand'

const stroke = getStroke(points, {
  size: 16,
  thinning: 0.5,
  smoothing: 0.5,
  streamline: 0.5,
})
```

---

## 9. SVG.js

- **URL:** [https://svgjs.dev/](https://svgjs.dev/)
- **Type:** Lightweight SVG manipulation library
- **Framework:** Pure JavaScript
- **Features:**
  - Clean SVG API
  - Animations and morphing
  - Plugin ecosystem
  - Interactive elements
  - Small footprint (~27KB)
- **License:** MIT
- **FAP Rating:** ⭐⭐⭐⭐ (Great for structured diagrams)

```js
// SVG.js example
var draw = SVG().addTo('#drawing').size(300, 300)
var rect = draw.rect(100, 100).attr({ fill: '#f06' })
```

---

## 🎯 **Top Recommendations for FAP Excalidraw Replacement**

### **Option 1: Paper.js + Rough.js (Best Overall)**
```js
// Combine Paper.js for interaction + Rough.js for sketchy style
import rough from 'roughjs/bundled/rough.esm.js'
import paper from 'paper'

const canvas = document.getElementById('canvas')
const rc = rough.canvas(canvas)
paper.setup(canvas)

// Draw sketchy rectangle with Paper.js interaction
const tool = new paper.Tool()
tool.onMouseDown = (event) => {
  rc.rectangle(event.point.x, event.point.y, 100, 50, {
    roughness: 1.2,
    stroke: '#000'
  })
}
```

### **Option 2: Perfect Freehand + Fabric.js (Best for Drawing)**
```js
// Perfect natural drawing with object management
import { getStroke } from 'perfect-freehand'
import { fabric } from 'fabric'

const canvas = new fabric.Canvas('canvas')
let currentPath = []

canvas.on('mouse:down', (e) => {
  currentPath = [{ x: e.pointer.x, y: e.pointer.y, pressure: 0.5 }]
})

canvas.on('mouse:move', (e) => {
  if (currentPath.length > 0) {
    currentPath.push({ x: e.pointer.x, y: e.pointer.y, pressure: 0.5 })
    const stroke = getStroke(currentPath)
    // Render smooth stroke to canvas
  }
})
```

### **Option 3: SVG.js + Rough.js (Lightweight)**
```js
// Minimal setup for simple diagrams
import SVG from '@svgdotjs/svg.js'
import rough from 'roughjs/bundled/rough.esm.js'

const draw = SVG().addTo('#drawing').size(800, 600)
const rc = rough.svg(draw.node)

// Add sketchy shapes
const rect = rc.rectangle(10, 10, 100, 50, { fill: 'red', fillStyle: 'hachure' })
draw.node.appendChild(rect)
```

---

## 📊 **Comparison Matrix for FAP**

| **Library** | **Bundle Size** | **Learning Curve** | **Features** | **FAP Rating** |
|-------------|-----------------|-------------------|--------------|----------------|
| **Paper.js** | ~140KB | Medium | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| **Perfect Freehand** | ~10KB | Easy | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| **Fabric.js** | ~200KB | Medium | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ |
| **Rough.js** | ~50KB | Easy | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| **SVG.js** | ~27KB | Easy | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ |
| **Konva.js** | ~170KB | Medium | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ |

---

## 🚀 **Implementation Strategy for Logseq FAP**

### **Phase 1: Minimal Drawing (Immediate)**
- **Perfect Freehand** for natural pen/pencil drawing
- **Rough.js** for basic shapes (rectangles, circles, arrows)
- Custom HTML UI for tool selection

### **Phase 2: Advanced Features (Later)**
- **Paper.js** for complex vector operations
- Text annotations and labels
- Export to SVG/PNG
- Collaborative editing hooks

### **Phase 3: Integration (Future)**
- Save drawings as Logseq blocks
- Link drawings to notes
- Version control for drawings
- P2P sync of drawing data

---

## 💡 **Why These Are Better Than Excalidraw for FAP**

1. **Zero React Dependencies** - Pure JavaScript, perfect for FAP
2. **Smaller Bundle Sizes** - Most are <200KB vs Excalidraw's ~2MB
3. **More Flexible** - Can be customized without React constraints
4. **Better Performance** - Direct Canvas/SVG manipulation
5. **Easier Integration** - Works with any HTML/JS architecture

---

## 🛠 **Next Steps**

Would you like me to create:

1. **Minimal drawing prototype** using Perfect Freehand + Rough.js
2. **FAP-compatible drawing component** with HTML custom elements
3. **Integration guide** for embedding in Logseq blocks
4. **Performance comparison** of the top 3 options

