# FAP Design Guides

**Freedom Application Platform** guides for building semantic, functional web applications.

## Core Guides

- **`design_guides/fap_best_practices.md`** - Complete FAP implementation patterns
- **`design_guides/fap_css_guide.md`** - CSS conventions and styling
- **`design_guides/fap-html-template.html`** - Working template
- **`design_guides/fap_infinite_canvas_guide.md`** - Canvas applications

## FAP Principles

- **HTML-first**: Semantic custom elements
- **Functional JavaScript**: Pure functions, closures for state
- **Zero dependencies**: Direct browser execution
- **Component boundaries**: Tag selectors, minimal utilities

## Quick Example

```html
<fap-counter>
  <button-decrement>–</button-decrement>
  <span-value>0</span-value>
  <button-increment>+</button-increment>
</fap-counter>
```

```js
window.fap = window.fap || {};
Object.assign(window.fap, {
  counter: Object.freeze({
    enhance: (el) => { /* pure enhancement */ }
  })
});
```

**Working Example**: See `../loqsec-functional/packages/fap-tree/` for complete reference implementation.