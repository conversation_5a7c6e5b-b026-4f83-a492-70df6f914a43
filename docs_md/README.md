# Documentation Directory

This directory contains all documentation for the Loqseq-Augment project, organized by topic.

## Directory Structure

### `architecture/`
Analysis of Logseq's core architecture and component system:
- **LOGSEQ_ARCHITECTURE.md** - Technology stack, directory structure, and data flow
- **CORE_COMPONENTS.md** - Essential components to extract for functional version

### `research/`  
Research findings from exploring and testing Logseq:
- **BUILD_ANALYSIS.md** - Build system complexity analysis and simplification strategy

### `planning/`
Implementation plans for the functional programming version:
- **FUNCTIONAL_PLAN.md** - Complete roadmap for pure functional Logseq implementation

### Root Level
- **RESTART_INSTRUCTIONS.md** - Quick reference for restarting development after breaks

## Navigation

Each document builds on the previous ones:
1. Start with **architecture/** to understand what we're working with
2. Review **research/** to see what we learned from testing
3. Use **planning/** to understand the implementation strategy
4. Reference **RESTART_INSTRUCTIONS.md** when resuming work

## Purpose

This documentation serves as:
- **Knowledge base** for understanding Logseq's architecture
- **Reference guide** for the functional reimplementation  
- **Project memory** to maintain context across sessions
- **Design decisions record** for the simplified approach