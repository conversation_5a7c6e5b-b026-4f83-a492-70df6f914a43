# Naming Conventions

This document establishes consistent naming patterns for the Loqseq-Augment project to ensure clarity, maintainability, and logical organization across all code and files.

## Quick Reference

| Context | Convention | Example |
|---------|-----------|---------|
| Directories | `kebab-case` | `fap-tree-core/`, `user-interface/` |
| Files | `snake_case` | `fap_tree_core.js`, `user_data.css` |
| HTML IDs/Classes | `kebab-case` | `#main-content`, `.tree-node` |
| JS Variables | `camelCase` | `userData`, `renderTree()` |
| JS Constants | `UPPER_SNAKE_CASE` | `MAX_DEPTH`, `API_BASE_URL` |
| Custom Tags | `kebab-case` + prefix | `<fap-tree>`, `<fap-block>` |

## Detailed Guidelines

### 1. Directory Names: `kebab-case`

**Pattern**: Use hyphens to separate words, all lowercase

**Examples**:
- ✅ `fap-tree-core/`
- ✅ `user-interface/`
- ✅ `data-storage/`
- ❌ `fap_tree_core/`
- ❌ `UserInterface/`

**Rationale**: URL-friendly, readable, standard for web projects and package managers.

### 2. File Names: `snake_case`

**Pattern**: Use underscores to separate words, all lowercase

**Examples**:
- ✅ `fap_tree_core.js`
- ✅ `user_interface.css`
- ✅ `data_storage_utils.html`
- ❌ `fap-tree-core.js`
- ❌ `UserInterface.css`

**Rationale**: Filesystem-safe across all platforms (Windows, macOS, Linux), avoids issues with case-sensitive filesystems, clear word separation.

### 3. HTML/CSS Identifiers: `kebab-case`

**Pattern**: Use hyphens for IDs, classes, and CSS selectors

**Examples**:
- ✅ `<div id="fs-tree">`
- ✅ `.fap-tree-label`
- ✅ `#main-content`
- ❌ `<div id="fsTree">`
- ❌ `.fap_tree_label`

**Rationale**: Web standard, CSS convention, matches HTML5 specifications.

### 4. JavaScript Variables & Functions: `camelCase`

**Pattern**: First word lowercase, subsequent words capitalized

**Examples**:
- ✅ `fsTree`
- ✅ `origRender`
- ✅ `getUserData()`
- ✅ `handleTreeClick()`
- ❌ `fs_tree`
- ❌ `OrigRender`

**Rationale**: JavaScript language standard, widely adopted convention.

### 5. JavaScript Constants: `UPPER_SNAKE_CASE`

**Pattern**: All uppercase with underscores

**Examples**:
- ✅ `DEFAULT_CONFIG`
- ✅ `MAX_DEPTH`
- ✅ `API_ENDPOINTS`
- ✅ `TREE_NODE_TYPES`
- ❌ `defaultConfig`
- ❌ `MaxDepth`

**Rationale**: Traditional constant naming convention, clearly distinguishes immutable values.

### 6. Custom HTML Tags: `kebab-case` with Prefix

**Pattern**: Use project prefix + hyphen + descriptive name

**Examples**:
- ✅ `<fap-tree>`
- ✅ `<fap-block>`
- ✅ `<fap-editor>`
- ✅ `<logseq-node>`
- ❌ `<fapTree>`
- ❌ `<fap_tree>`

**Rationale**: HTML5 standard for custom elements, namespace consistency, prevents conflicts with future HTML standards.

## Special Cases

### File Extensions
Always use lowercase for file extensions:
- ✅ `.js`, `.css`, `.html`, `.md`
- ❌ `.JS`, `.CSS`, `.HTML`

### Acronyms in Names
Treat acronyms as single words:
- ✅ `apiResponse` (camelCase)
- ✅ `API_BASE_URL` (constants)
- ✅ `html-parser` (kebab-case)
- ❌ `APIResponse`, `HTML-Parser`

### Version Numbers
Use semantic versioning format:
- ✅ `v1.2.3`, `1.0.0-beta.1`
- ❌ `v1_2_3`, `version-1-2-3`

## Benefits of This System

1. **Logical Separation**: Different contexts use different conventions, making the codebase self-documenting
2. **Tool Compatibility**: Works well with all major tools, frameworks, and platforms
3. **Team Consistency**: Clear rules prevent naming debates and inconsistencies
4. **Future-Proof**: Follows established web standards and best practices
5. **Search-Friendly**: Consistent patterns make code easier to search and navigate

## Enforcement

- Use linting tools where possible to enforce naming conventions
- Include naming checks in code review process
- Update this document when adding new naming contexts
- Reference this document in onboarding materials for new contributors

## Migration Notes

When updating existing files to match these conventions:
1. Update file references in import/require statements
2. Update CSS selectors and HTML references
3. Test thoroughly to ensure no broken links
4. Use find-and-replace tools carefully to avoid unintended changes