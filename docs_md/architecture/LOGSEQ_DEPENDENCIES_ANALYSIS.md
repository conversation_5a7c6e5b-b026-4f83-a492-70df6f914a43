# Logseq Dependencies Analysis

## Overview
Analysis of all third-party dependencies in `loqsec-clone/package.json` to determine which are essential for FAP implementation and which can be replaced or eliminated.

## Runtime Dependencies Analysis

| Package | What It Does | Alternatives | Needed for FAP? | Comments |
|---------|-------------|-------------|-----------------|----------|
| **Core Framework** |
| `react` | UI framework for components | Native Web Components, Lit, Vue | ❌ No | FAP uses semantic HTML + vanilla JS |
| `react-dom` | React DOM rendering | Native DOM APIs | ❌ No | FAP renders directly to DOM |
| `prop-types` | React prop validation | TypeScript, JSDoc | ❌ No | Not needed without React |
| **Mobile/Desktop** |
| `@capacitor/*` (14 packages) | Mobile app framework | Cordova, Tauri, PWA | ⚠️ Maybe | Only if targeting mobile apps |
| `electron` | Desktop app framework | Tauri, Neutralino | ⚠️ Maybe | Only if targeting desktop apps |
| `@ionic/*` | Mobile UI components | Native mobile APIs | ❌ No | FAP uses semantic HTML |
| **File System & Storage** |
| `@isomorphic-git/lightning-fs` | In-memory file system for browsers | IndexedDB, OPFS, custom FS | ✅ Yes | **Critical** - Hard to replicate, well-tested |
| `@logseq/sqlite-wasm` | SQLite in WebAssembly | IndexedDB, Dexie.js | ⚠️ Maybe | Powerful but complex, could use simpler storage |
| `fs-extra` | Enhanced Node.js file operations | Native fs module | ❌ No | Only needed for Node.js/Electron |
| `chokidar` | File system watcher | Native File System API | ❌ No | Browser has native file watching |
| **Text Processing & Parsing** |
| `marked` | Markdown parser | Unified, Remark | ⚠️ Maybe | Could implement basic markdown parser |
| `mldoc` | Logseq's document parser | Custom parser | ❌ No | Logseq-specific, can simplify |
| `katex` | Math rendering | MathJax, custom | ⚠️ Maybe | Useful for math notation |
| `codemirror` | Code editor | Monaco, Ace, custom | ⚠️ Maybe | Powerful but heavy, could use contenteditable |
| `dompurify` | HTML sanitization | Custom sanitizer | ✅ Yes | **Security critical** - Hard to implement safely |
| **Date & Time** |
| `@js-joda/*` (3 packages) | Date/time handling | Native Date, date-fns | ❌ No | Native Date API sufficient for basic needs |
| `chrono-node` | Natural language date parsing | Custom parser | ❌ No | Nice-to-have, not essential |
| **UI & Interaction** |
| `@dnd-kit/*` | Drag and drop | Native HTML5 DnD | ⚠️ Maybe | Native DnD is complex, this is well-tested |
| `interactjs` | Touch/mouse interactions | Native event handlers | ❌ No | Can implement with native events |
| `react-*` (8 packages) | React-specific UI components | Native implementations | ❌ No | Not needed without React |
| **Graphics & Visualization** |
| `@excalidraw/excalidraw` | Drawing/whiteboard tool | Custom canvas, Fabric.js | ✅ Yes | **Complex to replicate** - Keep as generic component |
| `pixi.js` + `pixi-graph-fork` | 2D graphics rendering | Canvas API, SVG | ⚠️ Maybe | Powerful but heavy, evaluate if needed |
| `d3-force` | Force-directed graph layout | Custom algorithm | ⚠️ Maybe | Useful for graph visualization |
| `html2canvas` | HTML to canvas conversion | Native canvas APIs | ⚠️ Maybe | Useful for screenshots/exports |
| **Media & Assets** |
| `pdfjs-dist` | PDF rendering | Native PDF viewer | ✅ Yes | **Complex to replicate** - Essential for PDF support |
| `photoswipe` | Image gallery/lightbox | Custom lightbox | ⚠️ Maybe | Nice UX but can implement simpler version |
| **Search & Text** |
| `fuse.js` | Fuzzy search | Custom search | ⚠️ Maybe | Good fuzzy search is complex |
| `grapheme-splitter` | Unicode text splitting | Native string methods | ❌ No | Modern browsers handle Unicode well |
| `remove-accents` | Text normalization | Native Intl.Collator | ❌ No | Can use native internationalization |
| **Utilities** |
| `bignumber.js` | Arbitrary precision numbers | Native BigInt | ❌ No | BigInt covers most use cases |
| `diff` | Text diffing algorithm | Custom implementation | ⚠️ Maybe | Useful for version control features |
| `ignore` | .gitignore-style filtering | Custom patterns | ❌ No | Simple to implement |
| `jszip` | ZIP file handling | Native compression APIs | ⚠️ Maybe | Useful for export/import |
| `sanitize-filename` | Filename sanitization | Custom function | ❌ No | Simple to implement |
| `yargs-parser` | Command line parsing | Custom parser | ❌ No | Not needed in browser |
| **Icons & Fonts** |
| `@tabler/icons-*` | Icon library | Custom SVG icons | ⚠️ Maybe | Large but useful icon set |
| `inter-ui` | Inter font family | System fonts, Google Fonts | ❌ No | Can use web fonts or system fonts |
| **Emoji & Social** |
| `@emoji-mart/*` + `emoji-mart` | Emoji picker | Native emoji, simple picker | ❌ No | Can implement basic emoji support |
| `@logseq/react-tweet-embed` | Tweet embedding | Custom embed | ❌ No | Social media specific |
| **Analytics & Monitoring** |
| `@sentry/*` | Error tracking | Custom error handling | ❌ No | Can implement simple error logging |
| `posthog-js` | Analytics | Custom analytics | ❌ No | Privacy-focused app shouldn't need this |
| **Threading & Communication** |
| `comlink` | Web Worker communication | MessageChannel, custom RPC | ⚠️ Maybe | Useful for worker communication |
| `threads` | Worker thread management | Native Web Workers | ❌ No | Can manage workers directly |
| **Specialized** |
| `@logseq/diff-merge` | Logseq-specific diffing | Custom implementation | ❌ No | Logseq-specific |
| `graphology` | Graph data structures | Custom graph implementation | ⚠️ Maybe | Useful for link graphs |
| `shepherd.js` | User onboarding tours | Custom tooltips | ❌ No | Can implement simple help system |
| `@tippyjs/react` | Tooltip library | Custom tooltips | ❌ No | Simple to implement |
| `@radix-ui/colors` | Color system | Custom CSS variables | ❌ No | Can define own color palette |

## Summary by Category

### ✅ **Essential (Keep)** - Hard to replicate safely
1. `@isomorphic-git/lightning-fs` - Browser file system
2. `dompurify` - HTML sanitization (security)
3. `@excalidraw/excalidraw` - Drawing tools (if keeping whiteboards)
4. `pdfjs-dist` - PDF rendering (if keeping PDF support)

### ⚠️ **Evaluate** - Useful but could be replaced
1. `@dnd-kit/*` - Drag and drop (complex to implement well)
2. `marked` - Markdown parsing (could implement basic version)
3. `fuse.js` - Fuzzy search (complex algorithm)
4. `katex` - Math rendering (if keeping math support)
5. `comlink` - Worker communication (useful pattern)
6. `@logseq/sqlite-wasm` - Database (powerful but heavy)

### ❌ **Remove** - Not needed for FAP
1. All React packages (18 packages)
2. All Capacitor/Ionic packages (16 packages) - unless targeting mobile
3. Date/time libraries (can use native Date)
4. Most utility libraries (simple to implement)
5. Analytics and tracking packages

## FAP Implementation Strategy

### Phase 1: Core Dependencies (4-6 packages)
- `@isomorphic-git/lightning-fs` for file system
- `dompurify` for security
- `marked` for basic markdown (or implement custom)
- `fuse.js` for search (or implement custom)

### Phase 2: Enhanced Features (Optional)
- `@excalidraw/excalidraw` for whiteboard functionality
- `pdfjs-dist` for PDF support
- `katex` for math rendering
- `@dnd-kit/core` for drag and drop

### Phase 3: Platform-Specific (If Needed)
- Capacitor packages for mobile apps
- Electron for desktop apps

## Size Reduction Potential

**Current**: ~1200 dependencies (including transitive)
**FAP Target**: 4-10 direct dependencies
**Estimated Size Reduction**: 95%+ reduction in dependency count

This aligns with the FAP philosophy of minimal dependencies while keeping the most valuable and hard-to-replicate functionality.
