# Logseq Database Architecture Guide

## Overview

Logseq uses a sophisticated dual-database architecture that combines the best of both worlds:

1. **DataScript** - An immutable, in-memory database for reactive queries and real-time UI updates
2. **SQLite WASM** - A persistent, relational database for storage and complex queries

This guide explains how these databases work, their relationship, and how to understand and work with them.

## 🗄️ Database Types in Logseq

### 1. DataScript - The Reactive Layer

**What is DataScript?**
- An immutable, in-memory database inspired by Datomic
- Uses **Datalog** query language (similar to Prolog)
- Designed for ClojureScript and browser environments
- Provides reactive subscriptions to data changes

**Key Concepts:**
- **Datoms**: Atomic facts in the form `[entity-id attribute value transaction-id]`
- **Entities**: Collections of attributes about a thing (like a block or page)
- **Attributes**: Properties that describe entities (like `:block/title`, `:block/uuid`)
- **Transactions**: Immutable sets of changes to the database

**Example DataScript Query:**
```clojure
;; Find all TODO blocks
[:find (pull ?b [*])
 :where
 [?b :block/marker "TODO"]]

;; Find pages with specific properties
[:find ?page-name
 :where
 [?p :block/name ?page-name]
 [?p :block/properties ?props]
 [(contains? ?props "priority")]]
```

### 2. SQLite WASM - The Persistence Layer

**What is SQLite WASM?**
- SQLite database compiled to WebAssembly
- Runs entirely in the browser with no server required
- Provides ACID transactions and SQL queries
- Uses Origin Private File System (OPFS) for persistence

**Key Features:**
- **WAL Mode**: Write-Ahead Logging for better concurrency
- **OPFS Storage**: Browser-native file system for persistence
- **Web Workers**: Runs in background threads to avoid blocking UI
- **Full SQL Support**: Complex joins, aggregations, and indexes

## 🏗️ Database Architecture

### Dual-Database Pattern

```
┌─────────────────┐    ┌─────────────────┐
│   DataScript    │    │   SQLite WASM   │
│  (In-Memory)    │◄──►│  (Persistent)   │
│                 │    │                 │
│ • Reactive UI   │    │ • Long-term     │
│ • Fast queries  │    │   storage       │
│ • Immutable     │    │ • Complex SQL   │
│ • Datalog       │    │ • ACID          │
└─────────────────┘    └─────────────────┘
         ▲                       ▲
         │                       │
         ▼                       ▼
┌─────────────────────────────────────────┐
│           Application Layer             │
│                                         │
│ • UI Components subscribe to DataScript │
│ • Changes written to both databases     │
│ • SQLite provides persistence          │
└─────────────────────────────────────────┘
```

### Data Flow

1. **User Action** → Creates transaction
2. **DataScript** → Updates in-memory state immediately
3. **UI Reacts** → Components re-render based on DataScript changes
4. **SQLite** → Persists changes asynchronously in Web Worker
5. **Sync** → Ensures both databases stay consistent

## 📊 Schema Structure

### DataScript Schema (File-based Graphs)

```clojure
{:block/uuid {:db/unique :db.unique/identity}
 :block/parent {:db/valueType :db.type/ref
                :db/index true}
 :block/page {:db/valueType :db.type/ref
              :db/index true}
 :block/refs {:db/valueType :db.type/ref
              :db/cardinality :db.cardinality/many}
 :block/title {:db/index true}
 :block/content {}
 :block/properties {}
 :block/created-at {:db/index true}
 :block/updated-at {:db/index true}
 
 ;; File-specific attributes
 :file/path {:db/unique :db.unique/identity}
 :file/content {}
 :file/last-modified-at {}}
```

### Key Schema Attributes

| Attribute | Type | Purpose | Example |
|-----------|------|---------|---------|
| `:block/uuid` | UUID | Unique identifier | `#uuid "123e4567-e89b-12d3-a456-************"` |
| `:block/title` | String | Block content | `"This is a block"` |
| `:block/parent` | Ref | Parent block | Points to another block entity |
| `:block/page` | Ref | Containing page | Points to page entity |
| `:block/refs` | Ref (many) | Referenced blocks/pages | `[page1 page2 block3]` |
| `:block/properties` | Map | Key-value properties | `{"priority" "high", "due" "2024-01-01"}` |
| `:block/marker` | String | Task status | `"TODO"`, `"DONE"`, `"LATER"` |
| `:block/tags` | Ref (many) | Tagged pages | `[#tag1 #tag2]` |

## 🔍 Query Examples

### DataScript Queries (Datalog)

```clojure
;; 1. Find all blocks containing specific text
[:find (pull ?b [:block/title :block/uuid])
 :where
 [?b :block/title ?title]
 [(clojure.string/includes? ?title "important")]]

;; 2. Find all TODO blocks on a specific page
[:find (pull ?b [*])
 :where
 [?p :block/name "my-page"]
 [?b :block/page ?p]
 [?b :block/marker "TODO"]]

;; 3. Find blocks with specific properties
[:find ?block-title ?priority
 :where
 [?b :block/title ?block-title]
 [?b :block/properties ?props]
 [(get ?props "priority") ?priority]]

;; 4. Find all pages that reference a specific page
[:find (pull ?p [:block/name :block/title])
 :where
 [?target :block/name "target-page"]
 [?b :block/refs ?target]
 [?b :block/page ?p]]

;; 5. Complex query with rules
[:find (pull ?b [*])
 :in $ %
 :where
 (task ?b #{"TODO" "DOING"})
 (priority ?b #{"A" "B"})]
```

### SQLite Queries (SQL)

```sql
-- 1. Find blocks by content (full-text search)
SELECT * FROM blocks_fts 
WHERE blocks_fts MATCH 'important'
ORDER BY rank;

-- 2. Complex aggregation query
SELECT 
  page_name,
  COUNT(*) as block_count,
  COUNT(CASE WHEN marker = 'TODO' THEN 1 END) as todo_count
FROM blocks b
JOIN pages p ON b.page_id = p.id
GROUP BY page_name
HAVING todo_count > 0;

-- 3. Time-based queries
SELECT * FROM blocks 
WHERE created_at > datetime('now', '-7 days')
ORDER BY created_at DESC;
```

## 🔧 Storage Implementation

### DataScript Storage Layer

DataScript uses a custom storage interface that persists to SQLite:

```clojure
(defn new-sqlite-storage [sqlite-db]
  (reify IStorage
    (-store [_ addr+data-seq _delete-addrs]
      ;; Store DataScript data as serialized content in SQLite
      (let [data (map (fn [[addr data]]
                        {:addr addr
                         :content (transit-write data)
                         :addresses (json-stringify addresses)})
                      addr+data-seq)]
        (upsert-addr-content! sqlite-db data)))
    
    (-restore [_ addr]
      ;; Restore DataScript data from SQLite
      (restore-data-from-addr sqlite-db addr))))
```

### SQLite Tables

**KVS Table** (DataScript storage):
```sql
CREATE TABLE kvs (
  addr INTEGER PRIMARY KEY,
  content TEXT,
  addresses JSON
);
```

**Search Tables** (Full-text search):
```sql
CREATE VIRTUAL TABLE blocks_fts USING fts5(
  uuid,
  content,
  page
);
```

## 🚀 Performance Characteristics

### DataScript Advantages
- **Instant Queries**: In-memory, no I/O overhead
- **Reactive**: Automatic UI updates when data changes
- **Immutable**: No race conditions, safe concurrent access
- **Datalog**: Powerful recursive queries and joins

### SQLite Advantages
- **Persistence**: Data survives browser restarts
- **Full-Text Search**: Built-in FTS5 for content search
- **Complex Queries**: SQL for aggregations and analytics
- **ACID**: Guaranteed consistency and durability

### Performance Trade-offs

| Operation | DataScript | SQLite | Best For |
|-----------|------------|--------|----------|
| Simple lookups | ⚡ Instant | 🐌 Slower | Real-time UI |
| Complex aggregations | 🐌 Limited | ⚡ Fast | Analytics |
| Full-text search | ❌ No | ⚡ Fast | Content search |
| Reactive updates | ⚡ Built-in | ❌ Manual | Live UI |
| Large datasets | 🐌 Memory limited | ⚡ Scalable | Big graphs |

## 🛠️ FAP Implementation Strategy

For a FAP implementation, you have several options:

### Option 1: Simplified Single Database
```javascript
// Use only SQLite WASM with reactive wrapper
class FAPDatabase {
  constructor() {
    this.sqlite = new SQLiteWASM();
    this.subscribers = new Map();
  }
  
  query(sql, params) {
    return this.sqlite.exec(sql, params);
  }
  
  subscribe(query, callback) {
    // Simple reactive layer on top of SQLite
    this.subscribers.set(query, callback);
  }
}
```

### Option 2: Lightweight DataScript Alternative
```javascript
// Minimal in-memory reactive store
class FAPReactiveStore {
  constructor() {
    this.data = new Map();
    this.subscribers = new Set();
  }
  
  transact(changes) {
    // Apply changes and notify subscribers
    changes.forEach(change => this.applyChange(change));
    this.notifySubscribers();
  }
}
```

### Option 3: Hybrid Approach
- Use **SQLite WASM** for persistence and complex queries
- Use **simple reactive layer** for UI updates
- Implement **basic Datalog-style queries** for common patterns

## 📚 Learning Resources

### DataScript
- [Official DataScript GitHub](https://github.com/tonsky/datascript)
- [DataScript Tutorial](https://github.com/kristianmandrup/datascript-tutorial)
- [Datalog Query Language Guide](https://docs.datomic.com/on-prem/query.html)

### SQLite WASM
- [SQLite WASM Official Docs](https://sqlite.org/wasm/doc/trunk/index.md)
- [Chrome Developer Blog on SQLite WASM](https://developer.chrome.com/blog/sqlite-wasm-in-the-browser-backed-by-the-origin-private-file-system)
- [Origin Private File System](https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API)

## 🎯 Key Takeaways

1. **Dual Database**: DataScript for reactivity + SQLite for persistence
2. **Datalog**: Powerful query language for graph-like data
3. **Immutable**: DataScript's immutability enables safe concurrent access
4. **Web Workers**: SQLite runs in background to avoid blocking UI
5. **OPFS**: Modern browser storage for SQLite persistence
6. **Schema Design**: Careful attribute design enables efficient queries
7. **FAP Strategy**: Can simplify to single database with reactive wrapper

This architecture enables Logseq to provide both instant UI responsiveness and robust data persistence, making it a powerful reference for building similar applications.
