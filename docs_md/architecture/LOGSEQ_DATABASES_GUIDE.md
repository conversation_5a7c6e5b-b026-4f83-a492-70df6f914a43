# Logseq Database Architecture Guide

## Overview

Logseq uses a sophisticated dual-database architecture that combines the best of both worlds:

1. **DataScript** - An immutable, in-memory database for reactive queries and real-time UI updates
2. **SQLite WASM** - A persistent, relational database for storage and complex queries

This guide explains how these databases work, their relationship, and evaluates alternatives for FAP implementation.

## ⚠️ **Important for FAP Implementation**

**DataScript is NOT suitable for vanilla JavaScript projects** because it requires ClojureScript runtime and compilation. For FAP (zero-dependency, HTML-first) applications, we recommend **SQLite WASM** or **DuckDB WASM** as better alternatives.

## 🗄️ Database Types in Logseq

### 1. DataScript - The Reactive Layer

**What is DataScript?**
- An immutable, in-memory database inspired by Datomic
- Uses **Datalog** query language (similar to Prolog)
- Designed for ClojureScript and browser environments
- Provides reactive subscriptions to data changes

**Key Concepts:**
- **Datoms**: Atomic facts in the form `[entity-id attribute value transaction-id]`
- **Entities**: Collections of attributes about a thing (like a block or page)
- **Attributes**: Properties that describe entities (like `:block/title`, `:block/uuid`)
- **Transactions**: Immutable sets of changes to the database

**Example DataScript Query:**
```clojure
;; Find all TODO blocks
[:find (pull ?b [*])
 :where
 [?b :block/marker "TODO"]]

;; Find pages with specific properties
[:find ?page-name
 :where
 [?p :block/name ?page-name]
 [?p :block/properties ?props]
 [(contains? ?props "priority")]]
```

### 2. SQLite WASM - The Persistence Layer

**What is SQLite WASM?**
- SQLite database compiled to WebAssembly
- Runs entirely in the browser with no server required
- Provides ACID transactions and SQL queries
- Uses Origin Private File System (OPFS) for persistence

**Key Features:**
- **WAL Mode**: Write-Ahead Logging for better concurrency
- **OPFS Storage**: Browser-native file system for persistence
- **Web Workers**: Runs in background threads to avoid blocking UI
- **Full SQL Support**: Complex joins, aggregations, and indexes

## 🏗️ Database Architecture

### Dual-Database Pattern

```
┌─────────────────┐    ┌─────────────────┐
│   DataScript    │    │   SQLite WASM   │
│  (In-Memory)    │◄──►│  (Persistent)   │
│                 │    │                 │
│ • Reactive UI   │    │ • Long-term     │
│ • Fast queries  │    │   storage       │
│ • Immutable     │    │ • Complex SQL   │
│ • Datalog       │    │ • ACID          │
└─────────────────┘    └─────────────────┘
         ▲                       ▲
         │                       │
         ▼                       ▼
┌─────────────────────────────────────────┐
│           Application Layer             │
│                                         │
│ • UI Components subscribe to DataScript │
│ • Changes written to both databases     │
│ • SQLite provides persistence          │
└─────────────────────────────────────────┘
```

### Data Flow

1. **User Action** → Creates transaction
2. **DataScript** → Updates in-memory state immediately
3. **UI Reacts** → Components re-render based on DataScript changes
4. **SQLite** → Persists changes asynchronously in Web Worker
5. **Sync** → Ensures both databases stay consistent

## 📊 Schema Structure

### DataScript Schema (File-based Graphs)

```clojure
{:block/uuid {:db/unique :db.unique/identity}
 :block/parent {:db/valueType :db.type/ref
                :db/index true}
 :block/page {:db/valueType :db.type/ref
              :db/index true}
 :block/refs {:db/valueType :db.type/ref
              :db/cardinality :db.cardinality/many}
 :block/title {:db/index true}
 :block/content {}
 :block/properties {}
 :block/created-at {:db/index true}
 :block/updated-at {:db/index true}
 
 ;; File-specific attributes
 :file/path {:db/unique :db.unique/identity}
 :file/content {}
 :file/last-modified-at {}}
```

### Key Schema Attributes

| Attribute | Type | Purpose | Example |
|-----------|------|---------|---------|
| `:block/uuid` | UUID | Unique identifier | `#uuid "123e4567-e89b-12d3-a456-************"` |
| `:block/title` | String | Block content | `"This is a block"` |
| `:block/parent` | Ref | Parent block | Points to another block entity |
| `:block/page` | Ref | Containing page | Points to page entity |
| `:block/refs` | Ref (many) | Referenced blocks/pages | `[page1 page2 block3]` |
| `:block/properties` | Map | Key-value properties | `{"priority" "high", "due" "2024-01-01"}` |
| `:block/marker` | String | Task status | `"TODO"`, `"DONE"`, `"LATER"` |
| `:block/tags` | Ref (many) | Tagged pages | `[#tag1 #tag2]` |

## 🔍 Query Examples

### DataScript Queries (Datalog)

```clojure
;; 1. Find all blocks containing specific text
[:find (pull ?b [:block/title :block/uuid])
 :where
 [?b :block/title ?title]
 [(clojure.string/includes? ?title "important")]]

;; 2. Find all TODO blocks on a specific page
[:find (pull ?b [*])
 :where
 [?p :block/name "my-page"]
 [?b :block/page ?p]
 [?b :block/marker "TODO"]]

;; 3. Find blocks with specific properties
[:find ?block-title ?priority
 :where
 [?b :block/title ?block-title]
 [?b :block/properties ?props]
 [(get ?props "priority") ?priority]]

;; 4. Find all pages that reference a specific page
[:find (pull ?p [:block/name :block/title])
 :where
 [?target :block/name "target-page"]
 [?b :block/refs ?target]
 [?b :block/page ?p]]

;; 5. Complex query with rules
[:find (pull ?b [*])
 :in $ %
 :where
 (task ?b #{"TODO" "DOING"})
 (priority ?b #{"A" "B"})]
```

### SQLite Queries (SQL)

```sql
-- 1. Find blocks by content (full-text search)
SELECT * FROM blocks_fts 
WHERE blocks_fts MATCH 'important'
ORDER BY rank;

-- 2. Complex aggregation query
SELECT 
  page_name,
  COUNT(*) as block_count,
  COUNT(CASE WHEN marker = 'TODO' THEN 1 END) as todo_count
FROM blocks b
JOIN pages p ON b.page_id = p.id
GROUP BY page_name
HAVING todo_count > 0;

-- 3. Time-based queries
SELECT * FROM blocks 
WHERE created_at > datetime('now', '-7 days')
ORDER BY created_at DESC;
```

## 🔧 Storage Implementation

### DataScript Storage Layer

DataScript uses a custom storage interface that persists to SQLite:

```clojure
(defn new-sqlite-storage [sqlite-db]
  (reify IStorage
    (-store [_ addr+data-seq _delete-addrs]
      ;; Store DataScript data as serialized content in SQLite
      (let [data (map (fn [[addr data]]
                        {:addr addr
                         :content (transit-write data)
                         :addresses (json-stringify addresses)})
                      addr+data-seq)]
        (upsert-addr-content! sqlite-db data)))
    
    (-restore [_ addr]
      ;; Restore DataScript data from SQLite
      (restore-data-from-addr sqlite-db addr))))
```

### SQLite Tables

**KVS Table** (DataScript storage):
```sql
CREATE TABLE kvs (
  addr INTEGER PRIMARY KEY,
  content TEXT,
  addresses JSON
);
```

**Search Tables** (Full-text search):
```sql
CREATE VIRTUAL TABLE blocks_fts USING fts5(
  uuid,
  content,
  page
);
```

## 🚀 Performance Characteristics

### DataScript Advantages
- **Instant Queries**: In-memory, no I/O overhead
- **Reactive**: Automatic UI updates when data changes
- **Immutable**: No race conditions, safe concurrent access
- **Datalog**: Powerful recursive queries and joins

### SQLite Advantages
- **Persistence**: Data survives browser restarts
- **Full-Text Search**: Built-in FTS5 for content search
- **Complex Queries**: SQL for aggregations and analytics
- **ACID**: Guaranteed consistency and durability

### Performance Trade-offs

| Operation | DataScript | SQLite | Best For |
|-----------|------------|--------|----------|
| Simple lookups | ⚡ Instant | 🐌 Slower | Real-time UI |
| Complex aggregations | 🐌 Limited | ⚡ Fast | Analytics |
| Full-text search | ❌ No | ⚡ Fast | Content search |
| Reactive updates | ⚡ Built-in | ❌ Manual | Live UI |
| Large datasets | 🐌 Memory limited | ⚡ Scalable | Big graphs |

## 🛠️ FAP Database Implementation Options

For FAP implementation, **DataScript is not suitable** due to ClojureScript dependencies. Here are the recommended alternatives:

## 🆚 **SQLite WASM vs DuckDB WASM Comparison**

| Feature | SQLite WASM | DuckDB WASM | Winner |
|---------|-------------|-------------|---------|
| **Size** | ~2.3MB compressed | ~3.2MB compressed | 🏆 SQLite |
| **OLTP Performance** | ⚡ Excellent | 🐌 Good | 🏆 SQLite |
| **OLAP Performance** | 🐌 Limited | ⚡ Excellent | 🏆 DuckDB |
| **Memory Usage** | 🏆 Low | 📈 Higher | 🏆 SQLite |
| **Ecosystem Maturity** | 🏆 Very Mature | 🆕 Growing | 🏆 SQLite |
| **JSON Support** | ✅ Good | ⚡ Excellent | 🏆 DuckDB |
| **Parquet Support** | ❌ Limited | ⚡ Native | 🏆 DuckDB |
| **Full-Text Search** | ⚡ FTS5 | ✅ Basic | 🏆 SQLite |
| **Extensions** | ✅ Some | ⚡ Rich | 🏆 DuckDB |
| **Learning Curve** | 🏆 Familiar SQL | 📚 Advanced SQL | 🏆 SQLite |

### **Recommendation for FAP: SQLite WASM**

For Logseq FAP implementation, **SQLite WASM is the better choice** because:

1. **Smaller Bundle**: 2.3MB vs 3.2MB (30% smaller)
2. **OLTP Workload**: Logseq is primarily transactional (create/edit blocks)
3. **Mature Ecosystem**: Better browser compatibility and stability
4. **Full-Text Search**: Essential for Logseq's search functionality
5. **Simpler API**: Easier to integrate with zero-dependency approach

### **When to Consider DuckDB WASM**

DuckDB WASM would be better if you need:
- **Analytics**: Complex aggregations over large datasets
- **Parquet Files**: Direct reading of analytical data formats
- **Advanced JSON**: Complex JSON transformations and queries
- **Data Science**: Statistical functions and window operations

## 💡 **Recommended FAP Architecture**

### Option 1: SQLite WASM + Reactive Layer
```javascript
// Lightweight reactive wrapper around SQLite WASM
class FAPDatabase {
  constructor() {
    this.sqlite = new SQLiteWASM();
    this.subscribers = new Map();
    this.queryCache = new Map();
  }

  async query(sql, params = []) {
    const result = await this.sqlite.exec(sql, params);
    this.notifySubscribers(sql);
    return result;
  }

  subscribe(queryKey, callback) {
    if (!this.subscribers.has(queryKey)) {
      this.subscribers.set(queryKey, new Set());
    }
    this.subscribers.get(queryKey).add(callback);
  }

  // Reactive query that auto-updates UI
  reactiveQuery(sql, params = []) {
    const queryKey = `${sql}:${JSON.stringify(params)}`;

    return {
      subscribe: (callback) => this.subscribe(queryKey, callback),
      execute: () => this.query(sql, params)
    };
  }
}
```

### Option 2: Hybrid SQLite + In-Memory Cache
```javascript
// Fast in-memory cache for frequently accessed data
class FAPHybridStore {
  constructor() {
    this.sqlite = new SQLiteWASM();
    this.memoryCache = new Map(); // For hot data
    this.subscribers = new Set();
  }

  async getBlock(uuid) {
    // Check memory cache first
    if (this.memoryCache.has(uuid)) {
      return this.memoryCache.get(uuid);
    }

    // Fallback to SQLite
    const result = await this.sqlite.exec(
      'SELECT * FROM blocks WHERE uuid = ?', [uuid]
    );

    if (result.length > 0) {
      this.memoryCache.set(uuid, result[0]);
      return result[0];
    }
    return null;
  }

  async updateBlock(uuid, data) {
    // Update both cache and SQLite
    this.memoryCache.set(uuid, data);
    await this.sqlite.exec(
      'UPDATE blocks SET content = ?, updated_at = ? WHERE uuid = ?',
      [data.content, Date.now(), uuid]
    );
    this.notifySubscribers();
  }
}
```

### Option 3: Simple Event-Driven Store
```javascript
// Minimal reactive store without external dependencies
class FAPEventStore {
  constructor() {
    this.data = new Map();
    this.listeners = new Map();
  }

  set(key, value) {
    this.data.set(key, value);
    this.emit('change', { key, value });
    this.emit(`change:${key}`, value);
  }

  get(key) {
    return this.data.get(key);
  }

  on(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set());
    }
    this.listeners.get(event).add(callback);
  }

  emit(event, data) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).forEach(callback => callback(data));
    }
  }
}
```

## 📚 Learning Resources

### SQLite WASM (Recommended for FAP)
- [SQLite WASM Official Docs](https://sqlite.org/wasm/doc/trunk/index.md)
- [Chrome Developer Blog on SQLite WASM](https://developer.chrome.com/blog/sqlite-wasm-in-the-browser-backed-by-the-origin-private-file-system)
- [Origin Private File System](https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API)
- [SQLite FTS5 Full-Text Search](https://sqlite.org/fts5.html)

### DuckDB WASM (For Analytics Use Cases)
- [DuckDB WASM GitHub](https://github.com/duckdb/duckdb-wasm)
- [DuckDB WASM Shell Demo](https://shell.duckdb.org)
- [DuckDB Extensions](https://duckdb.org/docs/extensions/overview)
- [DuckDB vs SQLite Comparison](https://duckdb.org/why_duckdb)

### DataScript (ClojureScript Only)
- [Official DataScript GitHub](https://github.com/tonsky/datascript)
- [DataScript Tutorial](https://github.com/kristianmandrup/datascript-tutorial)
- [Datalog Query Language Guide](https://docs.datomic.com/on-prem/query.html)

## 🎯 Key Takeaways for FAP Implementation

1. **❌ DataScript**: Not suitable for vanilla JavaScript (requires ClojureScript)
2. **✅ SQLite WASM**: Best choice for FAP - smaller, mature, excellent OLTP performance
3. **🤔 DuckDB WASM**: Consider for analytics-heavy applications with large datasets
4. **🏗️ Architecture**: Single database + reactive layer is simpler than dual-database
5. **📦 Bundle Size**: SQLite WASM is 30% smaller than DuckDB WASM
6. **🔍 Search**: SQLite's FTS5 is superior for full-text search
7. **⚡ Performance**: SQLite excels at transactional workloads (perfect for note-taking)
8. **🛠️ Simplicity**: Fewer dependencies = easier maintenance and debugging

## 🚀 **Final Recommendation**

For **Logseq FAP implementation**, use:

```javascript
// Simple, effective, zero-dependency approach
const fapDB = new SQLiteWASM();

// Add reactive layer for UI updates
class LogseqFAP {
  constructor() {
    this.db = fapDB;
    this.subscribers = new Map();
  }

  async createBlock(content, parentId) {
    const uuid = crypto.randomUUID();
    await this.db.exec(`
      INSERT INTO blocks (uuid, content, parent_id, created_at)
      VALUES (?, ?, ?, ?)
    `, [uuid, content, parentId, Date.now()]);

    this.notifySubscribers('blocks:changed');
    return uuid;
  }
}
```

This approach gives you **95% of Logseq's functionality** with **5% of the complexity** - perfect for the FAP philosophy of simplicity and effectiveness.
