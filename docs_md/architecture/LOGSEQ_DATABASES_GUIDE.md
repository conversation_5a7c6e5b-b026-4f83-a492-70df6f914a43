# Logseq Database Architecture Guide

## Overview

Logseq uses a sophisticated dual-database architecture that combines the best of both worlds:

1. **DataScript** - An immutable, in-memory database for reactive queries and real-time UI updates
2. **SQLite WASM** - A persistent, relational database for storage and complex queries

This guide explains how these databases work, their relationship, and evaluates alternatives for FAP implementation.

## ⚠️ **Important for FAP Implementation**

**DataScript is NOT suitable for vanilla JavaScript projects** because it requires ClojureScript runtime and compilation. For FAP (zero-dependency, HTML-first) applications, we recommend **SQLite WASM** or **DuckDB WASM** as better alternatives.

## 🗄️ Database Types in Logseq

### 1. DataScript - The Reactive Layer

**What is DataScript?**
- An immutable, in-memory database inspired by Datomic
- Uses **Datalog** query language (similar to Prolog)
- Designed for ClojureScript and browser environments
- Provides reactive subscriptions to data changes

**Key Concepts:**
- **Datoms**: Atomic facts in the form `[entity-id attribute value transaction-id]`
- **Entities**: Collections of attributes about a thing (like a block or page)
- **Attributes**: Properties that describe entities (like `:block/title`, `:block/uuid`)
- **Transactions**: Immutable sets of changes to the database

**Example DataScript Query:**
```clojure
;; Find all TODO blocks
[:find (pull ?b [*])
 :where
 [?b :block/marker "TODO"]]

;; Find pages with specific properties
[:find ?page-name
 :where
 [?p :block/name ?page-name]
 [?p :block/properties ?props]
 [(contains? ?props "priority")]]
```

### 2. SQLite WASM - The Persistence Layer

**What is SQLite WASM?**
- SQLite database compiled to WebAssembly
- Runs entirely in the browser with no server required
- Provides ACID transactions and SQL queries
- Uses Origin Private File System (OPFS) for persistence

**Key Features:**
- **WAL Mode**: Write-Ahead Logging for better concurrency
- **OPFS Storage**: Browser-native file system for persistence
- **Web Workers**: Runs in background threads to avoid blocking UI
- **Full SQL Support**: Complex joins, aggregations, and indexes

## 🏗️ Database Architecture

### Dual-Database Pattern

```
┌─────────────────┐    ┌─────────────────┐
│   DataScript    │    │   SQLite WASM   │
│  (In-Memory)    │◄──►│  (Persistent)   │
│                 │    │                 │
│ • Reactive UI   │    │ • Long-term     │
│ • Fast queries  │    │   storage       │
│ • Immutable     │    │ • Complex SQL   │
│ • Datalog       │    │ • ACID          │
└─────────────────┘    └─────────────────┘
         ▲                       ▲
         │                       │
         ▼                       ▼
┌─────────────────────────────────────────┐
│           Application Layer             │
│                                         │
│ • UI Components subscribe to DataScript │
│ • Changes written to both databases     │
│ • SQLite provides persistence          │
└─────────────────────────────────────────┘
```

### Data Flow

1. **User Action** → Creates transaction
2. **DataScript** → Updates in-memory state immediately
3. **UI Reacts** → Components re-render based on DataScript changes
4. **SQLite** → Persists changes asynchronously in Web Worker
5. **Sync** → Ensures both databases stay consistent

## 📊 Schema Structure

### DataScript Schema (File-based Graphs)

```clojure
{:block/uuid {:db/unique :db.unique/identity}
 :block/parent {:db/valueType :db.type/ref
                :db/index true}
 :block/page {:db/valueType :db.type/ref
              :db/index true}
 :block/refs {:db/valueType :db.type/ref
              :db/cardinality :db.cardinality/many}
 :block/title {:db/index true}
 :block/content {}
 :block/properties {}
 :block/created-at {:db/index true}
 :block/updated-at {:db/index true}
 
 ;; File-specific attributes
 :file/path {:db/unique :db.unique/identity}
 :file/content {}
 :file/last-modified-at {}}
```

### Key Schema Attributes

| Attribute | Type | Purpose | Example |
|-----------|------|---------|---------|
| `:block/uuid` | UUID | Unique identifier | `#uuid "123e4567-e89b-12d3-a456-************"` |
| `:block/title` | String | Block content | `"This is a block"` |
| `:block/parent` | Ref | Parent block | Points to another block entity |
| `:block/page` | Ref | Containing page | Points to page entity |
| `:block/refs` | Ref (many) | Referenced blocks/pages | `[page1 page2 block3]` |
| `:block/properties` | Map | Key-value properties | `{"priority" "high", "due" "2024-01-01"}` |
| `:block/marker` | String | Task status | `"TODO"`, `"DONE"`, `"LATER"` |
| `:block/tags` | Ref (many) | Tagged pages | `[#tag1 #tag2]` |

## 🔍 Query Examples

### DataScript Queries (Datalog)

```clojure
;; 1. Find all blocks containing specific text
[:find (pull ?b [:block/title :block/uuid])
 :where
 [?b :block/title ?title]
 [(clojure.string/includes? ?title "important")]]

;; 2. Find all TODO blocks on a specific page
[:find (pull ?b [*])
 :where
 [?p :block/name "my-page"]
 [?b :block/page ?p]
 [?b :block/marker "TODO"]]

;; 3. Find blocks with specific properties
[:find ?block-title ?priority
 :where
 [?b :block/title ?block-title]
 [?b :block/properties ?props]
 [(get ?props "priority") ?priority]]

;; 4. Find all pages that reference a specific page
[:find (pull ?p [:block/name :block/title])
 :where
 [?target :block/name "target-page"]
 [?b :block/refs ?target]
 [?b :block/page ?p]]

;; 5. Complex query with rules
[:find (pull ?b [*])
 :in $ %
 :where
 (task ?b #{"TODO" "DOING"})
 (priority ?b #{"A" "B"})]
```

### SQLite Queries (SQL)

```sql
-- 1. Find blocks by content (full-text search)
SELECT * FROM blocks_fts 
WHERE blocks_fts MATCH 'important'
ORDER BY rank;

-- 2. Complex aggregation query
SELECT 
  page_name,
  COUNT(*) as block_count,
  COUNT(CASE WHEN marker = 'TODO' THEN 1 END) as todo_count
FROM blocks b
JOIN pages p ON b.page_id = p.id
GROUP BY page_name
HAVING todo_count > 0;

-- 3. Time-based queries
SELECT * FROM blocks 
WHERE created_at > datetime('now', '-7 days')
ORDER BY created_at DESC;
```

## 🔧 Storage Implementation

### DataScript Storage Layer

DataScript uses a custom storage interface that persists to SQLite:

```clojure
(defn new-sqlite-storage [sqlite-db]
  (reify IStorage
    (-store [_ addr+data-seq _delete-addrs]
      ;; Store DataScript data as serialized content in SQLite
      (let [data (map (fn [[addr data]]
                        {:addr addr
                         :content (transit-write data)
                         :addresses (json-stringify addresses)})
                      addr+data-seq)]
        (upsert-addr-content! sqlite-db data)))
    
    (-restore [_ addr]
      ;; Restore DataScript data from SQLite
      (restore-data-from-addr sqlite-db addr))))
```

### SQLite Tables

**KVS Table** (DataScript storage):
```sql
CREATE TABLE kvs (
  addr INTEGER PRIMARY KEY,
  content TEXT,
  addresses JSON
);
```

**Search Tables** (Full-text search):
```sql
CREATE VIRTUAL TABLE blocks_fts USING fts5(
  uuid,
  content,
  page
);
```

## 🚀 Performance Characteristics

### DataScript Advantages
- **Instant Queries**: In-memory, no I/O overhead
- **Reactive**: Automatic UI updates when data changes
- **Immutable**: No race conditions, safe concurrent access
- **Datalog**: Powerful recursive queries and joins

### SQLite Advantages
- **Persistence**: Data survives browser restarts
- **Full-Text Search**: Built-in FTS5 for content search
- **Complex Queries**: SQL for aggregations and analytics
- **ACID**: Guaranteed consistency and durability

### Performance Trade-offs

| Operation | DataScript | SQLite | Best For |
|-----------|------------|--------|----------|
| Simple lookups | ⚡ Instant | 🐌 Slower | Real-time UI |
| Complex aggregations | 🐌 Limited | ⚡ Fast | Analytics |
| Full-text search | ❌ No | ⚡ Fast | Content search |
| Reactive updates | ⚡ Built-in | ❌ Manual | Live UI |
| Large datasets | 🐌 Memory limited | ⚡ Scalable | Big graphs |

## 🛠️ FAP Database Implementation Options

For FAP implementation, **DataScript is not suitable** due to ClojureScript dependencies. Here are the recommended alternatives:

## 🆚 **Database Comparison for FAP Implementation**

| Feature | SQLite WASM | DuckDB WASM | TerminusDB | Winner for FAP |
|---------|-------------|-------------|------------|----------------|
| **Bundle Size** | ~2.3MB | ~3.2MB | Client only (~200KB) | 🏆 **TerminusDB** |
| **OLTP Performance** | ⚡ Excellent | 🐌 Good | ⚡ Excellent | 🏆 **SQLite/TerminusDB** |
| **OLAP Performance** | 🐌 Limited | ⚡ Excellent | ✅ Good | 🏆 **DuckDB** |
| **Memory Usage** | 🏆 Low | 📈 Higher | 🏆 Low | 🏆 **SQLite/TerminusDB** |
| **Ecosystem Maturity** | 🏆 Very Mature | 🆕 Growing | 🆕 Emerging | 🏆 **SQLite** |
| **JSON Support** | ✅ Good | ⚡ Excellent | ⚡ Native | 🏆 **DuckDB/TerminusDB** |
| **Graph Queries** | ❌ None | ❌ Limited | ⚡ Native | 🏆 **TerminusDB** |
| **Full-Text Search** | ⚡ FTS5 | ✅ Basic | ✅ Basic | 🏆 **SQLite** |
| **Versioning/Git-like** | ❌ None | ❌ None | ⚡ Native | 🏆 **TerminusDB** |
| **Offline-First** | ⚡ Perfect | ⚡ Perfect | 🤔 Hybrid | 🏆 **SQLite/DuckDB** |
| **Learning Curve** | 🏆 Familiar | 📚 Advanced | 📚 Advanced | 🏆 **SQLite** |
| **Zero Dependencies** | ✅ Yes | ✅ Yes | ❌ Server Required | 🏆 **SQLite/DuckDB** |

## 🔍 **TerminusDB Deep Dive**

**TerminusDB** is a fascinating option that brings unique capabilities:

### **What is TerminusDB?**
- **Graph Database**: Native support for linked data and relationships
- **Document Store**: JSON-LD documents with graph connections
- **Git-for-Data**: Branch, merge, rollback, and time-travel operations
- **Immutable**: All changes are preserved with full history
- **RDF/OWL**: Semantic web standards with SPARQL-like queries
- **Datalog Engine**: Logic programming for complex graph queries

### **TerminusDB Unique Features**
```javascript
// Git-like operations on data
await client.branch("feature-branch");
await client.addDocument(newData);
await client.commit("Added new feature data");
await client.merge("main", "feature-branch");

// Time travel queries
const pastState = await client.checkout("2024-01-01");
const historicalData = await client.query(myQuery);

// Graph relationships
const schema = {
  "@type": "Class",
  "@id": "Block",
  "content": "xsd:string",
  "parent": "Block",  // Self-referential relationship
  "children": { "@type": "Set", "@class": "Block" }
};
```

### **TerminusDB Pros for Logseq FAP**
✅ **Perfect for hierarchical data** (blocks, pages, relationships)
✅ **Native graph queries** (backlinks, references, paths)
✅ **Git-like versioning** (perfect for collaborative editing)
✅ **Immutable history** (never lose data, full audit trail)
✅ **Small client bundle** (~200KB vs 2.3MB SQLite)
✅ **JSON-LD native** (semantic web standards)
✅ **Schema validation** (type safety for documents)

### **TerminusDB Cons for FAP**
❌ **Requires server** (not truly zero-dependency)
❌ **Learning curve** (Datalog, RDF concepts)
❌ **Network dependency** (can't work fully offline)
❌ **Smaller ecosystem** (fewer resources, examples)
❌ **Complex setup** (server deployment required)

### **Recommendation Update**

**For pure FAP (zero-dependency): SQLite WASM**
**For enhanced FAP with server: TerminusDB**
**For analytics-heavy FAP: DuckDB WASM**

### **When to Choose TerminusDB**

TerminusDB is **perfect** if you need:
- **Collaborative editing** with branching/merging
- **Complex graph relationships** (backlinks, references)
- **Data versioning** and audit trails
- **Semantic queries** across linked documents
- **Schema evolution** with migration support
- **Multi-user synchronization**

### **When to Avoid TerminusDB**

Stick with SQLite WASM if you need:
- **True offline-first** operation
- **Zero server dependencies**
- **Simpler deployment** (static files only)
- **Familiar SQL** queries
- **Smaller learning curve**

## 💡 **FAP Architecture Options**

### Option 1: Pure FAP - SQLite WASM + Reactive Layer
```javascript
// Zero-dependency, offline-first approach
class FAPSQLiteDatabase {
  constructor() {
    this.sqlite = new SQLiteWASM();
    this.subscribers = new Map();
    this.queryCache = new Map();
  }

  async query(sql, params = []) {
    const result = await this.sqlite.exec(sql, params);
    this.notifySubscribers(sql);
    return result;
  }

  subscribe(queryKey, callback) {
    if (!this.subscribers.has(queryKey)) {
      this.subscribers.set(queryKey, new Set());
    }
    this.subscribers.get(queryKey).add(callback);
  }

  // Reactive query that auto-updates UI
  reactiveQuery(sql, params = []) {
    const queryKey = `${sql}:${JSON.stringify(params)}`;

    return {
      subscribe: (callback) => this.subscribe(queryKey, callback),
      execute: () => this.query(sql, params)
    };
  }
}
```

### Option 2: Enhanced FAP - TerminusDB Client
```javascript
// Git-for-data with graph capabilities
class FAPTerminusDatabase {
  constructor(serverUrl, credentials) {
    this.client = new TerminusClient.WOQLClient(serverUrl, credentials);
    this.subscribers = new Map();
    this.currentBranch = 'main';
  }

  // Create a new feature branch for editing
  async createFeatureBranch(branchName) {
    await this.client.branch(branchName);
    this.currentBranch = branchName;
    return branchName;
  }

  // Add a block with automatic relationships
  async addBlock(content, parentId = null) {
    const blockData = {
      "@type": "Block",
      "@id": `Block/${crypto.randomUUID()}`,
      "content": content,
      "created_at": new Date().toISOString(),
      "parent": parentId ? `Block/${parentId}` : null
    };

    await this.client.addDocument(blockData);
    this.notifySubscribers('blocks:changed');
    return blockData["@id"];
  }

  // Query with graph traversal
  async getBlockWithChildren(blockId) {
    const query = `
      SELECT ?block ?content ?child ?childContent WHERE {
        ?block rdf:type Block .
        ?block content ?content .
        OPTIONAL {
          ?child parent ?block .
          ?child content ?childContent .
        }
        FILTER(?block = Block/${blockId})
      }
    `;
    return await this.client.query(query);
  }

  // Merge changes back to main
  async publishChanges(commitMessage) {
    await this.client.commit(commitMessage);
    await this.client.merge('main', this.currentBranch);
    this.currentBranch = 'main';
  }

  // Time travel to previous state
  async viewHistoricalState(timestamp) {
    const commits = await this.client.log();
    const targetCommit = commits.find(c =>
      new Date(c.timestamp) <= new Date(timestamp)
    );
    if (targetCommit) {
      await this.client.checkout(targetCommit.id);
    }
  }
}
```

### Option 3: Hybrid Approach - Best of Both Worlds
```javascript
// Combine local SQLite with TerminusDB sync
class FAPHybridDatabase {
  constructor(terminusConfig) {
    this.localDB = new SQLiteWASM();
    this.remoteDB = new TerminusClient.WOQLClient(
      terminusConfig.url,
      terminusConfig.credentials
    );
    this.syncQueue = [];
    this.isOnline = navigator.onLine;
  }

  // Work offline-first with local SQLite
  async addBlockLocal(content, parentId) {
    const uuid = crypto.randomUUID();
    const block = {
      uuid,
      content,
      parent_id: parentId,
      created_at: Date.now(),
      synced: false
    };

    await this.localDB.exec(`
      INSERT INTO blocks (uuid, content, parent_id, created_at, synced)
      VALUES (?, ?, ?, ?, ?)
    `, [uuid, content, parentId, block.created_at, 0]);

    // Queue for sync when online
    this.syncQueue.push({ action: 'create', data: block });
    this.attemptSync();

    return uuid;
  }

  // Sync with TerminusDB when online
  async attemptSync() {
    if (!this.isOnline || this.syncQueue.length === 0) return;

    try {
      // Create a sync branch
      const syncBranch = `sync-${Date.now()}`;
      await this.remoteDB.branch(syncBranch);

      // Apply all queued changes
      for (const change of this.syncQueue) {
        if (change.action === 'create') {
          await this.remoteDB.addDocument({
            "@type": "Block",
            "@id": `Block/${change.data.uuid}`,
            "content": change.data.content,
            "parent": change.data.parent_id ?
              `Block/${change.data.parent_id}` : null,
            "created_at": new Date(change.data.created_at).toISOString()
          });
        }
      }

      // Commit and merge
      await this.remoteDB.commit("Sync local changes");
      await this.remoteDB.merge('main', syncBranch);

      // Mark as synced locally
      await this.localDB.exec(
        'UPDATE blocks SET synced = 1 WHERE synced = 0'
      );

      this.syncQueue = [];

    } catch (error) {
      console.warn('Sync failed, will retry later:', error);
    }
  }

  // Get blocks with fallback strategy
  async getBlocks(query = {}) {
    // Try local first (always available)
    const localResults = await this.localDB.exec(`
      SELECT * FROM blocks
      WHERE content LIKE ?
      ORDER BY created_at DESC
    `, [`%${query.search || ''}%`]);

    // If online, also check remote for latest
    if (this.isOnline) {
      try {
        const remoteResults = await this.remoteDB.query(`
          SELECT ?block ?content ?created WHERE {
            ?block rdf:type Block .
            ?block content ?content .
            ?block created_at ?created .
            FILTER(contains(?content, "${query.search || ''}"))
          }
          ORDER BY DESC(?created)
        `);

        // Merge and deduplicate results
        return this.mergeResults(localResults, remoteResults);
      } catch (error) {
        console.warn('Remote query failed, using local only:', error);
      }
    }

    return localResults;
  }
}
```

## 📚 Learning Resources

### SQLite WASM (Recommended for FAP)
- [SQLite WASM Official Docs](https://sqlite.org/wasm/doc/trunk/index.md)
- [Chrome Developer Blog on SQLite WASM](https://developer.chrome.com/blog/sqlite-wasm-in-the-browser-backed-by-the-origin-private-file-system)
- [Origin Private File System](https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API)
- [SQLite FTS5 Full-Text Search](https://sqlite.org/fts5.html)

### DuckDB WASM (For Analytics Use Cases)
- [DuckDB WASM GitHub](https://github.com/duckdb/duckdb-wasm)
- [DuckDB WASM Shell Demo](https://shell.duckdb.org)
- [DuckDB Extensions](https://duckdb.org/docs/extensions/overview)
- [DuckDB vs SQLite Comparison](https://duckdb.org/why_duckdb)

### TerminusDB (Graph Database with Git-for-Data)
- [TerminusDB Official Documentation](https://terminusdb.org/docs/)
- [TerminusDB JavaScript Client](https://github.com/terminusdb/terminusdb-client-js)
- [TerminusDB Git-for-Data Tutorial](https://terminusdb.com/blog/succinct-data-structures-for-modern-databases/)
- [RDF and JSON-LD Primer](https://json-ld.org/primer/latest/)

### DataScript (ClojureScript Only)
- [Official DataScript GitHub](https://github.com/tonsky/datascript)
- [DataScript Tutorial](https://github.com/kristianmandrup/datascript-tutorial)
- [Datalog Query Language Guide](https://docs.datomic.com/on-prem/query.html)

## 🎯 Key Takeaways for FAP Implementation

1. **❌ DataScript**: Not suitable for vanilla JavaScript (requires ClojureScript)
2. **✅ SQLite WASM**: Best for pure FAP - offline-first, zero dependencies, mature
3. **🤔 DuckDB WASM**: Consider for analytics-heavy applications with large datasets
4. **🌟 TerminusDB**: Excellent for collaborative, graph-heavy applications (requires server)
5. **🏗️ Architecture**: Choose based on your collaboration and offline requirements
6. **📦 Bundle Size**: TerminusDB client (200KB) < SQLite WASM (2.3MB) < DuckDB WASM (3.2MB)
7. **🔍 Search**: SQLite's FTS5 is superior for full-text search
8. **⚡ Performance**: SQLite/TerminusDB excel at transactional workloads
9. **🔄 Versioning**: Only TerminusDB offers git-like data operations
10. **🛠️ Complexity**: SQLite (simple) < DuckDB (moderate) < TerminusDB (advanced)

## 🚀 **Final Recommendations**

### **Pure FAP (Zero Dependencies)**: SQLite WASM
```javascript
// Offline-first, zero-dependency approach
const fapDB = new SQLiteWASM();

class LogseqFAP {
  constructor() {
    this.db = fapDB;
    this.subscribers = new Map();
  }

  async createBlock(content, parentId) {
    const uuid = crypto.randomUUID();
    await this.db.exec(`
      INSERT INTO blocks (uuid, content, parent_id, created_at)
      VALUES (?, ?, ?, ?)
    `, [uuid, content, parentId, Date.now()]);

    this.notifySubscribers('blocks:changed');
    return uuid;
  }
}
```

### **Enhanced FAP (With Server)**: TerminusDB
```javascript
// Git-for-data with graph capabilities
const terminusDB = new TerminusClient.WOQLClient(serverUrl, credentials);

class LogseqEnhancedFAP {
  async createFeatureBranch(name) {
    await terminusDB.branch(name);
    return name;
  }

  async addBlockWithRelations(content, tags, references) {
    const blockData = {
      "@type": "Block",
      "@id": `Block/${crypto.randomUUID()}`,
      "content": content,
      "tags": tags.map(tag => ({ "@id": `Tag/${tag}` })),
      "references": references.map(ref => ({ "@id": `Block/${ref}` }))
    };

    await terminusDB.addDocument(blockData);
    return blockData["@id"];
  }

  async publishChanges(message) {
    await terminusDB.commit(message);
    await terminusDB.merge('main', currentBranch);
  }
}
```

### **Decision Matrix**

| **Need** | **Recommendation** | **Why** |
|----------|-------------------|---------|
| **Offline-first** | SQLite WASM | No server dependency |
| **Collaboration** | TerminusDB | Git-like branching/merging |
| **Graph queries** | TerminusDB | Native graph traversal |
| **Analytics** | DuckDB WASM | OLAP performance |
| **Simplicity** | SQLite WASM | Familiar SQL, mature ecosystem |
| **Versioning** | TerminusDB | Immutable history, time travel |
| **Zero dependencies** | SQLite WASM | True FAP approach |

**Bottom Line**: **SQLite WASM for pure FAP**, **TerminusDB for collaborative FAP** - both give you 95% of Logseq's functionality with dramatically reduced complexity!
