# Core Components Analysis

## 🎯 Essential Components for Functional Version

### 1. Block System (`src/main/frontend/components/block.cljs`)
**What it does:**
- Hierarchical text blocks (like bullet points)
- Each block can contain text, links, and nested blocks
- Supports indent/outdent operations

**Key Functions to Extract:**
```clojure
- create-block
- update-block-content  
- move-block
- indent-block
- outdent-block
```

**Functional Redesign:**
```javascript
// Pure functions for block operations
const createBlock = (content, parent = null) => ({ id: uuid(), content, parent, children: [] })
const updateBlock = (block, newContent) => ({ ...block, content: newContent })
const moveBlock = (block, newParent) => ({ ...block, parent: newParent })
```

### 2. Editor System (`src/main/frontend/components/editor.cljs`)
**What it does:**
- Text editing with markdown support
- Auto-completion and shortcuts
- Link creation and navigation

**Key Functions to Extract:**
```clojure
- parse-markdown
- render-block
- handle-keystroke
- auto-complete
```

**Functional Redesign:**
```javascript
// Pure parsing and rendering
const parseMarkdown = (text) => parseAST(text)
const renderBlock = (block) => htmlFromAST(block.ast)
const handleKeyStroke = (key, state) => updateState(key, state)
```

### 3. Database Layer (`src/main/frontend/db/`)
**What it does:**
- Stores blocks and relationships as facts
- Supports queries for linked content
- Reactive updates to UI

**Key Functions to Extract:**
```clojure
- add-block
- query-blocks
- get-block-refs
- update-graph
```

**Functional Redesign:**
```javascript
// Simple graph database
const addBlock = (db, block) => ({ ...db, blocks: { ...db.blocks, [block.id]: block } })
const queryBlocks = (db, predicate) => Object.values(db.blocks).filter(predicate)
const getReferences = (db, blockId) => queryBlocks(db, b => b.content.includes(`[[${blockId}]]`))
```

### 4. State Management (`src/main/frontend/state.cljs`)
**What it does:**
- Global application state
- Current page, editing state, UI state
- Reactive subscriptions

**Key State to Extract:**
```clojure
- current-page
- editing-block
- graph-data
- ui-state
```

**Functional Redesign:**
```javascript
// Simple state reducer
const initialState = { currentPage: null, editingBlock: null, graph: { blocks: {} } }
const reducer = (state, action) => {
  switch (action.type) {
    case 'SET_CURRENT_PAGE': return { ...state, currentPage: action.payload }
    case 'UPDATE_BLOCK': return { ...state, graph: updateBlock(state.graph, action.payload) }
    default: return state
  }
}
```

## 🔧 Supporting Utilities

### Text Processing (`src/main/frontend/util/text.cljs`)
**Essential Functions:**
- `normalize-text`: Clean and format text input
- `extract-links`: Find `[[page]]` style links  
- `parse-properties`: Extract block properties

### Database Queries (`src/main/frontend/util/datalog.cljc`)
**Essential Functions:**
- `q`: Simple query interface
- `pull`: Get entity with relationships
- `find-refs`: Find all references to a block

### File Parsing (`deps/graph-parser/`)
**Essential Functions:**
- `parse-file`: Convert markdown/org to blocks
- `extract-metadata`: Get page properties
- `build-graph`: Create block hierarchy

## 📦 Minimal Component Set

### Must Have (Core 10%)
1. **Block Editor**: Create, edit, move blocks
2. **Simple Parser**: Basic markdown to blocks
3. **Link System**: `[[page]]` style linking
4. **Local Storage**: Save/load graph data
5. **Basic UI**: Simple list view of blocks

### Nice to Have (Extended 20%)
1. **Search**: Find blocks by content
2. **Graph View**: Visual representation of links
3. **Export**: Basic markdown export
4. **Themes**: Simple CSS theming
5. **Shortcuts**: Keyboard navigation

### Don't Need (Complex 70%)
1. **Plugin System**: Too complex
2. **File Sync**: Collaboration features
3. **PDF Annotation**: Advanced features
4. **Whiteboards**: Visual tools
5. **Advanced Export**: Multiple formats
6. **Mobile Apps**: Cross-platform
7. **Desktop App**: Electron wrapper
8. **Advanced Queries**: Complex search

## 🏗️ Implementation Strategy

### Phase 1: Pure Functions
```javascript
// Start with pure data transformations
const blockOperations = {
  create: (content) => createBlock(content),
  update: (block, content) => updateBlock(block, content),
  move: (block, parent) => moveBlock(block, parent)
}

const graphOperations = {
  add: (graph, block) => addBlock(graph, block),
  query: (graph, predicate) => queryBlocks(graph, predicate),
  refs: (graph, blockId) => getReferences(graph, blockId)
}
```

### Phase 2: Simple UI
```javascript
// Pure render functions
const renderBlock = (block) => `<div class="block">${block.content}</div>`
const renderPage = (blocks) => blocks.map(renderBlock).join('')
const renderApp = (state) => `<div class="app">${renderPage(state.currentBlocks)}</div>`
```

### Phase 3: Event Handling
```javascript
// Pure event handlers
const handleBlockEdit = (blockId, newContent, state) => 
  reducer(state, { type: 'UPDATE_BLOCK', payload: { id: blockId, content: newContent } })

const handlePageNav = (pageId, state) =>
  reducer(state, { type: 'SET_CURRENT_PAGE', payload: pageId })
```