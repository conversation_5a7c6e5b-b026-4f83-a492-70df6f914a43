# Logseq Architecture Analysis

## 🏗️ Core Technology Stack

### Frontend
- **Language**: ClojureScript (compiled to JavaScript)
- **UI Framework**: Rum (React wrapper for ClojureScript)
- **Build System**: Shadow-CLJS + Gulp + Yarn
- **CSS**: Tailwind CSS with custom extensions
- **Database**: DataScript (in-memory Datalog database)

### Key Dependencies
```clojure
- rum/rum (React wrapper)
- datascript/datascript (graph database)
- thheller/shadow-cljs (build tool)
- metosin/malli (schema validation)
- org.babashka/sci (embedded Clojure interpreter)
```

## 📁 Directory Structure (Essential Parts)

```
src/main/frontend/
├── core.cljs                 # App entry point
├── page.cljs                 # Root component
├── state.cljs                # Global state management
├── components/
│   ├── block.cljs           # Core block editor
│   ├── editor.cljs          # Text editing functionality  
│   ├── container.cljs       # Main layout container
│   └── header.cljs          # App header/navigation
├── db/
│   ├── model.cljs           # Database queries
│   ├── transact.cljs        # Database transactions
│   └── react.cljs           # Reactive database subscriptions
├── handler/
│   ├── editor.cljs          # Editor event handlers
│   ├── block.cljs           # Block manipulation
│   └── page.cljs            # Page operations
└── util/
    ├── text.cljs            # Text processing utilities
    └── datalog.cljc         # Database query helpers
```

## 🔄 Data Flow Architecture

### 1. State Management Pattern
```
User Input → Handler Functions → Database Transaction → State Update → UI Re-render
```

### 2. Component Hierarchy
```
App (page.cljs)
└── Container (container.cljs)
    ├── Header (header.cljs)
    ├── Main Content
    │   ├── Block Editor (block.cljs)
    │   └── Page Content (content.cljs)
    └── Right Sidebar (right-sidebar.cljs)
```

### 3. Database Layer
- **DataScript**: In-memory Datalog database
- **Reactive Queries**: Components subscribe to database changes
- **Entity-Attribute-Value**: Everything stored as facts/triples

## 🧩 Core Concepts for Functional Version

### Essential Components (Keep)
1. **Block System**: Hierarchical text blocks
2. **Editor**: Simple text editing with markdown
3. **Database**: Lightweight graph storage
4. **State**: Minimal reactive state management
5. **Router**: Simple page navigation

### Complex Components (Strip)
1. **Plugin System**: Huge complexity, not needed
2. **File Sync**: Complex collaboration features
3. **PDF/Media**: Advanced annotation features
4. **Whiteboards**: Drawing/visual tools
5. **Advanced Export**: Multiple format support

## 📝 Key Files Analyzed

### Entry Points
- `static/index.html`: HTML shell with script loading
- `src/main/frontend/core.cljs`: ClojureScript entry point
- `src/main/frontend/page.cljs`: Root React component

### Core Editor
- `src/main/frontend/components/block.cljs`: Block editing logic
- `src/main/frontend/components/editor.cljs`: Text editor implementation
- `src/main/frontend/handler/editor.cljs`: Editor event handling

### Database
- `src/main/frontend/db/model.cljs`: Database queries and models
- `src/main/frontend/db/transact.cljs`: Transaction handling
- `deps/graph-parser/`: Markdown/Org parsing

## 🎯 Functional Redesign Insights

### Pure Function Opportunities
1. **Text Processing**: All markdown parsing can be pure functions
2. **Data Transformations**: Block manipulation as pure transformations
3. **Query Functions**: Database queries as pure functions
4. **Rendering**: UI components as pure functions of state

### State Management Simplification
- Replace complex reactive system with simple state atom
- Use pure reducer functions for state updates
- Minimize side effects to I/O boundaries only

### Component Simplification
- Extract core editing logic from UI framework dependencies
- Create pure render functions
- Separate business logic from presentation logic