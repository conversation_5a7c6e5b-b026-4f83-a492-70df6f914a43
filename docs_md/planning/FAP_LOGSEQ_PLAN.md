# FAP-Compliant Functional Logseq Implementation Plan

**Integrating Freedom Application Platform (FAP) principles with functional Logseq redesign**

---

## 🎯 FAP + Functional Programming Principles

### Core Philosophy
- **HTML-first**: Semantic structure using `<fap-*>` custom tags
- **Functional JavaScript**: Pure functions, closures for state, no classes
- **CSS component scoping**: Tag selectors, named colors only
- **No frameworks**: Direct browser execution, no build system
- **Immutable data**: Never modify data in place
- **Side effects isolated**: Only at I/O boundaries

---

## 🏗️ HTML Structure (Semantic-First Design)

### Main Application Layout
```html
<!DOCTYPE html>
<html>
<head>
  <title>Logseq FAP</title>
  <link rel="stylesheet" href="fap-logseq.css">
</head>
<body>
  <fap-logseq>
    <fap-header>
      <fap-logo>Logseq</fap-logo>
      <fap-search-trigger>🔍</fap-search-trigger>
      <fap-theme-toggle>🌙</fap-theme-toggle>
    </fap-header>

    <fap-sidebar>
      <fap-navigation>
        <fap-nav-item data-target="journals">📖 Journals</fap-nav-item>
        <fap-nav-item data-target="pages">📄 Pages</fap-nav-item>
        <fap-nav-item data-target="graph">🕸️ Graph</fap-nav-item>
      </fap-navigation>
      
      <fap-page-list>
        <!-- Dynamically populated -->
      </fap-page-list>
    </fap-sidebar>

    <fap-main>
      <fap-journal-view class="active">
        <fap-date-header>Jul 26th, 2025</fap-date-header>
        <fap-block-container>
          <!-- Blocks rendered here -->
        </fap-block-container>
      </fap-journal-view>

      <fap-page-view class="hidden">
        <fap-page-title contenteditable></fap-page-title>
        <fap-block-container>
          <!-- Page blocks here -->
        </fap-block-container>
      </fap-page-view>

      <fap-graph-view class="hidden">
        <canvas></canvas>
        <fap-graph-controls>
          <fap-zoom-control></fap-zoom-control>
        </fap-graph-controls>
      </fap-graph-view>
    </fap-main>

    <fap-search-modal class="hidden">
      <fap-search-input placeholder="What are you looking for?"></fap-search-input>
      <fap-search-results></fap-search-results>
    </fap-search-modal>
  </fap-logseq>

  <script src="fap-logseq.js"></script>
</body>
</html>
```

### Block Structure (Core Component)
```html
<fap-block data-block-id="block-123">
  <fap-block-bullet>•</fap-block-bullet>
  <fap-block-content contenteditable>
    This is a block with [[linked page]] content
  </fap-block-content>
  <fap-block-children>
    <!-- Nested blocks here -->
  </fap-block-children>
</fap-block>
```

---

## 🧩 Functional State Management (FAP Pattern)

### Closure-Based State
```javascript
// Core state creation utility (from FAP guide)
function createState(initial) {
  let value = initial
  const listeners = new Set()

  return {
    get: () => value,
    set: next => {
      value = next
      listeners.forEach(fn => fn(value))
    },
    update: fn => {
      value = fn(value)
      listeners.forEach(fn => fn(value))
    },
    subscribe: fn => {
      listeners.add(fn)
      fn(value)
      return () => listeners.delete(fn)
    }
  }
}

// Application state
const graphState = createState({
  blocks: {},
  pages: {},
  links: {},
  metadata: { version: '1.0.0' }
})

const uiState = createState({
  currentView: 'journals',
  currentPage: null,
  editingBlock: null,
  searchOpen: false
})
```

### Pure Data Structures
```javascript
// Immutable block creation
const createBlock = (content = '', parent = null) => ({
  id: `block-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
  content,
  parent,
  children: [],
  created: new Date().toISOString(),
  modified: new Date().toISOString()
})

// Immutable block updates
const updateBlockContent = (block, newContent) => ({
  ...block,
  content: newContent,
  modified: new Date().toISOString()
})

// Pure graph operations
const addBlockToGraph = (graph, block) => ({
  ...graph,
  blocks: {
    ...graph.blocks,
    [block.id]: block
  }
})

const removeBlockFromGraph = (graph, blockId) => {
  const { [blockId]: removed, ...remainingBlocks } = graph.blocks
  return {
    ...graph,
    blocks: remainingBlocks
  }
}
```

---

## 🎨 CSS Styling (FAP Component Approach)

### Theme Variables
```css
:root {
  --accent: cornflowerblue;
  --bg: darkslategray;
  --fg: white;
  --sidebar-bg: slategray;
  --block-bg: dimgray;
}

:root.light {
  --accent: steelblue;
  --bg: white;
  --fg: black;
  --sidebar-bg: lightgray;
  --block-bg: gainsboro;
}
```

### Component Styling
```css
/* Main layout */
fap-logseq {
  background: var(--bg);
  bottom: 0;
  color: var(--fg);
  display: flex;
  font-family: system-ui, sans-serif;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
}

fap-sidebar {
  background: var(--sidebar-bg);
  border-right: 1px solid gray;
  flex: 0 0 250px;
  overflow-y: auto;
  padding: 1rem;
}

fap-main {
  flex: 1;
  overflow: hidden;
  position: relative;
}

/* Block components */
fap-block {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 0.25rem;
  min-height: 1.5rem;
  padding: 0.25rem;
}

fap-block:hover {
  background: var(--block-bg);
}

fap-block-bullet {
  align-items: center;
  color: var(--accent);
  display: flex;
  flex-shrink: 0;
  font-weight: bold;
  width: 1rem;
}

fap-block-content {
  flex: 1;
  outline: none;
}

fap-block-content:focus {
  background: var(--block-bg);
  border-radius: 2px;
}

fap-block-children {
  border-left: 1px solid gray;
  margin-left: 1rem;
  padding-left: 1rem;
}
```

---

## ⚙️ Enhancement Functions (FAP Pattern)

### Block Editor Enhancement
```javascript
function enhanceBlockEditor(container) {
  // Find all blocks in container
  const blocks = container.querySelectorAll('fap-block')
  
  blocks.forEach(blockEl => {
    const content = blockEl.querySelector('fap-block-content')
    const blockId = blockEl.dataset.blockId
    
    // Handle content editing
    content?.addEventListener('input', () => {
      const newContent = content.textContent
      graphState.update(graph => 
        updateBlockInGraph(graph, blockId, newContent)
      )
      // Auto-save after 1 second of no typing
      clearTimeout(content.saveTimeout)
      content.saveTimeout = setTimeout(() => {
        window.fap.storage.saveGraph(graphState.get())
      }, 1000)
    })
    
    // Handle enter key for new blocks
    content?.addEventListener('keydown', (e) => {
      if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault()
        const newBlock = createBlock('', blockId)
        graphState.update(graph => addBlockToGraph(graph, newBlock))
        // Re-render and focus new block
        renderBlockContainer(container)
      }
    })
  })
}
```

### Navigation Enhancement
```javascript
function enhanceNavigation(nav) {
  const navItems = nav.querySelectorAll('fap-nav-item')
  
  navItems.forEach(item => {
    item.addEventListener('click', () => {
      const target = item.dataset.target
      uiState.update(ui => ({ ...ui, currentView: target }))
      
      // Update active states
      navItems.forEach(i => i.classList.remove('active'))
      item.classList.add('active')
      
      // Show/hide views
      document.querySelectorAll('fap-main > *').forEach(view => {
        view.classList.add('hidden')
      })
      document.querySelector(`fap-${target}-view`)?.classList.remove('hidden')
    })
  })
}
```

### Search Enhancement
```javascript
function enhanceSearch(searchModal) {
  const input = searchModal.querySelector('fap-search-input')
  const results = searchModal.querySelector('fap-search-results')
  
  input?.addEventListener('input', (e) => {
    const query = e.target.value.toLowerCase()
    if (query.length < 2) {
      results.innerHTML = ''
      return
    }
    
    const graph = graphState.get()
    const matchingBlocks = Object.values(graph.blocks)
      .filter(block => block.content.toLowerCase().includes(query))
      .slice(0, 10) // Limit results
    
    results.innerHTML = matchingBlocks
      .map(block => `
        <fap-search-result data-block-id="${block.id}">
          <fap-result-content>${highlightQuery(block.content, query)}</fap-result-content>
        </fap-search-result>
      `).join('')
    
    // Enhance result clicks
    results.querySelectorAll('fap-search-result').forEach(result => {
      result.addEventListener('click', () => {
        const blockId = result.dataset.blockId
        // Navigate to block
        window.fap.actions.navigateToBlock(blockId)
        uiState.update(ui => ({ ...ui, searchOpen: false }))
      })
    })
  })
}
```

---

## 🪟 Window.fap Namespace

```javascript
window.fap = {
  // Core state
  state: {
    graph: graphState,
    ui: uiState
  },
  
  // Enhancement functions
  enhance: {
    blockEditor: enhanceBlockEditor,
    navigation: enhanceNavigation,
    search: enhanceSearch,
    graphView: enhanceGraphView
  },
  
  // Pure utility functions
  util: {
    createBlock,
    updateBlockContent,
    parseMarkdown: (text) => {
      // Simple markdown parsing
      return text
        .replace(/\[\[([^\]]+)\]\]/g, '<fap-link data-page="$1">$1</fap-link>')
        .replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>')
        .replace(/\*([^*]+)\*/g, '<em>$1</em>')
    },
    
    extractLinks: (text) => {
      const links = []
      const linkRegex = /\[\[([^\]]+)\]\]/g
      let match
      while ((match = linkRegex.exec(text)) !== null) {
        links.push(match[1])
      }
      return links
    },
    
    generateId: () => `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  },
  
  // Actions (side effects isolated)
  actions: {
    navigateToBlock: (blockId) => {
      const block = graphState.get().blocks[blockId]
      if (block) {
        // Find and focus the block element
        const blockEl = document.querySelector(`[data-block-id="${blockId}"]`)
        blockEl?.scrollIntoView({ behavior: 'smooth' })
        blockEl?.querySelector('fap-block-content')?.focus()
      }
    },
    
    toggleTheme: () => {
      document.documentElement.classList.toggle('light')
      const isLight = document.documentElement.classList.contains('light')
      const toggle = document.querySelector('fap-theme-toggle')
      toggle.textContent = isLight ? '☀️' : '🌙'
    },
    
    toggleSearch: () => {
      uiState.update(ui => ({ ...ui, searchOpen: !ui.searchOpen }))
      const modal = document.querySelector('fap-search-modal')
      modal?.classList.toggle('hidden')
      if (!modal?.classList.contains('hidden')) {
        modal?.querySelector('fap-search-input')?.focus()
      }
    }
  },
  
  // Storage (isolated side effects)
  storage: {
    saveGraph: (graph) => {
      try {
        localStorage.setItem('fap-logseq-graph', JSON.stringify(graph))
        return { success: true }
      } catch (error) {
        console.error('Save failed:', error)
        return { success: false, error: error.message }
      }
    },
    
    loadGraph: () => {
      try {
        const data = localStorage.getItem('fap-logseq-graph')
        return data ? JSON.parse(data) : createEmptyGraph()
      } catch (error) {
        console.error('Load failed:', error)
        return createEmptyGraph()
      }
    }
  }
}
```

---

## 🚀 Implementation Strategy

### Phase 1: HTML Foundation (Week 1)
1. Create semantic HTML structure with `<fap-*>` tags
2. Set up basic CSS with component scoping
3. Implement theme switching
4. Create empty enhancement functions

### Phase 2: Core Functionality (Week 2)
1. Implement closure-based state management
2. Create pure block manipulation functions
3. Add block editing enhancement
4. Implement basic navigation

### Phase 3: Advanced Features (Week 3)
1. Add search functionality
2. Implement graph view with canvas
3. Add link parsing and navigation
4. Implement storage persistence

### Phase 4: Polish (Week 4)
1. Improve CSS styling and animations
2. Add keyboard shortcuts
3. Optimize performance
4. Add export/import features

---

## ✅ Benefits of FAP + Functional Approach

### Development Benefits
- **Readable HTML**: Custom tags make structure self-documenting
- **Debuggable**: DevTools show clear component boundaries
- **Testable**: Pure functions are easy to test
- **Maintainable**: No complex framework dependencies

### Performance Benefits
- **Fast Loading**: No framework overhead
- **Instant Updates**: Direct DOM manipulation
- **Small Bundle**: ~50KB total (vs 20MB+ for original)

### User Benefits
- **Reliable**: No framework bugs or breaking changes
- **Portable**: Works in any modern browser
- **Fast**: Instant startup and response

This FAP-compliant approach transforms Logseq from a complex ClojureScript application into a simple, semantic, functional web application that's easy to understand, modify, and extend.