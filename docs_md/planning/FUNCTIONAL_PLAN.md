# Functional Programming Plan for Logseq-Lite

## 🎯 Core Principles

### Pure Functions Only
- **No Side Effects**: Functions only transform input to output
- **Immutable Data**: Never modify data in place
- **Predictable**: Same input always produces same output
- **Testable**: Easy to test in isolation

### Simple and Generic
- **Small Functions**: Each function does one thing well
- **Composable**: Functions can be combined easily
- **Generic**: Reusable across different contexts
- **Readable**: Self-documenting code

## 📋 Minimal Feature Set

### Core Features (Must Have)
1. **Create/Edit Blocks**: Basic text editing
2. **Block Hierarchy**: Indent/outdent operations
3. **Simple Links**: `[[page name]]` style linking
4. **Local Storage**: Save/load data in browser
5. **Basic Navigation**: Switch between pages

### Extended Features (Nice to Have)
1. **Search**: Find blocks by content
2. **Export**: Save as markdown
3. **Import**: Load markdown files
4. **Themes**: Basic CSS theming

## 🏗️ Architecture Design

### Data Structures (Pure/Immutable)
```javascript
// Block: Core data unit
const Block = {
  id: String,           // Unique identifier
  content: String,      // Text content
  parent: String|null,  // Parent block ID
  children: [String],   // Child block IDs
  created: Date,        // Creation timestamp
  modified: Date        // Last modification
}

// Page: Collection of blocks
const Page = {
  id: String,           // Page identifier  
  title: String,        // Page title
  blocks: [String],     // Root block IDs
  created: Date,
  modified: Date
}

// Graph: Complete data structure
const Graph = {
  blocks: {[id]: Block},    // All blocks by ID
  pages: {[id]: Page},      // All pages by ID
  links: {[from]: [to]},    // Link relationships
  metadata: {               // App metadata
    version: String,
    created: Date,
    modified: Date
  }
}
```

### Core Functions (Pure)

#### Block Operations
```javascript
// Create new block
const createBlock = (content = '', parent = null) => ({
  id: generateId(),
  content,
  parent,
  children: [],
  created: new Date(),
  modified: new Date()
})

// Update block content
const updateBlock = (block, newContent) => ({
  ...block,
  content: newContent,
  modified: new Date()
})

// Move block to new parent
const moveBlock = (block, newParent) => ({
  ...block,
  parent: newParent,
  modified: new Date()
})

// Add child to block
const addChild = (block, childId) => ({
  ...block,
  children: [...block.children, childId],
  modified: new Date()
})
```

#### Graph Operations
```javascript
// Add block to graph
const addBlock = (graph, block) => ({
  ...graph,
  blocks: {
    ...graph.blocks,
    [block.id]: block
  }
})

// Update block in graph
const updateBlockInGraph = (graph, blockId, newContent) => {
  const block = graph.blocks[blockId]
  if (!block) return graph
  
  return {
    ...graph,
    blocks: {
      ...graph.blocks,
      [blockId]: updateBlock(block, newContent)
    }
  }
}

// Query blocks by predicate
const queryBlocks = (graph, predicate) =>
  Object.values(graph.blocks).filter(predicate)

// Find all references to a block/page
const findReferences = (graph, target) =>
  queryBlocks(graph, block => 
    block.content.includes(`[[${target}]]`)
  )
```

#### Parsing Functions
```javascript
// Parse markdown to blocks
const parseMarkdown = (text) => {
  const lines = text.split('\n')
  return lines.map(line => {
    const indent = getIndentLevel(line)
    const content = line.trim()
    return { content, indent }
  })
}

// Extract links from text
const extractLinks = (text) => {
  const linkRegex = /\[\[([^\]]+)\]\]/g
  const links = []
  let match
  while ((match = linkRegex.exec(text)) !== null) {
    links.push(match[1])
  }
  return links
}

// Convert blocks to markdown
const blocksToMarkdown = (blocks) =>
  blocks.map(block => 
    '  '.repeat(block.indent || 0) + '- ' + block.content
  ).join('\n')
```

### UI Functions (Pure)

#### Rendering Functions
```javascript
// Render single block
const renderBlock = (block, isEditing = false) => {
  if (isEditing) {
    return `<input type="text" value="${block.content}" data-block-id="${block.id}" />`
  }
  
  const processedContent = processLinks(block.content)
  return `<div class="block" data-block-id="${block.id}">${processedContent}</div>`
}

// Render page
const renderPage = (graph, pageId) => {
  const page = graph.pages[pageId]
  if (!page) return '<div>Page not found</div>'
  
  const blocks = page.blocks.map(blockId => graph.blocks[blockId])
  const blockHtml = blocks.map(block => renderBlock(block)).join('')
  
  return `
    <div class="page">
      <h1>${page.title}</h1>
      <div class="blocks">${blockHtml}</div>
    </div>
  `
}

// Render entire app
const renderApp = (graph, currentPageId, editingBlockId = null) => `
  <div class="app">
    <nav class="sidebar">
      ${renderPageList(graph)}
    </nav>
    <main class="content">
      ${renderPage(graph, currentPageId)}
    </main>
  </div>
`
```

#### Event Handling (Pure)
```javascript
// Handle block edit
const handleBlockEdit = (graph, blockId, newContent) =>
  updateBlockInGraph(graph, blockId, newContent)

// Handle new block creation
const handleNewBlock = (graph, parentId = null) => {
  const newBlock = createBlock('', parentId)
  return addBlock(graph, newBlock)
}

// Handle block deletion
const handleDeleteBlock = (graph, blockId) => {
  const { [blockId]: deleted, ...remainingBlocks } = graph.blocks
  return {
    ...graph,
    blocks: remainingBlocks
  }
}
```

### Storage Functions (Side Effects Isolated)
```javascript
// Save graph to localStorage (ONLY side effect function)
const saveGraph = (graph) => {
  try {
    localStorage.setItem('logseq-graph', JSON.stringify(graph))
    return { success: true }
  } catch (error) {
    return { success: false, error: error.message }
  }
}

// Load graph from localStorage (ONLY side effect function)
const loadGraph = () => {
  try {
    const data = localStorage.getItem('logseq-graph')
    return data ? JSON.parse(data) : createEmptyGraph()
  } catch (error) {
    return createEmptyGraph()
  }
}

// Create empty graph
const createEmptyGraph = () => ({
  blocks: {},
  pages: {},
  links: {},
  metadata: {
    version: '1.0.0',
    created: new Date(),
    modified: new Date()
  }
})
```

## 📁 File Structure

```
functional-logseq/
├── index.html                 # Single HTML file
├── app.js                     # Main application entry
├── core/
│   ├── block.js              # Block operations
│   ├── graph.js              # Graph operations  
│   ├── page.js               # Page operations
│   └── parser.js             # Text parsing
├── ui/
│   ├── render.js             # Pure render functions
│   ├── events.js             # Event handlers
│   └── components.js         # UI components
├── utils/
│   ├── storage.js            # Storage functions
│   ├── helpers.js            # Generic utilities
│   └── constants.js          # App constants
└── style.css                 # Simple CSS
```

## 🚀 Implementation Strategy

### Phase 1: Core Functions (Week 1)
1. Implement basic data structures
2. Create block operations (create, update, move)
3. Implement graph operations (add, query, update)
4. Add simple parsing functions
5. Write comprehensive tests

### Phase 2: UI Layer (Week 2)
1. Create pure render functions
2. Implement event handling
3. Add basic navigation
4. Style with simple CSS
5. Test user interactions

### Phase 3: Storage & Polish (Week 3)
1. Add localStorage persistence
2. Implement import/export
3. Add search functionality
4. Polish UI and UX
5. Final testing and optimization

## ✅ Benefits of This Approach

### Development Benefits
- **Easy Testing**: Pure functions are trivial to test
- **Easy Debugging**: No hidden state or side effects
- **Easy Refactoring**: Functions can be moved/renamed safely
- **Easy Understanding**: Code is self-documenting

### Performance Benefits
- **Fast Loading**: No framework overhead
- **Small Bundle**: <100KB total size
- **Instant Startup**: No compilation or bundling
- **Efficient Updates**: Only change what's needed

### Maintenance Benefits
- **No Dependencies**: Won't break with updates
- **Simple Deployment**: Just copy files anywhere
- **Future Proof**: Uses only standard web APIs
- **Educational**: Great for learning functional programming