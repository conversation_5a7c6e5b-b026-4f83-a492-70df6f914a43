"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var chromiumSwitches_exports = {};
__export(chromiumSwitches_exports, {
  chromiumSwitches: () => chromiumSwitches
});
module.exports = __toCommonJS(chromiumSwitches_exports);
const disabledFeatures = (assistantMode) => [
  // See https://github.com/microsoft/playwright/pull/10380
  "AcceptCHFrame",
  // See https://github.com/microsoft/playwright/pull/10679
  "AutoExpandDetailsElement",
  // See https://github.com/microsoft/playwright/issues/14047
  "AvoidUnnecessaryBeforeUnloadCheckSync",
  // See https://github.com/microsoft/playwright/pull/12992
  "CertificateTransparencyComponentUpdater",
  "DestroyProfileOnBrowserClose",
  // See https://github.com/microsoft/playwright/pull/13854
  "DialMediaRouteProvider",
  // Chromium is disabling manifest version 2. Allow testing it as long as Chromium can actually run it.
  // Disabled in https://chromium-review.googlesource.com/c/chromium/src/+/6265903.
  "ExtensionManifestV2Disabled",
  "GlobalMediaControls",
  // See https://github.com/microsoft/playwright/pull/27605
  "HttpsUpgrades",
  "ImprovedCookieControls",
  "LazyFrameLoading",
  // Hides the Lens feature in the URL address bar. Its not working in unofficial builds.
  "LensOverlay",
  // See https://github.com/microsoft/playwright/pull/8162
  "MediaRouter",
  // See https://github.com/microsoft/playwright/issues/28023
  "PaintHolding",
  // See https://github.com/microsoft/playwright/issues/32230
  "ThirdPartyStoragePartitioning",
  // See https://github.com/microsoft/playwright/issues/16126
  "Translate",
  assistantMode ? "AutomationControlled" : ""
].filter(Boolean);
const chromiumSwitches = (assistantMode, channel) => [
  "--disable-field-trial-config",
  // https://source.chromium.org/chromium/chromium/src/+/main:testing/variations/README.md
  "--disable-background-networking",
  "--disable-background-timer-throttling",
  "--disable-backgrounding-occluded-windows",
  "--disable-back-forward-cache",
  // Avoids surprises like main request not being intercepted during page.goBack().
  "--disable-breakpad",
  "--disable-client-side-phishing-detection",
  "--disable-component-extensions-with-background-pages",
  "--disable-component-update",
  // Avoids unneeded network activity after startup.
  "--no-default-browser-check",
  "--disable-default-apps",
  "--disable-dev-shm-usage",
  "--disable-extensions",
  "--disable-features=" + disabledFeatures(assistantMode).join(","),
  channel === "chromium-tip-of-tree" ? "--enable-features=CDPScreenshotNewSurface" : "",
  "--allow-pre-commit-input",
  "--disable-hang-monitor",
  "--disable-ipc-flooding-protection",
  "--disable-popup-blocking",
  "--disable-prompt-on-repost",
  "--disable-renderer-backgrounding",
  "--force-color-profile=srgb",
  "--metrics-recording-only",
  "--no-first-run",
  "--password-store=basic",
  "--use-mock-keychain",
  // See https://chromium-review.googlesource.com/c/chromium/src/+/2436773
  "--no-service-autorun",
  "--export-tagged-pdf",
  // https://chromium-review.googlesource.com/c/chromium/src/+/4853540
  "--disable-search-engine-choice-screen",
  // https://issues.chromium.org/41491762
  "--unsafely-disable-devtools-self-xss-warnings",
  // Edge can potentially restart on Windows (msRelaunchNoCompatLayer) which looses its file descriptors (stdout/stderr) and CDP (3/4). Disable until fixed upstream.
  "--edge-skip-compat-layer-relaunch",
  assistantMode ? "" : "--enable-automation"
].filter(Boolean);
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  chromiumSwitches
});
