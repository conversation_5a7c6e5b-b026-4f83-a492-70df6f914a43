#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Volumes/Data/Software/2025/Clone_testing_to_functional/Loqseq-Augment/loqsec-functional/node_modules/.pnpm/@isomorphic-git+lightning-fs@4.6.2/node_modules/@isomorphic-git/lightning-fs/src/node_modules:/Volumes/Data/Software/2025/Clone_testing_to_functional/Loqseq-Augment/loqsec-functional/node_modules/.pnpm/@isomorphic-git+lightning-fs@4.6.2/node_modules/@isomorphic-git/lightning-fs/node_modules:/Volumes/Data/Software/2025/Clone_testing_to_functional/Loqseq-Augment/loqsec-functional/node_modules/.pnpm/@isomorphic-git+lightning-fs@4.6.2/node_modules/@isomorphic-git/node_modules:/Volumes/Data/Software/2025/Clone_testing_to_functional/Loqseq-Augment/loqsec-functional/node_modules/.pnpm/@isomorphic-git+lightning-fs@4.6.2/node_modules:/Volumes/Data/Software/2025/Clone_testing_to_functional/Loqseq-Augment/loqsec-functional/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Volumes/Data/Software/2025/Clone_testing_to_functional/Loqseq-Augment/loqsec-functional/node_modules/.pnpm/@isomorphic-git+lightning-fs@4.6.2/node_modules/@isomorphic-git/lightning-fs/src/node_modules:/Volumes/Data/Software/2025/Clone_testing_to_functional/Loqseq-Augment/loqsec-functional/node_modules/.pnpm/@isomorphic-git+lightning-fs@4.6.2/node_modules/@isomorphic-git/lightning-fs/node_modules:/Volumes/Data/Software/2025/Clone_testing_to_functional/Loqseq-Augment/loqsec-functional/node_modules/.pnpm/@isomorphic-git+lightning-fs@4.6.2/node_modules/@isomorphic-git/node_modules:/Volumes/Data/Software/2025/Clone_testing_to_functional/Loqseq-Augment/loqsec-functional/node_modules/.pnpm/@isomorphic-git+lightning-fs@4.6.2/node_modules:/Volumes/Data/Software/2025/Clone_testing_to_functional/Loqseq-Augment/loqsec-functional/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../@isomorphic-git/lightning-fs/src/superblocktxt.js" "$@"
else
  exec node  "$basedir/../@isomorphic-git/lightning-fs/src/superblocktxt.js" "$@"
fi
