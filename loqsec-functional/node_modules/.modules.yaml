hoistPattern:
  - '*'
hoistedDependencies:
  '@isomorphic-git/idb-keyval@3.3.2':
    '@isomorphic-git/idb-keyval': private
  '@napi-rs/canvas-darwin-x64@0.1.74':
    '@napi-rs/canvas-darwin-x64': private
  '@napi-rs/canvas@0.1.74':
    '@napi-rs/canvas': private
  '@types/trusted-types@2.0.7':
    '@types/trusted-types': private
  fast-text-encoding@1.0.6:
    fast-text-encoding: private
  fsevents@2.3.2:
    fsevents: private
  isomorphic-textencoder@1.0.1:
    isomorphic-textencoder: private
  just-debounce-it@1.1.0:
    just-debounce-it: private
  just-once@1.1.0:
    just-once: private
  playwright-core@1.54.1:
    playwright-core: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.13.1
pendingBuilds: []
prunedAt: Sun, 27 Jul 2025 23:40:08 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped:
  - '@napi-rs/canvas-android-arm64@0.1.74'
  - '@napi-rs/canvas-darwin-arm64@0.1.74'
  - '@napi-rs/canvas-linux-arm-gnueabihf@0.1.74'
  - '@napi-rs/canvas-linux-arm64-gnu@0.1.74'
  - '@napi-rs/canvas-linux-arm64-musl@0.1.74'
  - '@napi-rs/canvas-linux-riscv64-gnu@0.1.74'
  - '@napi-rs/canvas-linux-x64-gnu@0.1.74'
  - '@napi-rs/canvas-linux-x64-musl@0.1.74'
  - '@napi-rs/canvas-win32-x64-msvc@0.1.74'
storeDir: /Volumes/Data/.pnpm-store/v10
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
