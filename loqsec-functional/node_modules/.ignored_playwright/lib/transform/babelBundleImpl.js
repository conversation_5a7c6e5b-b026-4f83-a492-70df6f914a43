"use strict";var iB=Object.create;var kl=Object.defineProperty;var aB=Object.getOwnPropertyDescriptor;var oB=Object.getOwnPropertyNames;var lB=Object.getPrototypeOf,uB=Object.prototype.hasOwnProperty;var T=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),cB=(e,t)=>{for(var r in t)kl(e,r,{get:t[r],enumerable:!0})},LE=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let s of oB(t))!uB.call(e,s)&&s!==r&&kl(e,s,{get:()=>t[s],enumerable:!(n=aB(t,s))||n.enumerable});return e};var zs=(e,t,r)=>(r=e!=null?iB(lB(e)):{},LE(t||!e||!e.__esModule?kl(r,"default",{value:e,enumerable:!0}):r,e)),pB=e=>LE(kl({},"__esModule",{value:!0}),e);var Fl=T(Wf=>{"use strict";Object.defineProperty(Wf,"__esModule",{value:!0});Wf.default=fB;function fB(e,t){let r=Object.keys(t);for(let n of r)if(e[n]!==t[n])return!1;return!0}});var Ya=T(Kf=>{"use strict";Object.defineProperty(Kf,"__esModule",{value:!0});Kf.default=dB;var jE=new Set;function dB(e,t,r=""){if(jE.has(e))return;jE.add(e);let{internal:n,trace:s}=hB(1,2);n||console.warn(`${r}\`${e}\` has been deprecated, please migrate to \`${t}\`
${s}`)}function hB(e,t){let{stackTraceLimit:r,prepareStackTrace:n}=Error,s;if(Error.stackTraceLimit=1+e+t,Error.prepareStackTrace=function(a,o){s=o},new Error().stack,Error.stackTraceLimit=r,Error.prepareStackTrace=n,!s)return{internal:!1,trace:""};let i=s.slice(1+e,1+e+t);return{internal:/[\\/]@babel[\\/]/.test(i[1].getFileName()),trace:i.map(a=>`    at ${a}`).join(`
`)}}});var Nt=T(S=>{"use strict";Object.defineProperty(S,"__esModule",{value:!0});S.isAccessor=_8;S.isAnyTypeAnnotation=X3;S.isArgumentPlaceholder=PF;S.isArrayExpression=mB;S.isArrayPattern=d3;S.isArrayTypeAnnotation=J3;S.isArrowFunctionExpression=h3;S.isAssignmentExpression=yB;S.isAssignmentPattern=f3;S.isAwaitExpression=j3;S.isBigIntLiteral=R3;S.isBinary=ZL;S.isBinaryExpression=gB;S.isBindExpression=AF;S.isBlock=r8;S.isBlockParent=t8;S.isBlockStatement=SB;S.isBooleanLiteral=VB;S.isBooleanLiteralTypeAnnotation=Q3;S.isBooleanTypeAnnotation=z3;S.isBreakStatement=xB;S.isCallExpression=vB;S.isCatchClause=PB;S.isClass=D8;S.isClassAccessorProperty=W3;S.isClassBody=m3;S.isClassDeclaration=g3;S.isClassExpression=y3;S.isClassImplements=ek;S.isClassMethod=I3;S.isClassPrivateMethod=G3;S.isClassPrivateProperty=K3;S.isClassProperty=$3;S.isCompletionStatement=i8;S.isConditional=a8;S.isConditionalExpression=AB;S.isContinueStatement=CB;S.isDebuggerStatement=DB;S.isDecimalLiteral=NF;S.isDeclaration=m8;S.isDeclareClass=tk;S.isDeclareExportAllDeclaration=ck;S.isDeclareExportDeclaration=uk;S.isDeclareFunction=rk;S.isDeclareInterface=nk;S.isDeclareModule=sk;S.isDeclareModuleExports=ik;S.isDeclareOpaqueType=ok;S.isDeclareTypeAlias=ak;S.isDeclareVariable=lk;S.isDeclaredPredicate=pk;S.isDecorator=DF;S.isDirective=EB;S.isDirectiveLiteral=TB;S.isDoExpression=wF;S.isDoWhileStatement=wB;S.isEmptyStatement=IB;S.isEmptyTypeAnnotation=xk;S.isEnumBody=j8;S.isEnumBooleanBody=Jk;S.isEnumBooleanMember=eF;S.isEnumDeclaration=Xk;S.isEnumDefaultedMember=nF;S.isEnumMember=M8;S.isEnumNumberBody=zk;S.isEnumNumberMember=tF;S.isEnumStringBody=Qk;S.isEnumStringMember=rF;S.isEnumSymbolBody=Zk;S.isExistsTypeAnnotation=fk;S.isExportAllDeclaration=b3;S.isExportDeclaration=w8;S.isExportDefaultDeclaration=E3;S.isExportDefaultSpecifier=IF;S.isExportNamedDeclaration=T3;S.isExportNamespaceSpecifier=q3;S.isExportSpecifier=S3;S.isExpression=QL;S.isExpressionStatement=_B;S.isExpressionWrapper=u8;S.isFile=OB;S.isFlow=N8;S.isFlowBaseAnnotation=k8;S.isFlowDeclaration=F8;S.isFlowPredicate=L8;S.isFlowType=B8;S.isFor=c8;S.isForInStatement=NB;S.isForOfStatement=x3;S.isForStatement=BB;S.isForXStatement=p8;S.isFunction=f8;S.isFunctionDeclaration=kB;S.isFunctionExpression=FB;S.isFunctionParent=d8;S.isFunctionTypeAnnotation=dk;S.isFunctionTypeParam=hk;S.isGenericTypeAnnotation=mk;S.isIdentifier=LB;S.isIfStatement=jB;S.isImmutable=T8;S.isImport=M3;S.isImportAttribute=CF;S.isImportDeclaration=v3;S.isImportDefaultSpecifier=P3;S.isImportExpression=D3;S.isImportNamespaceSpecifier=A3;S.isImportOrExportDeclaration=ME;S.isImportSpecifier=C3;S.isIndexedAccessType=sF;S.isInferredPredicate=yk;S.isInterfaceDeclaration=bk;S.isInterfaceExtends=gk;S.isInterfaceTypeAnnotation=Ek;S.isInterpreterDirective=bB;S.isIntersectionTypeAnnotation=Tk;S.isJSX=R8;S.isJSXAttribute=aF;S.isJSXClosingElement=oF;S.isJSXClosingFragment=TF;S.isJSXElement=lF;S.isJSXEmptyExpression=uF;S.isJSXExpressionContainer=cF;S.isJSXFragment=bF;S.isJSXIdentifier=fF;S.isJSXMemberExpression=dF;S.isJSXNamespacedName=hF;S.isJSXOpeningElement=mF;S.isJSXOpeningFragment=EF;S.isJSXSpreadAttribute=yF;S.isJSXSpreadChild=pF;S.isJSXText=gF;S.isLVal=g8;S.isLabeledStatement=MB;S.isLiteral=E8;S.isLogicalExpression=WB;S.isLoop=o8;S.isMemberExpression=KB;S.isMetaProperty=w3;S.isMethod=x8;S.isMiscellaneous=q8;S.isMixedTypeAnnotation=Sk;S.isModuleDeclaration=X8;S.isModuleExpression=BF;S.isModuleSpecifier=I8;S.isNewExpression=GB;S.isNoop=SF;S.isNullLiteral=UB;S.isNullLiteralTypeAnnotation=Z3;S.isNullableTypeAnnotation=vk;S.isNumberLiteral=K8;S.isNumberLiteralTypeAnnotation=Pk;S.isNumberTypeAnnotation=Ak;S.isNumericLiteral=qB;S.isObjectExpression=YB;S.isObjectMember=v8;S.isObjectMethod=XB;S.isObjectPattern=_3;S.isObjectProperty=JB;S.isObjectTypeAnnotation=Ck;S.isObjectTypeCallProperty=wk;S.isObjectTypeIndexer=Ik;S.isObjectTypeInternalSlot=Dk;S.isObjectTypeProperty=_k;S.isObjectTypeSpreadProperty=Ok;S.isOpaqueType=Nk;S.isOptionalCallExpression=V3;S.isOptionalIndexedAccessType=iF;S.isOptionalMemberExpression=U3;S.isParenthesizedExpression=e3;S.isPattern=C8;S.isPatternLike=y8;S.isPipelineBareFunction=LF;S.isPipelinePrimaryTopicReference=jF;S.isPipelineTopicExpression=FF;S.isPlaceholder=xF;S.isPrivate=O8;S.isPrivateName=H3;S.isProgram=HB;S.isProperty=P8;S.isPureish=h8;S.isQualifiedTypeIdentifier=Bk;S.isRecordExpression=_F;S.isRegExpLiteral=$B;S.isRegexLiteral=G8;S.isRestElement=zB;S.isRestProperty=H8;S.isReturnStatement=QB;S.isScopable=e8;S.isSequenceExpression=ZB;S.isSpreadElement=O3;S.isSpreadProperty=Y8;S.isStandardized=zL;S.isStatement=n8;S.isStaticBlock=Y3;S.isStringLiteral=RB;S.isStringLiteralTypeAnnotation=kk;S.isStringTypeAnnotation=Fk;S.isSuper=N3;S.isSwitchCase=t3;S.isSwitchStatement=r3;S.isSymbolTypeAnnotation=Lk;S.isTSAnyKeyword=HF;S.isTSArrayType=dL;S.isTSAsExpression=BL;S.isTSBaseType=W8;S.isTSBigIntKeyword=XF;S.isTSBooleanKeyword=YF;S.isTSCallSignatureDeclaration=VF;S.isTSConditionalType=TL;S.isTSConstructSignatureDeclaration=$F;S.isTSConstructorType=lL;S.isTSDeclareFunction=RF;S.isTSDeclareMethod=qF;S.isTSEntityName=b8;S.isTSEnumBody=LL;S.isTSEnumDeclaration=jL;S.isTSEnumMember=ML;S.isTSExportAssignment=KL;S.isTSExpressionWithTypeArguments=wL;S.isTSExternalModuleReference=$L;S.isTSFunctionType=oL;S.isTSImportEqualsDeclaration=VL;S.isTSImportType=UL;S.isTSIndexSignature=GF;S.isTSIndexedAccessType=PL;S.isTSInferType=SL;S.isTSInstantiationExpression=NL;S.isTSInterfaceBody=_L;S.isTSInterfaceDeclaration=IL;S.isTSIntersectionType=EL;S.isTSIntrinsicKeyword=JF;S.isTSLiteralType=DL;S.isTSMappedType=AL;S.isTSMethodSignature=KF;S.isTSModuleBlock=qL;S.isTSModuleDeclaration=RL;S.isTSNamedTupleMember=gL;S.isTSNamespaceExportDeclaration=GL;S.isTSNeverKeyword=zF;S.isTSNonNullExpression=WL;S.isTSNullKeyword=QF;S.isTSNumberKeyword=ZF;S.isTSObjectKeyword=eL;S.isTSOptionalType=mL;S.isTSParameterProperty=MF;S.isTSParenthesizedType=xL;S.isTSPropertySignature=WF;S.isTSQualifiedName=UF;S.isTSRestType=yL;S.isTSSatisfiesExpression=kL;S.isTSStringKeyword=tL;S.isTSSymbolKeyword=rL;S.isTSTemplateLiteralType=CL;S.isTSThisType=aL;S.isTSTupleType=hL;S.isTSType=$8;S.isTSTypeAliasDeclaration=OL;S.isTSTypeAnnotation=HL;S.isTSTypeAssertion=FL;S.isTSTypeElement=V8;S.isTSTypeLiteral=fL;S.isTSTypeOperator=vL;S.isTSTypeParameter=JL;S.isTSTypeParameterDeclaration=XL;S.isTSTypeParameterInstantiation=YL;S.isTSTypePredicate=cL;S.isTSTypeQuery=pL;S.isTSTypeReference=uL;S.isTSUndefinedKeyword=nL;S.isTSUnionType=bL;S.isTSUnknownKeyword=sL;S.isTSVoidKeyword=iL;S.isTaggedTemplateExpression=B3;S.isTemplateElement=k3;S.isTemplateLiteral=F3;S.isTerminatorless=s8;S.isThisExpression=n3;S.isThisTypeAnnotation=jk;S.isThrowStatement=s3;S.isTopicReference=kF;S.isTryStatement=i3;S.isTupleExpression=OF;S.isTupleTypeAnnotation=Mk;S.isTypeAlias=qk;S.isTypeAnnotation=Uk;S.isTypeCastExpression=Vk;S.isTypeParameter=$k;S.isTypeParameterDeclaration=Wk;S.isTypeParameterInstantiation=Kk;S.isTypeScript=U8;S.isTypeofTypeAnnotation=Rk;S.isUnaryExpression=a3;S.isUnaryLike=A8;S.isUnionTypeAnnotation=Gk;S.isUpdateExpression=o3;S.isUserWhitespacable=S8;S.isV8IntrinsicIdentifier=vF;S.isVariableDeclaration=l3;S.isVariableDeclarator=u3;S.isVariance=Hk;S.isVoidTypeAnnotation=Yk;S.isWhile=l8;S.isWhileStatement=c3;S.isWithStatement=p3;S.isYieldExpression=L3;var P=Fl(),Xa=Ya();function mB(e,t){return!e||e.type!=="ArrayExpression"?!1:t==null||(0,P.default)(e,t)}function yB(e,t){return!e||e.type!=="AssignmentExpression"?!1:t==null||(0,P.default)(e,t)}function gB(e,t){return!e||e.type!=="BinaryExpression"?!1:t==null||(0,P.default)(e,t)}function bB(e,t){return!e||e.type!=="InterpreterDirective"?!1:t==null||(0,P.default)(e,t)}function EB(e,t){return!e||e.type!=="Directive"?!1:t==null||(0,P.default)(e,t)}function TB(e,t){return!e||e.type!=="DirectiveLiteral"?!1:t==null||(0,P.default)(e,t)}function SB(e,t){return!e||e.type!=="BlockStatement"?!1:t==null||(0,P.default)(e,t)}function xB(e,t){return!e||e.type!=="BreakStatement"?!1:t==null||(0,P.default)(e,t)}function vB(e,t){return!e||e.type!=="CallExpression"?!1:t==null||(0,P.default)(e,t)}function PB(e,t){return!e||e.type!=="CatchClause"?!1:t==null||(0,P.default)(e,t)}function AB(e,t){return!e||e.type!=="ConditionalExpression"?!1:t==null||(0,P.default)(e,t)}function CB(e,t){return!e||e.type!=="ContinueStatement"?!1:t==null||(0,P.default)(e,t)}function DB(e,t){return!e||e.type!=="DebuggerStatement"?!1:t==null||(0,P.default)(e,t)}function wB(e,t){return!e||e.type!=="DoWhileStatement"?!1:t==null||(0,P.default)(e,t)}function IB(e,t){return!e||e.type!=="EmptyStatement"?!1:t==null||(0,P.default)(e,t)}function _B(e,t){return!e||e.type!=="ExpressionStatement"?!1:t==null||(0,P.default)(e,t)}function OB(e,t){return!e||e.type!=="File"?!1:t==null||(0,P.default)(e,t)}function NB(e,t){return!e||e.type!=="ForInStatement"?!1:t==null||(0,P.default)(e,t)}function BB(e,t){return!e||e.type!=="ForStatement"?!1:t==null||(0,P.default)(e,t)}function kB(e,t){return!e||e.type!=="FunctionDeclaration"?!1:t==null||(0,P.default)(e,t)}function FB(e,t){return!e||e.type!=="FunctionExpression"?!1:t==null||(0,P.default)(e,t)}function LB(e,t){return!e||e.type!=="Identifier"?!1:t==null||(0,P.default)(e,t)}function jB(e,t){return!e||e.type!=="IfStatement"?!1:t==null||(0,P.default)(e,t)}function MB(e,t){return!e||e.type!=="LabeledStatement"?!1:t==null||(0,P.default)(e,t)}function RB(e,t){return!e||e.type!=="StringLiteral"?!1:t==null||(0,P.default)(e,t)}function qB(e,t){return!e||e.type!=="NumericLiteral"?!1:t==null||(0,P.default)(e,t)}function UB(e,t){return!e||e.type!=="NullLiteral"?!1:t==null||(0,P.default)(e,t)}function VB(e,t){return!e||e.type!=="BooleanLiteral"?!1:t==null||(0,P.default)(e,t)}function $B(e,t){return!e||e.type!=="RegExpLiteral"?!1:t==null||(0,P.default)(e,t)}function WB(e,t){return!e||e.type!=="LogicalExpression"?!1:t==null||(0,P.default)(e,t)}function KB(e,t){return!e||e.type!=="MemberExpression"?!1:t==null||(0,P.default)(e,t)}function GB(e,t){return!e||e.type!=="NewExpression"?!1:t==null||(0,P.default)(e,t)}function HB(e,t){return!e||e.type!=="Program"?!1:t==null||(0,P.default)(e,t)}function YB(e,t){return!e||e.type!=="ObjectExpression"?!1:t==null||(0,P.default)(e,t)}function XB(e,t){return!e||e.type!=="ObjectMethod"?!1:t==null||(0,P.default)(e,t)}function JB(e,t){return!e||e.type!=="ObjectProperty"?!1:t==null||(0,P.default)(e,t)}function zB(e,t){return!e||e.type!=="RestElement"?!1:t==null||(0,P.default)(e,t)}function QB(e,t){return!e||e.type!=="ReturnStatement"?!1:t==null||(0,P.default)(e,t)}function ZB(e,t){return!e||e.type!=="SequenceExpression"?!1:t==null||(0,P.default)(e,t)}function e3(e,t){return!e||e.type!=="ParenthesizedExpression"?!1:t==null||(0,P.default)(e,t)}function t3(e,t){return!e||e.type!=="SwitchCase"?!1:t==null||(0,P.default)(e,t)}function r3(e,t){return!e||e.type!=="SwitchStatement"?!1:t==null||(0,P.default)(e,t)}function n3(e,t){return!e||e.type!=="ThisExpression"?!1:t==null||(0,P.default)(e,t)}function s3(e,t){return!e||e.type!=="ThrowStatement"?!1:t==null||(0,P.default)(e,t)}function i3(e,t){return!e||e.type!=="TryStatement"?!1:t==null||(0,P.default)(e,t)}function a3(e,t){return!e||e.type!=="UnaryExpression"?!1:t==null||(0,P.default)(e,t)}function o3(e,t){return!e||e.type!=="UpdateExpression"?!1:t==null||(0,P.default)(e,t)}function l3(e,t){return!e||e.type!=="VariableDeclaration"?!1:t==null||(0,P.default)(e,t)}function u3(e,t){return!e||e.type!=="VariableDeclarator"?!1:t==null||(0,P.default)(e,t)}function c3(e,t){return!e||e.type!=="WhileStatement"?!1:t==null||(0,P.default)(e,t)}function p3(e,t){return!e||e.type!=="WithStatement"?!1:t==null||(0,P.default)(e,t)}function f3(e,t){return!e||e.type!=="AssignmentPattern"?!1:t==null||(0,P.default)(e,t)}function d3(e,t){return!e||e.type!=="ArrayPattern"?!1:t==null||(0,P.default)(e,t)}function h3(e,t){return!e||e.type!=="ArrowFunctionExpression"?!1:t==null||(0,P.default)(e,t)}function m3(e,t){return!e||e.type!=="ClassBody"?!1:t==null||(0,P.default)(e,t)}function y3(e,t){return!e||e.type!=="ClassExpression"?!1:t==null||(0,P.default)(e,t)}function g3(e,t){return!e||e.type!=="ClassDeclaration"?!1:t==null||(0,P.default)(e,t)}function b3(e,t){return!e||e.type!=="ExportAllDeclaration"?!1:t==null||(0,P.default)(e,t)}function E3(e,t){return!e||e.type!=="ExportDefaultDeclaration"?!1:t==null||(0,P.default)(e,t)}function T3(e,t){return!e||e.type!=="ExportNamedDeclaration"?!1:t==null||(0,P.default)(e,t)}function S3(e,t){return!e||e.type!=="ExportSpecifier"?!1:t==null||(0,P.default)(e,t)}function x3(e,t){return!e||e.type!=="ForOfStatement"?!1:t==null||(0,P.default)(e,t)}function v3(e,t){return!e||e.type!=="ImportDeclaration"?!1:t==null||(0,P.default)(e,t)}function P3(e,t){return!e||e.type!=="ImportDefaultSpecifier"?!1:t==null||(0,P.default)(e,t)}function A3(e,t){return!e||e.type!=="ImportNamespaceSpecifier"?!1:t==null||(0,P.default)(e,t)}function C3(e,t){return!e||e.type!=="ImportSpecifier"?!1:t==null||(0,P.default)(e,t)}function D3(e,t){return!e||e.type!=="ImportExpression"?!1:t==null||(0,P.default)(e,t)}function w3(e,t){return!e||e.type!=="MetaProperty"?!1:t==null||(0,P.default)(e,t)}function I3(e,t){return!e||e.type!=="ClassMethod"?!1:t==null||(0,P.default)(e,t)}function _3(e,t){return!e||e.type!=="ObjectPattern"?!1:t==null||(0,P.default)(e,t)}function O3(e,t){return!e||e.type!=="SpreadElement"?!1:t==null||(0,P.default)(e,t)}function N3(e,t){return!e||e.type!=="Super"?!1:t==null||(0,P.default)(e,t)}function B3(e,t){return!e||e.type!=="TaggedTemplateExpression"?!1:t==null||(0,P.default)(e,t)}function k3(e,t){return!e||e.type!=="TemplateElement"?!1:t==null||(0,P.default)(e,t)}function F3(e,t){return!e||e.type!=="TemplateLiteral"?!1:t==null||(0,P.default)(e,t)}function L3(e,t){return!e||e.type!=="YieldExpression"?!1:t==null||(0,P.default)(e,t)}function j3(e,t){return!e||e.type!=="AwaitExpression"?!1:t==null||(0,P.default)(e,t)}function M3(e,t){return!e||e.type!=="Import"?!1:t==null||(0,P.default)(e,t)}function R3(e,t){return!e||e.type!=="BigIntLiteral"?!1:t==null||(0,P.default)(e,t)}function q3(e,t){return!e||e.type!=="ExportNamespaceSpecifier"?!1:t==null||(0,P.default)(e,t)}function U3(e,t){return!e||e.type!=="OptionalMemberExpression"?!1:t==null||(0,P.default)(e,t)}function V3(e,t){return!e||e.type!=="OptionalCallExpression"?!1:t==null||(0,P.default)(e,t)}function $3(e,t){return!e||e.type!=="ClassProperty"?!1:t==null||(0,P.default)(e,t)}function W3(e,t){return!e||e.type!=="ClassAccessorProperty"?!1:t==null||(0,P.default)(e,t)}function K3(e,t){return!e||e.type!=="ClassPrivateProperty"?!1:t==null||(0,P.default)(e,t)}function G3(e,t){return!e||e.type!=="ClassPrivateMethod"?!1:t==null||(0,P.default)(e,t)}function H3(e,t){return!e||e.type!=="PrivateName"?!1:t==null||(0,P.default)(e,t)}function Y3(e,t){return!e||e.type!=="StaticBlock"?!1:t==null||(0,P.default)(e,t)}function X3(e,t){return!e||e.type!=="AnyTypeAnnotation"?!1:t==null||(0,P.default)(e,t)}function J3(e,t){return!e||e.type!=="ArrayTypeAnnotation"?!1:t==null||(0,P.default)(e,t)}function z3(e,t){return!e||e.type!=="BooleanTypeAnnotation"?!1:t==null||(0,P.default)(e,t)}function Q3(e,t){return!e||e.type!=="BooleanLiteralTypeAnnotation"?!1:t==null||(0,P.default)(e,t)}function Z3(e,t){return!e||e.type!=="NullLiteralTypeAnnotation"?!1:t==null||(0,P.default)(e,t)}function ek(e,t){return!e||e.type!=="ClassImplements"?!1:t==null||(0,P.default)(e,t)}function tk(e,t){return!e||e.type!=="DeclareClass"?!1:t==null||(0,P.default)(e,t)}function rk(e,t){return!e||e.type!=="DeclareFunction"?!1:t==null||(0,P.default)(e,t)}function nk(e,t){return!e||e.type!=="DeclareInterface"?!1:t==null||(0,P.default)(e,t)}function sk(e,t){return!e||e.type!=="DeclareModule"?!1:t==null||(0,P.default)(e,t)}function ik(e,t){return!e||e.type!=="DeclareModuleExports"?!1:t==null||(0,P.default)(e,t)}function ak(e,t){return!e||e.type!=="DeclareTypeAlias"?!1:t==null||(0,P.default)(e,t)}function ok(e,t){return!e||e.type!=="DeclareOpaqueType"?!1:t==null||(0,P.default)(e,t)}function lk(e,t){return!e||e.type!=="DeclareVariable"?!1:t==null||(0,P.default)(e,t)}function uk(e,t){return!e||e.type!=="DeclareExportDeclaration"?!1:t==null||(0,P.default)(e,t)}function ck(e,t){return!e||e.type!=="DeclareExportAllDeclaration"?!1:t==null||(0,P.default)(e,t)}function pk(e,t){return!e||e.type!=="DeclaredPredicate"?!1:t==null||(0,P.default)(e,t)}function fk(e,t){return!e||e.type!=="ExistsTypeAnnotation"?!1:t==null||(0,P.default)(e,t)}function dk(e,t){return!e||e.type!=="FunctionTypeAnnotation"?!1:t==null||(0,P.default)(e,t)}function hk(e,t){return!e||e.type!=="FunctionTypeParam"?!1:t==null||(0,P.default)(e,t)}function mk(e,t){return!e||e.type!=="GenericTypeAnnotation"?!1:t==null||(0,P.default)(e,t)}function yk(e,t){return!e||e.type!=="InferredPredicate"?!1:t==null||(0,P.default)(e,t)}function gk(e,t){return!e||e.type!=="InterfaceExtends"?!1:t==null||(0,P.default)(e,t)}function bk(e,t){return!e||e.type!=="InterfaceDeclaration"?!1:t==null||(0,P.default)(e,t)}function Ek(e,t){return!e||e.type!=="InterfaceTypeAnnotation"?!1:t==null||(0,P.default)(e,t)}function Tk(e,t){return!e||e.type!=="IntersectionTypeAnnotation"?!1:t==null||(0,P.default)(e,t)}function Sk(e,t){return!e||e.type!=="MixedTypeAnnotation"?!1:t==null||(0,P.default)(e,t)}function xk(e,t){return!e||e.type!=="EmptyTypeAnnotation"?!1:t==null||(0,P.default)(e,t)}function vk(e,t){return!e||e.type!=="NullableTypeAnnotation"?!1:t==null||(0,P.default)(e,t)}function Pk(e,t){return!e||e.type!=="NumberLiteralTypeAnnotation"?!1:t==null||(0,P.default)(e,t)}function Ak(e,t){return!e||e.type!=="NumberTypeAnnotation"?!1:t==null||(0,P.default)(e,t)}function Ck(e,t){return!e||e.type!=="ObjectTypeAnnotation"?!1:t==null||(0,P.default)(e,t)}function Dk(e,t){return!e||e.type!=="ObjectTypeInternalSlot"?!1:t==null||(0,P.default)(e,t)}function wk(e,t){return!e||e.type!=="ObjectTypeCallProperty"?!1:t==null||(0,P.default)(e,t)}function Ik(e,t){return!e||e.type!=="ObjectTypeIndexer"?!1:t==null||(0,P.default)(e,t)}function _k(e,t){return!e||e.type!=="ObjectTypeProperty"?!1:t==null||(0,P.default)(e,t)}function Ok(e,t){return!e||e.type!=="ObjectTypeSpreadProperty"?!1:t==null||(0,P.default)(e,t)}function Nk(e,t){return!e||e.type!=="OpaqueType"?!1:t==null||(0,P.default)(e,t)}function Bk(e,t){return!e||e.type!=="QualifiedTypeIdentifier"?!1:t==null||(0,P.default)(e,t)}function kk(e,t){return!e||e.type!=="StringLiteralTypeAnnotation"?!1:t==null||(0,P.default)(e,t)}function Fk(e,t){return!e||e.type!=="StringTypeAnnotation"?!1:t==null||(0,P.default)(e,t)}function Lk(e,t){return!e||e.type!=="SymbolTypeAnnotation"?!1:t==null||(0,P.default)(e,t)}function jk(e,t){return!e||e.type!=="ThisTypeAnnotation"?!1:t==null||(0,P.default)(e,t)}function Mk(e,t){return!e||e.type!=="TupleTypeAnnotation"?!1:t==null||(0,P.default)(e,t)}function Rk(e,t){return!e||e.type!=="TypeofTypeAnnotation"?!1:t==null||(0,P.default)(e,t)}function qk(e,t){return!e||e.type!=="TypeAlias"?!1:t==null||(0,P.default)(e,t)}function Uk(e,t){return!e||e.type!=="TypeAnnotation"?!1:t==null||(0,P.default)(e,t)}function Vk(e,t){return!e||e.type!=="TypeCastExpression"?!1:t==null||(0,P.default)(e,t)}function $k(e,t){return!e||e.type!=="TypeParameter"?!1:t==null||(0,P.default)(e,t)}function Wk(e,t){return!e||e.type!=="TypeParameterDeclaration"?!1:t==null||(0,P.default)(e,t)}function Kk(e,t){return!e||e.type!=="TypeParameterInstantiation"?!1:t==null||(0,P.default)(e,t)}function Gk(e,t){return!e||e.type!=="UnionTypeAnnotation"?!1:t==null||(0,P.default)(e,t)}function Hk(e,t){return!e||e.type!=="Variance"?!1:t==null||(0,P.default)(e,t)}function Yk(e,t){return!e||e.type!=="VoidTypeAnnotation"?!1:t==null||(0,P.default)(e,t)}function Xk(e,t){return!e||e.type!=="EnumDeclaration"?!1:t==null||(0,P.default)(e,t)}function Jk(e,t){return!e||e.type!=="EnumBooleanBody"?!1:t==null||(0,P.default)(e,t)}function zk(e,t){return!e||e.type!=="EnumNumberBody"?!1:t==null||(0,P.default)(e,t)}function Qk(e,t){return!e||e.type!=="EnumStringBody"?!1:t==null||(0,P.default)(e,t)}function Zk(e,t){return!e||e.type!=="EnumSymbolBody"?!1:t==null||(0,P.default)(e,t)}function eF(e,t){return!e||e.type!=="EnumBooleanMember"?!1:t==null||(0,P.default)(e,t)}function tF(e,t){return!e||e.type!=="EnumNumberMember"?!1:t==null||(0,P.default)(e,t)}function rF(e,t){return!e||e.type!=="EnumStringMember"?!1:t==null||(0,P.default)(e,t)}function nF(e,t){return!e||e.type!=="EnumDefaultedMember"?!1:t==null||(0,P.default)(e,t)}function sF(e,t){return!e||e.type!=="IndexedAccessType"?!1:t==null||(0,P.default)(e,t)}function iF(e,t){return!e||e.type!=="OptionalIndexedAccessType"?!1:t==null||(0,P.default)(e,t)}function aF(e,t){return!e||e.type!=="JSXAttribute"?!1:t==null||(0,P.default)(e,t)}function oF(e,t){return!e||e.type!=="JSXClosingElement"?!1:t==null||(0,P.default)(e,t)}function lF(e,t){return!e||e.type!=="JSXElement"?!1:t==null||(0,P.default)(e,t)}function uF(e,t){return!e||e.type!=="JSXEmptyExpression"?!1:t==null||(0,P.default)(e,t)}function cF(e,t){return!e||e.type!=="JSXExpressionContainer"?!1:t==null||(0,P.default)(e,t)}function pF(e,t){return!e||e.type!=="JSXSpreadChild"?!1:t==null||(0,P.default)(e,t)}function fF(e,t){return!e||e.type!=="JSXIdentifier"?!1:t==null||(0,P.default)(e,t)}function dF(e,t){return!e||e.type!=="JSXMemberExpression"?!1:t==null||(0,P.default)(e,t)}function hF(e,t){return!e||e.type!=="JSXNamespacedName"?!1:t==null||(0,P.default)(e,t)}function mF(e,t){return!e||e.type!=="JSXOpeningElement"?!1:t==null||(0,P.default)(e,t)}function yF(e,t){return!e||e.type!=="JSXSpreadAttribute"?!1:t==null||(0,P.default)(e,t)}function gF(e,t){return!e||e.type!=="JSXText"?!1:t==null||(0,P.default)(e,t)}function bF(e,t){return!e||e.type!=="JSXFragment"?!1:t==null||(0,P.default)(e,t)}function EF(e,t){return!e||e.type!=="JSXOpeningFragment"?!1:t==null||(0,P.default)(e,t)}function TF(e,t){return!e||e.type!=="JSXClosingFragment"?!1:t==null||(0,P.default)(e,t)}function SF(e,t){return!e||e.type!=="Noop"?!1:t==null||(0,P.default)(e,t)}function xF(e,t){return!e||e.type!=="Placeholder"?!1:t==null||(0,P.default)(e,t)}function vF(e,t){return!e||e.type!=="V8IntrinsicIdentifier"?!1:t==null||(0,P.default)(e,t)}function PF(e,t){return!e||e.type!=="ArgumentPlaceholder"?!1:t==null||(0,P.default)(e,t)}function AF(e,t){return!e||e.type!=="BindExpression"?!1:t==null||(0,P.default)(e,t)}function CF(e,t){return!e||e.type!=="ImportAttribute"?!1:t==null||(0,P.default)(e,t)}function DF(e,t){return!e||e.type!=="Decorator"?!1:t==null||(0,P.default)(e,t)}function wF(e,t){return!e||e.type!=="DoExpression"?!1:t==null||(0,P.default)(e,t)}function IF(e,t){return!e||e.type!=="ExportDefaultSpecifier"?!1:t==null||(0,P.default)(e,t)}function _F(e,t){return!e||e.type!=="RecordExpression"?!1:t==null||(0,P.default)(e,t)}function OF(e,t){return!e||e.type!=="TupleExpression"?!1:t==null||(0,P.default)(e,t)}function NF(e,t){return!e||e.type!=="DecimalLiteral"?!1:t==null||(0,P.default)(e,t)}function BF(e,t){return!e||e.type!=="ModuleExpression"?!1:t==null||(0,P.default)(e,t)}function kF(e,t){return!e||e.type!=="TopicReference"?!1:t==null||(0,P.default)(e,t)}function FF(e,t){return!e||e.type!=="PipelineTopicExpression"?!1:t==null||(0,P.default)(e,t)}function LF(e,t){return!e||e.type!=="PipelineBareFunction"?!1:t==null||(0,P.default)(e,t)}function jF(e,t){return!e||e.type!=="PipelinePrimaryTopicReference"?!1:t==null||(0,P.default)(e,t)}function MF(e,t){return!e||e.type!=="TSParameterProperty"?!1:t==null||(0,P.default)(e,t)}function RF(e,t){return!e||e.type!=="TSDeclareFunction"?!1:t==null||(0,P.default)(e,t)}function qF(e,t){return!e||e.type!=="TSDeclareMethod"?!1:t==null||(0,P.default)(e,t)}function UF(e,t){return!e||e.type!=="TSQualifiedName"?!1:t==null||(0,P.default)(e,t)}function VF(e,t){return!e||e.type!=="TSCallSignatureDeclaration"?!1:t==null||(0,P.default)(e,t)}function $F(e,t){return!e||e.type!=="TSConstructSignatureDeclaration"?!1:t==null||(0,P.default)(e,t)}function WF(e,t){return!e||e.type!=="TSPropertySignature"?!1:t==null||(0,P.default)(e,t)}function KF(e,t){return!e||e.type!=="TSMethodSignature"?!1:t==null||(0,P.default)(e,t)}function GF(e,t){return!e||e.type!=="TSIndexSignature"?!1:t==null||(0,P.default)(e,t)}function HF(e,t){return!e||e.type!=="TSAnyKeyword"?!1:t==null||(0,P.default)(e,t)}function YF(e,t){return!e||e.type!=="TSBooleanKeyword"?!1:t==null||(0,P.default)(e,t)}function XF(e,t){return!e||e.type!=="TSBigIntKeyword"?!1:t==null||(0,P.default)(e,t)}function JF(e,t){return!e||e.type!=="TSIntrinsicKeyword"?!1:t==null||(0,P.default)(e,t)}function zF(e,t){return!e||e.type!=="TSNeverKeyword"?!1:t==null||(0,P.default)(e,t)}function QF(e,t){return!e||e.type!=="TSNullKeyword"?!1:t==null||(0,P.default)(e,t)}function ZF(e,t){return!e||e.type!=="TSNumberKeyword"?!1:t==null||(0,P.default)(e,t)}function eL(e,t){return!e||e.type!=="TSObjectKeyword"?!1:t==null||(0,P.default)(e,t)}function tL(e,t){return!e||e.type!=="TSStringKeyword"?!1:t==null||(0,P.default)(e,t)}function rL(e,t){return!e||e.type!=="TSSymbolKeyword"?!1:t==null||(0,P.default)(e,t)}function nL(e,t){return!e||e.type!=="TSUndefinedKeyword"?!1:t==null||(0,P.default)(e,t)}function sL(e,t){return!e||e.type!=="TSUnknownKeyword"?!1:t==null||(0,P.default)(e,t)}function iL(e,t){return!e||e.type!=="TSVoidKeyword"?!1:t==null||(0,P.default)(e,t)}function aL(e,t){return!e||e.type!=="TSThisType"?!1:t==null||(0,P.default)(e,t)}function oL(e,t){return!e||e.type!=="TSFunctionType"?!1:t==null||(0,P.default)(e,t)}function lL(e,t){return!e||e.type!=="TSConstructorType"?!1:t==null||(0,P.default)(e,t)}function uL(e,t){return!e||e.type!=="TSTypeReference"?!1:t==null||(0,P.default)(e,t)}function cL(e,t){return!e||e.type!=="TSTypePredicate"?!1:t==null||(0,P.default)(e,t)}function pL(e,t){return!e||e.type!=="TSTypeQuery"?!1:t==null||(0,P.default)(e,t)}function fL(e,t){return!e||e.type!=="TSTypeLiteral"?!1:t==null||(0,P.default)(e,t)}function dL(e,t){return!e||e.type!=="TSArrayType"?!1:t==null||(0,P.default)(e,t)}function hL(e,t){return!e||e.type!=="TSTupleType"?!1:t==null||(0,P.default)(e,t)}function mL(e,t){return!e||e.type!=="TSOptionalType"?!1:t==null||(0,P.default)(e,t)}function yL(e,t){return!e||e.type!=="TSRestType"?!1:t==null||(0,P.default)(e,t)}function gL(e,t){return!e||e.type!=="TSNamedTupleMember"?!1:t==null||(0,P.default)(e,t)}function bL(e,t){return!e||e.type!=="TSUnionType"?!1:t==null||(0,P.default)(e,t)}function EL(e,t){return!e||e.type!=="TSIntersectionType"?!1:t==null||(0,P.default)(e,t)}function TL(e,t){return!e||e.type!=="TSConditionalType"?!1:t==null||(0,P.default)(e,t)}function SL(e,t){return!e||e.type!=="TSInferType"?!1:t==null||(0,P.default)(e,t)}function xL(e,t){return!e||e.type!=="TSParenthesizedType"?!1:t==null||(0,P.default)(e,t)}function vL(e,t){return!e||e.type!=="TSTypeOperator"?!1:t==null||(0,P.default)(e,t)}function PL(e,t){return!e||e.type!=="TSIndexedAccessType"?!1:t==null||(0,P.default)(e,t)}function AL(e,t){return!e||e.type!=="TSMappedType"?!1:t==null||(0,P.default)(e,t)}function CL(e,t){return!e||e.type!=="TSTemplateLiteralType"?!1:t==null||(0,P.default)(e,t)}function DL(e,t){return!e||e.type!=="TSLiteralType"?!1:t==null||(0,P.default)(e,t)}function wL(e,t){return!e||e.type!=="TSExpressionWithTypeArguments"?!1:t==null||(0,P.default)(e,t)}function IL(e,t){return!e||e.type!=="TSInterfaceDeclaration"?!1:t==null||(0,P.default)(e,t)}function _L(e,t){return!e||e.type!=="TSInterfaceBody"?!1:t==null||(0,P.default)(e,t)}function OL(e,t){return!e||e.type!=="TSTypeAliasDeclaration"?!1:t==null||(0,P.default)(e,t)}function NL(e,t){return!e||e.type!=="TSInstantiationExpression"?!1:t==null||(0,P.default)(e,t)}function BL(e,t){return!e||e.type!=="TSAsExpression"?!1:t==null||(0,P.default)(e,t)}function kL(e,t){return!e||e.type!=="TSSatisfiesExpression"?!1:t==null||(0,P.default)(e,t)}function FL(e,t){return!e||e.type!=="TSTypeAssertion"?!1:t==null||(0,P.default)(e,t)}function LL(e,t){return!e||e.type!=="TSEnumBody"?!1:t==null||(0,P.default)(e,t)}function jL(e,t){return!e||e.type!=="TSEnumDeclaration"?!1:t==null||(0,P.default)(e,t)}function ML(e,t){return!e||e.type!=="TSEnumMember"?!1:t==null||(0,P.default)(e,t)}function RL(e,t){return!e||e.type!=="TSModuleDeclaration"?!1:t==null||(0,P.default)(e,t)}function qL(e,t){return!e||e.type!=="TSModuleBlock"?!1:t==null||(0,P.default)(e,t)}function UL(e,t){return!e||e.type!=="TSImportType"?!1:t==null||(0,P.default)(e,t)}function VL(e,t){return!e||e.type!=="TSImportEqualsDeclaration"?!1:t==null||(0,P.default)(e,t)}function $L(e,t){return!e||e.type!=="TSExternalModuleReference"?!1:t==null||(0,P.default)(e,t)}function WL(e,t){return!e||e.type!=="TSNonNullExpression"?!1:t==null||(0,P.default)(e,t)}function KL(e,t){return!e||e.type!=="TSExportAssignment"?!1:t==null||(0,P.default)(e,t)}function GL(e,t){return!e||e.type!=="TSNamespaceExportDeclaration"?!1:t==null||(0,P.default)(e,t)}function HL(e,t){return!e||e.type!=="TSTypeAnnotation"?!1:t==null||(0,P.default)(e,t)}function YL(e,t){return!e||e.type!=="TSTypeParameterInstantiation"?!1:t==null||(0,P.default)(e,t)}function XL(e,t){return!e||e.type!=="TSTypeParameterDeclaration"?!1:t==null||(0,P.default)(e,t)}function JL(e,t){return!e||e.type!=="TSTypeParameter"?!1:t==null||(0,P.default)(e,t)}function zL(e,t){if(!e)return!1;switch(e.type){case"ArrayExpression":case"AssignmentExpression":case"BinaryExpression":case"InterpreterDirective":case"Directive":case"DirectiveLiteral":case"BlockStatement":case"BreakStatement":case"CallExpression":case"CatchClause":case"ConditionalExpression":case"ContinueStatement":case"DebuggerStatement":case"DoWhileStatement":case"EmptyStatement":case"ExpressionStatement":case"File":case"ForInStatement":case"ForStatement":case"FunctionDeclaration":case"FunctionExpression":case"Identifier":case"IfStatement":case"LabeledStatement":case"StringLiteral":case"NumericLiteral":case"NullLiteral":case"BooleanLiteral":case"RegExpLiteral":case"LogicalExpression":case"MemberExpression":case"NewExpression":case"Program":case"ObjectExpression":case"ObjectMethod":case"ObjectProperty":case"RestElement":case"ReturnStatement":case"SequenceExpression":case"ParenthesizedExpression":case"SwitchCase":case"SwitchStatement":case"ThisExpression":case"ThrowStatement":case"TryStatement":case"UnaryExpression":case"UpdateExpression":case"VariableDeclaration":case"VariableDeclarator":case"WhileStatement":case"WithStatement":case"AssignmentPattern":case"ArrayPattern":case"ArrowFunctionExpression":case"ClassBody":case"ClassExpression":case"ClassDeclaration":case"ExportAllDeclaration":case"ExportDefaultDeclaration":case"ExportNamedDeclaration":case"ExportSpecifier":case"ForOfStatement":case"ImportDeclaration":case"ImportDefaultSpecifier":case"ImportNamespaceSpecifier":case"ImportSpecifier":case"ImportExpression":case"MetaProperty":case"ClassMethod":case"ObjectPattern":case"SpreadElement":case"Super":case"TaggedTemplateExpression":case"TemplateElement":case"TemplateLiteral":case"YieldExpression":case"AwaitExpression":case"Import":case"BigIntLiteral":case"ExportNamespaceSpecifier":case"OptionalMemberExpression":case"OptionalCallExpression":case"ClassProperty":case"ClassAccessorProperty":case"ClassPrivateProperty":case"ClassPrivateMethod":case"PrivateName":case"StaticBlock":break;case"Placeholder":switch(e.expectedNode){case"Identifier":case"StringLiteral":case"BlockStatement":case"ClassBody":break;default:return!1}break;default:return!1}return t==null||(0,P.default)(e,t)}function QL(e,t){if(!e)return!1;switch(e.type){case"ArrayExpression":case"AssignmentExpression":case"BinaryExpression":case"CallExpression":case"ConditionalExpression":case"FunctionExpression":case"Identifier":case"StringLiteral":case"NumericLiteral":case"NullLiteral":case"BooleanLiteral":case"RegExpLiteral":case"LogicalExpression":case"MemberExpression":case"NewExpression":case"ObjectExpression":case"SequenceExpression":case"ParenthesizedExpression":case"ThisExpression":case"UnaryExpression":case"UpdateExpression":case"ArrowFunctionExpression":case"ClassExpression":case"ImportExpression":case"MetaProperty":case"Super":case"TaggedTemplateExpression":case"TemplateLiteral":case"YieldExpression":case"AwaitExpression":case"Import":case"BigIntLiteral":case"OptionalMemberExpression":case"OptionalCallExpression":case"TypeCastExpression":case"JSXElement":case"JSXFragment":case"BindExpression":case"DoExpression":case"RecordExpression":case"TupleExpression":case"DecimalLiteral":case"ModuleExpression":case"TopicReference":case"PipelineTopicExpression":case"PipelineBareFunction":case"PipelinePrimaryTopicReference":case"TSInstantiationExpression":case"TSAsExpression":case"TSSatisfiesExpression":case"TSTypeAssertion":case"TSNonNullExpression":break;case"Placeholder":switch(e.expectedNode){case"Expression":case"Identifier":case"StringLiteral":break;default:return!1}break;default:return!1}return t==null||(0,P.default)(e,t)}function ZL(e,t){if(!e)return!1;switch(e.type){case"BinaryExpression":case"LogicalExpression":break;default:return!1}return t==null||(0,P.default)(e,t)}function e8(e,t){if(!e)return!1;switch(e.type){case"BlockStatement":case"CatchClause":case"DoWhileStatement":case"ForInStatement":case"ForStatement":case"FunctionDeclaration":case"FunctionExpression":case"Program":case"ObjectMethod":case"SwitchStatement":case"WhileStatement":case"ArrowFunctionExpression":case"ClassExpression":case"ClassDeclaration":case"ForOfStatement":case"ClassMethod":case"ClassPrivateMethod":case"StaticBlock":case"TSModuleBlock":break;case"Placeholder":if(e.expectedNode==="BlockStatement")break;default:return!1}return t==null||(0,P.default)(e,t)}function t8(e,t){if(!e)return!1;switch(e.type){case"BlockStatement":case"CatchClause":case"DoWhileStatement":case"ForInStatement":case"ForStatement":case"FunctionDeclaration":case"FunctionExpression":case"Program":case"ObjectMethod":case"SwitchStatement":case"WhileStatement":case"ArrowFunctionExpression":case"ForOfStatement":case"ClassMethod":case"ClassPrivateMethod":case"StaticBlock":case"TSModuleBlock":break;case"Placeholder":if(e.expectedNode==="BlockStatement")break;default:return!1}return t==null||(0,P.default)(e,t)}function r8(e,t){if(!e)return!1;switch(e.type){case"BlockStatement":case"Program":case"TSModuleBlock":break;case"Placeholder":if(e.expectedNode==="BlockStatement")break;default:return!1}return t==null||(0,P.default)(e,t)}function n8(e,t){if(!e)return!1;switch(e.type){case"BlockStatement":case"BreakStatement":case"ContinueStatement":case"DebuggerStatement":case"DoWhileStatement":case"EmptyStatement":case"ExpressionStatement":case"ForInStatement":case"ForStatement":case"FunctionDeclaration":case"IfStatement":case"LabeledStatement":case"ReturnStatement":case"SwitchStatement":case"ThrowStatement":case"TryStatement":case"VariableDeclaration":case"WhileStatement":case"WithStatement":case"ClassDeclaration":case"ExportAllDeclaration":case"ExportDefaultDeclaration":case"ExportNamedDeclaration":case"ForOfStatement":case"ImportDeclaration":case"DeclareClass":case"DeclareFunction":case"DeclareInterface":case"DeclareModule":case"DeclareModuleExports":case"DeclareTypeAlias":case"DeclareOpaqueType":case"DeclareVariable":case"DeclareExportDeclaration":case"DeclareExportAllDeclaration":case"InterfaceDeclaration":case"OpaqueType":case"TypeAlias":case"EnumDeclaration":case"TSDeclareFunction":case"TSInterfaceDeclaration":case"TSTypeAliasDeclaration":case"TSEnumDeclaration":case"TSModuleDeclaration":case"TSImportEqualsDeclaration":case"TSExportAssignment":case"TSNamespaceExportDeclaration":break;case"Placeholder":switch(e.expectedNode){case"Statement":case"Declaration":case"BlockStatement":break;default:return!1}break;default:return!1}return t==null||(0,P.default)(e,t)}function s8(e,t){if(!e)return!1;switch(e.type){case"BreakStatement":case"ContinueStatement":case"ReturnStatement":case"ThrowStatement":case"YieldExpression":case"AwaitExpression":break;default:return!1}return t==null||(0,P.default)(e,t)}function i8(e,t){if(!e)return!1;switch(e.type){case"BreakStatement":case"ContinueStatement":case"ReturnStatement":case"ThrowStatement":break;default:return!1}return t==null||(0,P.default)(e,t)}function a8(e,t){if(!e)return!1;switch(e.type){case"ConditionalExpression":case"IfStatement":break;default:return!1}return t==null||(0,P.default)(e,t)}function o8(e,t){if(!e)return!1;switch(e.type){case"DoWhileStatement":case"ForInStatement":case"ForStatement":case"WhileStatement":case"ForOfStatement":break;default:return!1}return t==null||(0,P.default)(e,t)}function l8(e,t){if(!e)return!1;switch(e.type){case"DoWhileStatement":case"WhileStatement":break;default:return!1}return t==null||(0,P.default)(e,t)}function u8(e,t){if(!e)return!1;switch(e.type){case"ExpressionStatement":case"ParenthesizedExpression":case"TypeCastExpression":break;default:return!1}return t==null||(0,P.default)(e,t)}function c8(e,t){if(!e)return!1;switch(e.type){case"ForInStatement":case"ForStatement":case"ForOfStatement":break;default:return!1}return t==null||(0,P.default)(e,t)}function p8(e,t){if(!e)return!1;switch(e.type){case"ForInStatement":case"ForOfStatement":break;default:return!1}return t==null||(0,P.default)(e,t)}function f8(e,t){if(!e)return!1;switch(e.type){case"FunctionDeclaration":case"FunctionExpression":case"ObjectMethod":case"ArrowFunctionExpression":case"ClassMethod":case"ClassPrivateMethod":break;default:return!1}return t==null||(0,P.default)(e,t)}function d8(e,t){if(!e)return!1;switch(e.type){case"FunctionDeclaration":case"FunctionExpression":case"ObjectMethod":case"ArrowFunctionExpression":case"ClassMethod":case"ClassPrivateMethod":case"StaticBlock":case"TSModuleBlock":break;default:return!1}return t==null||(0,P.default)(e,t)}function h8(e,t){if(!e)return!1;switch(e.type){case"FunctionDeclaration":case"FunctionExpression":case"StringLiteral":case"NumericLiteral":case"NullLiteral":case"BooleanLiteral":case"RegExpLiteral":case"ArrowFunctionExpression":case"BigIntLiteral":case"DecimalLiteral":break;case"Placeholder":if(e.expectedNode==="StringLiteral")break;default:return!1}return t==null||(0,P.default)(e,t)}function m8(e,t){if(!e)return!1;switch(e.type){case"FunctionDeclaration":case"VariableDeclaration":case"ClassDeclaration":case"ExportAllDeclaration":case"ExportDefaultDeclaration":case"ExportNamedDeclaration":case"ImportDeclaration":case"DeclareClass":case"DeclareFunction":case"DeclareInterface":case"DeclareModule":case"DeclareModuleExports":case"DeclareTypeAlias":case"DeclareOpaqueType":case"DeclareVariable":case"DeclareExportDeclaration":case"DeclareExportAllDeclaration":case"InterfaceDeclaration":case"OpaqueType":case"TypeAlias":case"EnumDeclaration":case"TSDeclareFunction":case"TSInterfaceDeclaration":case"TSTypeAliasDeclaration":case"TSEnumDeclaration":case"TSModuleDeclaration":case"TSImportEqualsDeclaration":break;case"Placeholder":if(e.expectedNode==="Declaration")break;default:return!1}return t==null||(0,P.default)(e,t)}function y8(e,t){if(!e)return!1;switch(e.type){case"Identifier":case"RestElement":case"AssignmentPattern":case"ArrayPattern":case"ObjectPattern":case"TSAsExpression":case"TSSatisfiesExpression":case"TSTypeAssertion":case"TSNonNullExpression":break;case"Placeholder":switch(e.expectedNode){case"Pattern":case"Identifier":break;default:return!1}break;default:return!1}return t==null||(0,P.default)(e,t)}function g8(e,t){if(!e)return!1;switch(e.type){case"Identifier":case"MemberExpression":case"RestElement":case"AssignmentPattern":case"ArrayPattern":case"ObjectPattern":case"TSParameterProperty":case"TSAsExpression":case"TSSatisfiesExpression":case"TSTypeAssertion":case"TSNonNullExpression":break;case"Placeholder":switch(e.expectedNode){case"Pattern":case"Identifier":break;default:return!1}break;default:return!1}return t==null||(0,P.default)(e,t)}function b8(e,t){if(!e)return!1;switch(e.type){case"Identifier":case"TSQualifiedName":break;case"Placeholder":if(e.expectedNode==="Identifier")break;default:return!1}return t==null||(0,P.default)(e,t)}function E8(e,t){if(!e)return!1;switch(e.type){case"StringLiteral":case"NumericLiteral":case"NullLiteral":case"BooleanLiteral":case"RegExpLiteral":case"TemplateLiteral":case"BigIntLiteral":case"DecimalLiteral":break;case"Placeholder":if(e.expectedNode==="StringLiteral")break;default:return!1}return t==null||(0,P.default)(e,t)}function T8(e,t){if(!e)return!1;switch(e.type){case"StringLiteral":case"NumericLiteral":case"NullLiteral":case"BooleanLiteral":case"BigIntLiteral":case"JSXAttribute":case"JSXClosingElement":case"JSXElement":case"JSXExpressionContainer":case"JSXSpreadChild":case"JSXOpeningElement":case"JSXText":case"JSXFragment":case"JSXOpeningFragment":case"JSXClosingFragment":case"DecimalLiteral":break;case"Placeholder":if(e.expectedNode==="StringLiteral")break;default:return!1}return t==null||(0,P.default)(e,t)}function S8(e,t){if(!e)return!1;switch(e.type){case"ObjectMethod":case"ObjectProperty":case"ObjectTypeInternalSlot":case"ObjectTypeCallProperty":case"ObjectTypeIndexer":case"ObjectTypeProperty":case"ObjectTypeSpreadProperty":break;default:return!1}return t==null||(0,P.default)(e,t)}function x8(e,t){if(!e)return!1;switch(e.type){case"ObjectMethod":case"ClassMethod":case"ClassPrivateMethod":break;default:return!1}return t==null||(0,P.default)(e,t)}function v8(e,t){if(!e)return!1;switch(e.type){case"ObjectMethod":case"ObjectProperty":break;default:return!1}return t==null||(0,P.default)(e,t)}function P8(e,t){if(!e)return!1;switch(e.type){case"ObjectProperty":case"ClassProperty":case"ClassAccessorProperty":case"ClassPrivateProperty":break;default:return!1}return t==null||(0,P.default)(e,t)}function A8(e,t){if(!e)return!1;switch(e.type){case"UnaryExpression":case"SpreadElement":break;default:return!1}return t==null||(0,P.default)(e,t)}function C8(e,t){if(!e)return!1;switch(e.type){case"AssignmentPattern":case"ArrayPattern":case"ObjectPattern":break;case"Placeholder":if(e.expectedNode==="Pattern")break;default:return!1}return t==null||(0,P.default)(e,t)}function D8(e,t){if(!e)return!1;switch(e.type){case"ClassExpression":case"ClassDeclaration":break;default:return!1}return t==null||(0,P.default)(e,t)}function ME(e,t){if(!e)return!1;switch(e.type){case"ExportAllDeclaration":case"ExportDefaultDeclaration":case"ExportNamedDeclaration":case"ImportDeclaration":break;default:return!1}return t==null||(0,P.default)(e,t)}function w8(e,t){if(!e)return!1;switch(e.type){case"ExportAllDeclaration":case"ExportDefaultDeclaration":case"ExportNamedDeclaration":break;default:return!1}return t==null||(0,P.default)(e,t)}function I8(e,t){if(!e)return!1;switch(e.type){case"ExportSpecifier":case"ImportDefaultSpecifier":case"ImportNamespaceSpecifier":case"ImportSpecifier":case"ExportNamespaceSpecifier":case"ExportDefaultSpecifier":break;default:return!1}return t==null||(0,P.default)(e,t)}function _8(e,t){if(!e)return!1;switch(e.type){case"ClassAccessorProperty":break;default:return!1}return t==null||(0,P.default)(e,t)}function O8(e,t){if(!e)return!1;switch(e.type){case"ClassPrivateProperty":case"ClassPrivateMethod":case"PrivateName":break;default:return!1}return t==null||(0,P.default)(e,t)}function N8(e,t){if(!e)return!1;switch(e.type){case"AnyTypeAnnotation":case"ArrayTypeAnnotation":case"BooleanTypeAnnotation":case"BooleanLiteralTypeAnnotation":case"NullLiteralTypeAnnotation":case"ClassImplements":case"DeclareClass":case"DeclareFunction":case"DeclareInterface":case"DeclareModule":case"DeclareModuleExports":case"DeclareTypeAlias":case"DeclareOpaqueType":case"DeclareVariable":case"DeclareExportDeclaration":case"DeclareExportAllDeclaration":case"DeclaredPredicate":case"ExistsTypeAnnotation":case"FunctionTypeAnnotation":case"FunctionTypeParam":case"GenericTypeAnnotation":case"InferredPredicate":case"InterfaceExtends":case"InterfaceDeclaration":case"InterfaceTypeAnnotation":case"IntersectionTypeAnnotation":case"MixedTypeAnnotation":case"EmptyTypeAnnotation":case"NullableTypeAnnotation":case"NumberLiteralTypeAnnotation":case"NumberTypeAnnotation":case"ObjectTypeAnnotation":case"ObjectTypeInternalSlot":case"ObjectTypeCallProperty":case"ObjectTypeIndexer":case"ObjectTypeProperty":case"ObjectTypeSpreadProperty":case"OpaqueType":case"QualifiedTypeIdentifier":case"StringLiteralTypeAnnotation":case"StringTypeAnnotation":case"SymbolTypeAnnotation":case"ThisTypeAnnotation":case"TupleTypeAnnotation":case"TypeofTypeAnnotation":case"TypeAlias":case"TypeAnnotation":case"TypeCastExpression":case"TypeParameter":case"TypeParameterDeclaration":case"TypeParameterInstantiation":case"UnionTypeAnnotation":case"Variance":case"VoidTypeAnnotation":case"EnumDeclaration":case"EnumBooleanBody":case"EnumNumberBody":case"EnumStringBody":case"EnumSymbolBody":case"EnumBooleanMember":case"EnumNumberMember":case"EnumStringMember":case"EnumDefaultedMember":case"IndexedAccessType":case"OptionalIndexedAccessType":break;default:return!1}return t==null||(0,P.default)(e,t)}function B8(e,t){if(!e)return!1;switch(e.type){case"AnyTypeAnnotation":case"ArrayTypeAnnotation":case"BooleanTypeAnnotation":case"BooleanLiteralTypeAnnotation":case"NullLiteralTypeAnnotation":case"ExistsTypeAnnotation":case"FunctionTypeAnnotation":case"GenericTypeAnnotation":case"InterfaceTypeAnnotation":case"IntersectionTypeAnnotation":case"MixedTypeAnnotation":case"EmptyTypeAnnotation":case"NullableTypeAnnotation":case"NumberLiteralTypeAnnotation":case"NumberTypeAnnotation":case"ObjectTypeAnnotation":case"StringLiteralTypeAnnotation":case"StringTypeAnnotation":case"SymbolTypeAnnotation":case"ThisTypeAnnotation":case"TupleTypeAnnotation":case"TypeofTypeAnnotation":case"UnionTypeAnnotation":case"VoidTypeAnnotation":case"IndexedAccessType":case"OptionalIndexedAccessType":break;default:return!1}return t==null||(0,P.default)(e,t)}function k8(e,t){if(!e)return!1;switch(e.type){case"AnyTypeAnnotation":case"BooleanTypeAnnotation":case"NullLiteralTypeAnnotation":case"MixedTypeAnnotation":case"EmptyTypeAnnotation":case"NumberTypeAnnotation":case"StringTypeAnnotation":case"SymbolTypeAnnotation":case"ThisTypeAnnotation":case"VoidTypeAnnotation":break;default:return!1}return t==null||(0,P.default)(e,t)}function F8(e,t){if(!e)return!1;switch(e.type){case"DeclareClass":case"DeclareFunction":case"DeclareInterface":case"DeclareModule":case"DeclareModuleExports":case"DeclareTypeAlias":case"DeclareOpaqueType":case"DeclareVariable":case"DeclareExportDeclaration":case"DeclareExportAllDeclaration":case"InterfaceDeclaration":case"OpaqueType":case"TypeAlias":break;default:return!1}return t==null||(0,P.default)(e,t)}function L8(e,t){if(!e)return!1;switch(e.type){case"DeclaredPredicate":case"InferredPredicate":break;default:return!1}return t==null||(0,P.default)(e,t)}function j8(e,t){if(!e)return!1;switch(e.type){case"EnumBooleanBody":case"EnumNumberBody":case"EnumStringBody":case"EnumSymbolBody":break;default:return!1}return t==null||(0,P.default)(e,t)}function M8(e,t){if(!e)return!1;switch(e.type){case"EnumBooleanMember":case"EnumNumberMember":case"EnumStringMember":case"EnumDefaultedMember":break;default:return!1}return t==null||(0,P.default)(e,t)}function R8(e,t){if(!e)return!1;switch(e.type){case"JSXAttribute":case"JSXClosingElement":case"JSXElement":case"JSXEmptyExpression":case"JSXExpressionContainer":case"JSXSpreadChild":case"JSXIdentifier":case"JSXMemberExpression":case"JSXNamespacedName":case"JSXOpeningElement":case"JSXSpreadAttribute":case"JSXText":case"JSXFragment":case"JSXOpeningFragment":case"JSXClosingFragment":break;default:return!1}return t==null||(0,P.default)(e,t)}function q8(e,t){if(!e)return!1;switch(e.type){case"Noop":case"Placeholder":case"V8IntrinsicIdentifier":break;default:return!1}return t==null||(0,P.default)(e,t)}function U8(e,t){if(!e)return!1;switch(e.type){case"TSParameterProperty":case"TSDeclareFunction":case"TSDeclareMethod":case"TSQualifiedName":case"TSCallSignatureDeclaration":case"TSConstructSignatureDeclaration":case"TSPropertySignature":case"TSMethodSignature":case"TSIndexSignature":case"TSAnyKeyword":case"TSBooleanKeyword":case"TSBigIntKeyword":case"TSIntrinsicKeyword":case"TSNeverKeyword":case"TSNullKeyword":case"TSNumberKeyword":case"TSObjectKeyword":case"TSStringKeyword":case"TSSymbolKeyword":case"TSUndefinedKeyword":case"TSUnknownKeyword":case"TSVoidKeyword":case"TSThisType":case"TSFunctionType":case"TSConstructorType":case"TSTypeReference":case"TSTypePredicate":case"TSTypeQuery":case"TSTypeLiteral":case"TSArrayType":case"TSTupleType":case"TSOptionalType":case"TSRestType":case"TSNamedTupleMember":case"TSUnionType":case"TSIntersectionType":case"TSConditionalType":case"TSInferType":case"TSParenthesizedType":case"TSTypeOperator":case"TSIndexedAccessType":case"TSMappedType":case"TSTemplateLiteralType":case"TSLiteralType":case"TSExpressionWithTypeArguments":case"TSInterfaceDeclaration":case"TSInterfaceBody":case"TSTypeAliasDeclaration":case"TSInstantiationExpression":case"TSAsExpression":case"TSSatisfiesExpression":case"TSTypeAssertion":case"TSEnumBody":case"TSEnumDeclaration":case"TSEnumMember":case"TSModuleDeclaration":case"TSModuleBlock":case"TSImportType":case"TSImportEqualsDeclaration":case"TSExternalModuleReference":case"TSNonNullExpression":case"TSExportAssignment":case"TSNamespaceExportDeclaration":case"TSTypeAnnotation":case"TSTypeParameterInstantiation":case"TSTypeParameterDeclaration":case"TSTypeParameter":break;default:return!1}return t==null||(0,P.default)(e,t)}function V8(e,t){if(!e)return!1;switch(e.type){case"TSCallSignatureDeclaration":case"TSConstructSignatureDeclaration":case"TSPropertySignature":case"TSMethodSignature":case"TSIndexSignature":break;default:return!1}return t==null||(0,P.default)(e,t)}function $8(e,t){if(!e)return!1;switch(e.type){case"TSAnyKeyword":case"TSBooleanKeyword":case"TSBigIntKeyword":case"TSIntrinsicKeyword":case"TSNeverKeyword":case"TSNullKeyword":case"TSNumberKeyword":case"TSObjectKeyword":case"TSStringKeyword":case"TSSymbolKeyword":case"TSUndefinedKeyword":case"TSUnknownKeyword":case"TSVoidKeyword":case"TSThisType":case"TSFunctionType":case"TSConstructorType":case"TSTypeReference":case"TSTypePredicate":case"TSTypeQuery":case"TSTypeLiteral":case"TSArrayType":case"TSTupleType":case"TSOptionalType":case"TSRestType":case"TSUnionType":case"TSIntersectionType":case"TSConditionalType":case"TSInferType":case"TSParenthesizedType":case"TSTypeOperator":case"TSIndexedAccessType":case"TSMappedType":case"TSTemplateLiteralType":case"TSLiteralType":case"TSExpressionWithTypeArguments":case"TSImportType":break;default:return!1}return t==null||(0,P.default)(e,t)}function W8(e,t){if(!e)return!1;switch(e.type){case"TSAnyKeyword":case"TSBooleanKeyword":case"TSBigIntKeyword":case"TSIntrinsicKeyword":case"TSNeverKeyword":case"TSNullKeyword":case"TSNumberKeyword":case"TSObjectKeyword":case"TSStringKeyword":case"TSSymbolKeyword":case"TSUndefinedKeyword":case"TSUnknownKeyword":case"TSVoidKeyword":case"TSThisType":case"TSTemplateLiteralType":case"TSLiteralType":break;default:return!1}return t==null||(0,P.default)(e,t)}function K8(e,t){return(0,Xa.default)("isNumberLiteral","isNumericLiteral"),!e||e.type!=="NumberLiteral"?!1:t==null||(0,P.default)(e,t)}function G8(e,t){return(0,Xa.default)("isRegexLiteral","isRegExpLiteral"),!e||e.type!=="RegexLiteral"?!1:t==null||(0,P.default)(e,t)}function H8(e,t){return(0,Xa.default)("isRestProperty","isRestElement"),!e||e.type!=="RestProperty"?!1:t==null||(0,P.default)(e,t)}function Y8(e,t){return(0,Xa.default)("isSpreadProperty","isSpreadElement"),!e||e.type!=="SpreadProperty"?!1:t==null||(0,P.default)(e,t)}function X8(e,t){return(0,Xa.default)("isModuleDeclaration","isImportOrExportDeclaration"),ME(e,t)}});var Hf=T(Gf=>{"use strict";Object.defineProperty(Gf,"__esModule",{value:!0});Gf.default=J8;var Ja=Nt();function J8(e,t,r){if(!(0,Ja.isMemberExpression)(e))return!1;let n=Array.isArray(t)?t:t.split("."),s=[],i;for(i=e;(0,Ja.isMemberExpression)(i);i=i.object)s.push(i.property);if(s.push(i),s.length<n.length||!r&&s.length>n.length)return!1;for(let a=0,o=s.length-1;a<n.length;a++,o--){let l=s[o],u;if((0,Ja.isIdentifier)(l))u=l.name;else if((0,Ja.isStringLiteral)(l))u=l.value;else if((0,Ja.isThisExpression)(l))u="this";else return!1;if(n[a]!==u)return!1}return!0}});var Xf=T(Yf=>{"use strict";Object.defineProperty(Yf,"__esModule",{value:!0});Yf.default=Q8;var z8=Hf();function Q8(e,t){let r=e.split(".");return n=>(0,z8.default)(n,r,t)}});var RE=T(Ll=>{"use strict";Object.defineProperty(Ll,"__esModule",{value:!0});Ll.default=void 0;var Z8=Xf(),e4=(0,Z8.default)("React.Component"),KTe=Ll.default=e4});var qE=T(Jf=>{"use strict";Object.defineProperty(Jf,"__esModule",{value:!0});Jf.default=t4;function t4(e){return!!e&&/^[a-z]/.test(e)}});var jl=T(zf=>{"use strict";Object.defineProperty(zf,"__esModule",{value:!0});zf.default=r4;var UE=Fr();function r4(e,t){if(e===t)return!0;if(e==null||UE.ALIAS_KEYS[t])return!1;let r=UE.FLIPPED_ALIAS_KEYS[t];if(r){if(r[0]===e)return!0;for(let n of r)if(e===n)return!0}return!1}});var Zf=T(Qf=>{"use strict";Object.defineProperty(Qf,"__esModule",{value:!0});Qf.default=s4;var n4=Fr();function s4(e,t){if(e===t)return!0;let r=n4.PLACEHOLDERS_ALIAS[e];if(r){for(let n of r)if(t===n)return!0}return!1}});var Ui=T(ed=>{"use strict";Object.defineProperty(ed,"__esModule",{value:!0});ed.default=u4;var i4=Fl(),a4=jl(),o4=Zf(),l4=Fr();function u4(e,t,r){return t?(0,a4.default)(t.type,e)?r===void 0?!0:(0,i4.default)(t,r):!r&&t.type==="Placeholder"&&e in l4.FLIPPED_ALIAS_KEYS?(0,o4.default)(t.expectedNode,e):!1:!1}});var GE=T(za=>{"use strict";Object.defineProperty(za,"__esModule",{value:!0});za.isIdentifierChar=KE;za.isIdentifierName=d4;za.isIdentifierStart=WE;var rd="\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0560-\u0588\u05D0-\u05EA\u05EF-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u0860-\u086A\u0870-\u0887\u0889-\u088E\u08A0-\u08C9\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u09FC\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C5D\u0C60\u0C61\u0C80\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D04-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D54-\u0D56\u0D5F-\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E86-\u0E8A\u0E8C-\u0EA3\u0EA5\u0EA7-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u1711\u171F-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1878\u1880-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4C\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1C80-\u1C8A\u1C90-\u1CBA\u1CBD-\u1CBF\u1CE9-\u1CEC\u1CEE-\u1CF3\u1CF5\u1CF6\u1CFA\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2118-\u211D\u2124\u2126\u2128\u212A-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u3005-\u3007\u3021-\u3029\u3031-\u3035\u3038-\u303C\u3041-\u3096\u309B-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312F\u3131-\u318E\u31A0-\u31BF\u31F0-\u31FF\u3400-\u4DBF\u4E00-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6EF\uA717-\uA71F\uA722-\uA788\uA78B-\uA7CD\uA7D0\uA7D1\uA7D3\uA7D5-\uA7DC\uA7F2-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA8FD\uA8FE\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uA9E0-\uA9E4\uA9E6-\uA9EF\uA9FA-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB69\uAB70-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC",VE="\xB7\u0300-\u036F\u0387\u0483-\u0487\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u0610-\u061A\u064B-\u0669\u0670\u06D6-\u06DC\u06DF-\u06E4\u06E7\u06E8\u06EA-\u06ED\u06F0-\u06F9\u0711\u0730-\u074A\u07A6-\u07B0\u07C0-\u07C9\u07EB-\u07F3\u07FD\u0816-\u0819\u081B-\u0823\u0825-\u0827\u0829-\u082D\u0859-\u085B\u0897-\u089F\u08CA-\u08E1\u08E3-\u0903\u093A-\u093C\u093E-\u094F\u0951-\u0957\u0962\u0963\u0966-\u096F\u0981-\u0983\u09BC\u09BE-\u09C4\u09C7\u09C8\u09CB-\u09CD\u09D7\u09E2\u09E3\u09E6-\u09EF\u09FE\u0A01-\u0A03\u0A3C\u0A3E-\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A66-\u0A71\u0A75\u0A81-\u0A83\u0ABC\u0ABE-\u0AC5\u0AC7-\u0AC9\u0ACB-\u0ACD\u0AE2\u0AE3\u0AE6-\u0AEF\u0AFA-\u0AFF\u0B01-\u0B03\u0B3C\u0B3E-\u0B44\u0B47\u0B48\u0B4B-\u0B4D\u0B55-\u0B57\u0B62\u0B63\u0B66-\u0B6F\u0B82\u0BBE-\u0BC2\u0BC6-\u0BC8\u0BCA-\u0BCD\u0BD7\u0BE6-\u0BEF\u0C00-\u0C04\u0C3C\u0C3E-\u0C44\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C62\u0C63\u0C66-\u0C6F\u0C81-\u0C83\u0CBC\u0CBE-\u0CC4\u0CC6-\u0CC8\u0CCA-\u0CCD\u0CD5\u0CD6\u0CE2\u0CE3\u0CE6-\u0CEF\u0CF3\u0D00-\u0D03\u0D3B\u0D3C\u0D3E-\u0D44\u0D46-\u0D48\u0D4A-\u0D4D\u0D57\u0D62\u0D63\u0D66-\u0D6F\u0D81-\u0D83\u0DCA\u0DCF-\u0DD4\u0DD6\u0DD8-\u0DDF\u0DE6-\u0DEF\u0DF2\u0DF3\u0E31\u0E34-\u0E3A\u0E47-\u0E4E\u0E50-\u0E59\u0EB1\u0EB4-\u0EBC\u0EC8-\u0ECE\u0ED0-\u0ED9\u0F18\u0F19\u0F20-\u0F29\u0F35\u0F37\u0F39\u0F3E\u0F3F\u0F71-\u0F84\u0F86\u0F87\u0F8D-\u0F97\u0F99-\u0FBC\u0FC6\u102B-\u103E\u1040-\u1049\u1056-\u1059\u105E-\u1060\u1062-\u1064\u1067-\u106D\u1071-\u1074\u1082-\u108D\u108F-\u109D\u135D-\u135F\u1369-\u1371\u1712-\u1715\u1732-\u1734\u1752\u1753\u1772\u1773\u17B4-\u17D3\u17DD\u17E0-\u17E9\u180B-\u180D\u180F-\u1819\u18A9\u1920-\u192B\u1930-\u193B\u1946-\u194F\u19D0-\u19DA\u1A17-\u1A1B\u1A55-\u1A5E\u1A60-\u1A7C\u1A7F-\u1A89\u1A90-\u1A99\u1AB0-\u1ABD\u1ABF-\u1ACE\u1B00-\u1B04\u1B34-\u1B44\u1B50-\u1B59\u1B6B-\u1B73\u1B80-\u1B82\u1BA1-\u1BAD\u1BB0-\u1BB9\u1BE6-\u1BF3\u1C24-\u1C37\u1C40-\u1C49\u1C50-\u1C59\u1CD0-\u1CD2\u1CD4-\u1CE8\u1CED\u1CF4\u1CF7-\u1CF9\u1DC0-\u1DFF\u200C\u200D\u203F\u2040\u2054\u20D0-\u20DC\u20E1\u20E5-\u20F0\u2CEF-\u2CF1\u2D7F\u2DE0-\u2DFF\u302A-\u302F\u3099\u309A\u30FB\uA620-\uA629\uA66F\uA674-\uA67D\uA69E\uA69F\uA6F0\uA6F1\uA802\uA806\uA80B\uA823-\uA827\uA82C\uA880\uA881\uA8B4-\uA8C5\uA8D0-\uA8D9\uA8E0-\uA8F1\uA8FF-\uA909\uA926-\uA92D\uA947-\uA953\uA980-\uA983\uA9B3-\uA9C0\uA9D0-\uA9D9\uA9E5\uA9F0-\uA9F9\uAA29-\uAA36\uAA43\uAA4C\uAA4D\uAA50-\uAA59\uAA7B-\uAA7D\uAAB0\uAAB2-\uAAB4\uAAB7\uAAB8\uAABE\uAABF\uAAC1\uAAEB-\uAAEF\uAAF5\uAAF6\uABE3-\uABEA\uABEC\uABED\uABF0-\uABF9\uFB1E\uFE00-\uFE0F\uFE20-\uFE2F\uFE33\uFE34\uFE4D-\uFE4F\uFF10-\uFF19\uFF3F\uFF65",c4=new RegExp("["+rd+"]"),p4=new RegExp("["+rd+VE+"]");rd=VE=null;var $E=[0,11,2,25,2,18,2,1,2,14,3,13,35,122,70,52,268,28,4,48,48,31,14,29,6,37,11,29,3,35,5,7,2,4,43,157,19,35,5,35,5,39,9,51,13,10,2,14,2,6,2,1,2,10,2,14,2,6,2,1,4,51,13,310,10,21,11,7,25,5,2,41,2,8,70,5,3,0,2,43,2,1,4,0,3,22,11,22,10,30,66,18,2,1,11,21,11,25,71,55,7,1,65,0,16,3,2,2,2,28,43,28,4,28,36,7,2,27,28,53,11,21,11,18,14,17,111,72,56,50,14,50,14,35,39,27,10,22,251,41,7,1,17,2,60,28,11,0,9,21,43,17,47,20,28,22,13,52,58,1,3,0,14,44,33,24,27,35,30,0,3,0,9,34,4,0,13,47,15,3,22,0,2,0,36,17,2,24,20,1,64,6,2,0,2,3,2,14,2,9,8,46,39,7,3,1,3,21,2,6,2,1,2,4,4,0,19,0,13,4,31,9,2,0,3,0,2,37,2,0,26,0,2,0,45,52,19,3,21,2,31,47,21,1,2,0,185,46,42,3,37,47,21,0,60,42,14,0,72,26,38,6,186,43,117,63,32,7,3,0,3,7,2,1,2,23,16,0,2,0,95,7,3,38,17,0,2,0,29,0,11,39,8,0,22,0,12,45,20,0,19,72,200,32,32,8,2,36,18,0,50,29,113,6,2,1,2,37,22,0,26,5,2,1,2,31,15,0,328,18,16,0,2,12,2,33,125,0,80,921,103,110,18,195,2637,96,16,1071,18,5,26,3994,6,582,6842,29,1763,568,8,30,18,78,18,29,19,47,17,3,32,20,6,18,433,44,212,63,129,74,6,0,67,12,65,1,2,0,29,6135,9,1237,42,9,8936,3,2,6,2,1,2,290,16,0,30,2,3,0,15,3,9,395,2309,106,6,12,4,8,8,9,5991,84,2,70,2,1,3,0,3,1,3,3,2,11,2,0,2,6,2,64,2,3,3,7,2,6,2,27,2,3,2,4,2,0,4,6,2,339,3,24,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,7,1845,30,7,5,262,61,147,44,11,6,17,0,322,29,19,43,485,27,229,29,3,0,496,6,2,3,2,1,2,14,2,196,60,67,8,0,1205,3,2,26,2,1,2,0,3,0,2,9,2,3,2,0,2,0,7,0,5,0,2,0,2,0,2,2,2,1,2,0,3,0,2,0,2,0,2,0,2,0,2,1,2,0,3,3,2,6,2,3,2,3,2,0,2,9,2,16,6,2,2,4,2,16,4421,42719,33,4153,7,221,3,5761,15,7472,16,621,2467,541,1507,4938,6,4191],f4=[509,0,227,0,150,4,294,9,1368,2,2,1,6,3,41,2,5,0,166,1,574,3,9,9,7,9,32,4,318,1,80,3,71,10,50,3,123,2,54,14,32,10,3,1,11,3,46,10,8,0,46,9,7,2,37,13,2,9,6,1,45,0,13,2,49,13,9,3,2,11,83,11,7,0,3,0,158,11,6,9,7,3,56,1,2,6,3,1,3,2,10,0,11,1,3,6,4,4,68,8,2,0,3,0,2,3,2,4,2,0,15,1,83,17,10,9,5,0,82,19,13,9,214,6,3,8,28,1,83,16,16,9,82,12,9,9,7,19,58,14,5,9,243,14,166,9,71,5,2,1,3,3,2,0,2,1,13,9,120,6,3,6,4,0,29,9,41,6,2,3,9,0,10,10,47,15,343,9,54,7,2,7,17,9,57,21,2,13,123,5,4,0,2,1,2,6,2,0,9,9,49,4,2,1,2,4,9,9,330,3,10,1,2,0,49,6,4,4,14,10,5350,0,7,14,11465,27,2343,9,87,9,39,4,60,6,26,9,535,9,470,0,2,54,8,3,82,0,12,1,19628,1,4178,9,519,45,3,22,543,4,4,5,9,7,3,6,31,3,149,2,1418,49,513,54,5,49,9,0,15,0,23,4,2,14,1361,6,2,16,3,6,2,1,2,4,101,0,161,6,10,9,357,0,62,13,499,13,245,1,2,9,726,6,110,6,6,9,4759,9,787719,239];function td(e,t){let r=65536;for(let n=0,s=t.length;n<s;n+=2){if(r+=t[n],r>e)return!1;if(r+=t[n+1],r>=e)return!0}return!1}function WE(e){return e<65?e===36:e<=90?!0:e<97?e===95:e<=122?!0:e<=65535?e>=170&&c4.test(String.fromCharCode(e)):td(e,$E)}function KE(e){return e<48?e===36:e<58?!0:e<65?!1:e<=90?!0:e<97?e===95:e<=122?!0:e<=65535?e>=170&&p4.test(String.fromCharCode(e)):td(e,$E)||td(e,f4)}function d4(e){let t=!0;for(let r=0;r<e.length;r++){let n=e.charCodeAt(r);if((n&64512)===55296&&r+1<e.length){let s=e.charCodeAt(++r);(s&64512)===56320&&(n=65536+((n&1023)<<10)+(s&1023))}if(t){if(t=!1,!WE(n))return!1}else if(!KE(n))return!1}return!t}});var JE=T(Qs=>{"use strict";Object.defineProperty(Qs,"__esModule",{value:!0});Qs.isKeyword=b4;Qs.isReservedWord=HE;Qs.isStrictBindOnlyReservedWord=XE;Qs.isStrictBindReservedWord=g4;Qs.isStrictReservedWord=YE;var nd={keyword:["break","case","catch","continue","debugger","default","do","else","finally","for","function","if","return","switch","throw","try","var","const","while","with","new","this","super","class","extends","export","import","null","true","false","in","instanceof","typeof","void","delete"],strict:["implements","interface","let","package","private","protected","public","static","yield"],strictBind:["eval","arguments"]},h4=new Set(nd.keyword),m4=new Set(nd.strict),y4=new Set(nd.strictBind);function HE(e,t){return t&&e==="await"||e==="enum"}function YE(e,t){return HE(e,t)||m4.has(e)}function XE(e){return y4.has(e)}function g4(e,t){return YE(e,t)||XE(e)}function b4(e){return h4.has(e)}});var Vi=T(yn=>{"use strict";Object.defineProperty(yn,"__esModule",{value:!0});Object.defineProperty(yn,"isIdentifierChar",{enumerable:!0,get:function(){return sd.isIdentifierChar}});Object.defineProperty(yn,"isIdentifierName",{enumerable:!0,get:function(){return sd.isIdentifierName}});Object.defineProperty(yn,"isIdentifierStart",{enumerable:!0,get:function(){return sd.isIdentifierStart}});Object.defineProperty(yn,"isKeyword",{enumerable:!0,get:function(){return Qa.isKeyword}});Object.defineProperty(yn,"isReservedWord",{enumerable:!0,get:function(){return Qa.isReservedWord}});Object.defineProperty(yn,"isStrictBindOnlyReservedWord",{enumerable:!0,get:function(){return Qa.isStrictBindOnlyReservedWord}});Object.defineProperty(yn,"isStrictBindReservedWord",{enumerable:!0,get:function(){return Qa.isStrictBindReservedWord}});Object.defineProperty(yn,"isStrictReservedWord",{enumerable:!0,get:function(){return Qa.isStrictReservedWord}});var sd=GE(),Qa=JE()});var $i=T(ad=>{"use strict";Object.defineProperty(ad,"__esModule",{value:!0});ad.default=E4;var id=Vi();function E4(e,t=!0){return typeof e!="string"||t&&((0,id.isKeyword)(e)||(0,id.isStrictReservedWord)(e,!0))?!1:(0,id.isIdentifierName)(e)}});var eT=T(Za=>{"use strict";Object.defineProperty(Za,"__esModule",{value:!0});Za.readCodePoint=ZE;Za.readInt=QE;Za.readStringContents=S4;var T4=function(t){return t>=48&&t<=57},zE={decBinOct:new Set([46,66,69,79,95,98,101,111]),hex:new Set([46,88,95,120])},Ml={bin:e=>e===48||e===49,oct:e=>e>=48&&e<=55,dec:e=>e>=48&&e<=57,hex:e=>e>=48&&e<=57||e>=65&&e<=70||e>=97&&e<=102};function S4(e,t,r,n,s,i){let a=r,o=n,l=s,u="",c=null,p=r,{length:d}=t;for(;;){if(r>=d){i.unterminated(a,o,l),u+=t.slice(p,r);break}let y=t.charCodeAt(r);if(x4(e,y,t,r)){u+=t.slice(p,r);break}if(y===92){u+=t.slice(p,r);let E=v4(t,r,n,s,e==="template",i);E.ch===null&&!c?c={pos:r,lineStart:n,curLine:s}:u+=E.ch,{pos:r,lineStart:n,curLine:s}=E,p=r}else y===8232||y===8233?(++r,++s,n=r):y===10||y===13?e==="template"?(u+=t.slice(p,r)+`
`,++r,y===13&&t.charCodeAt(r)===10&&++r,++s,p=n=r):i.unterminated(a,o,l):++r}return{pos:r,str:u,firstInvalidLoc:c,lineStart:n,curLine:s,containsInvalid:!!c}}function x4(e,t,r,n){return e==="template"?t===96||t===36&&r.charCodeAt(n+1)===123:t===(e==="double"?34:39)}function v4(e,t,r,n,s,i){let a=!s;t++;let o=u=>({pos:t,ch:u,lineStart:r,curLine:n}),l=e.charCodeAt(t++);switch(l){case 110:return o(`
`);case 114:return o("\r");case 120:{let u;return{code:u,pos:t}=od(e,t,r,n,2,!1,a,i),o(u===null?null:String.fromCharCode(u))}case 117:{let u;return{code:u,pos:t}=ZE(e,t,r,n,a,i),o(u===null?null:String.fromCodePoint(u))}case 116:return o("	");case 98:return o("\b");case 118:return o("\v");case 102:return o("\f");case 13:e.charCodeAt(t)===10&&++t;case 10:r=t,++n;case 8232:case 8233:return o("");case 56:case 57:if(s)return o(null);i.strictNumericEscape(t-1,r,n);default:if(l>=48&&l<=55){let u=t-1,p=/^[0-7]+/.exec(e.slice(u,t+2))[0],d=parseInt(p,8);d>255&&(p=p.slice(0,-1),d=parseInt(p,8)),t+=p.length-1;let y=e.charCodeAt(t);if(p!=="0"||y===56||y===57){if(s)return o(null);i.strictNumericEscape(u,r,n)}return o(String.fromCharCode(d))}return o(String.fromCharCode(l))}}function od(e,t,r,n,s,i,a,o){let l=t,u;return{n:u,pos:t}=QE(e,t,r,n,16,s,i,!1,o,!a),u===null&&(a?o.invalidEscapeSequence(l,r,n):t=l-1),{code:u,pos:t}}function QE(e,t,r,n,s,i,a,o,l,u){let c=t,p=s===16?zE.hex:zE.decBinOct,d=s===16?Ml.hex:s===10?Ml.dec:s===8?Ml.oct:Ml.bin,y=!1,E=0;for(let f=0,m=i==null?1/0:i;f<m;++f){let g=e.charCodeAt(t),D;if(g===95&&o!=="bail"){let _=e.charCodeAt(t-1),O=e.charCodeAt(t+1);if(o){if(Number.isNaN(O)||!d(O)||p.has(_)||p.has(O)){if(u)return{n:null,pos:t};l.unexpectedNumericSeparator(t,r,n)}}else{if(u)return{n:null,pos:t};l.numericSeparatorInEscapeSequence(t,r,n)}++t;continue}if(g>=97?D=g-97+10:g>=65?D=g-65+10:T4(g)?D=g-48:D=1/0,D>=s){if(D<=9&&u)return{n:null,pos:t};if(D<=9&&l.invalidDigit(t,r,n,s))D=0;else if(a)D=0,y=!0;else break}++t,E=E*s+D}return t===c||i!=null&&t-c!==i||y?{n:null,pos:t}:{n:E,pos:t}}function ZE(e,t,r,n,s,i){let a=e.charCodeAt(t),o;if(a===123){if(++t,{code:o,pos:t}=od(e,t,r,n,e.indexOf("}",t)-t,!0,s,i),++t,o!==null&&o>1114111)if(s)i.invalidCodePoint(t,r,n);else return{code:null,pos:t}}else({code:o,pos:t}=od(e,t,r,n,4,!1,s,i));return{code:o,pos:t}}});var hs=T(Re=>{"use strict";Object.defineProperty(Re,"__esModule",{value:!0});Re.UPDATE_OPERATORS=Re.UNARY_OPERATORS=Re.STRING_UNARY_OPERATORS=Re.STATEMENT_OR_BLOCK_KEYS=Re.NUMBER_UNARY_OPERATORS=Re.NUMBER_BINARY_OPERATORS=Re.NOT_LOCAL_BINDING=Re.LOGICAL_OPERATORS=Re.INHERIT_KEYS=Re.FOR_INIT_KEYS=Re.FLATTENABLE_KEYS=Re.EQUALITY_BINARY_OPERATORS=Re.COMPARISON_BINARY_OPERATORS=Re.COMMENT_KEYS=Re.BOOLEAN_UNARY_OPERATORS=Re.BOOLEAN_NUMBER_BINARY_OPERATORS=Re.BOOLEAN_BINARY_OPERATORS=Re.BLOCK_SCOPED_SYMBOL=Re.BINARY_OPERATORS=Re.ASSIGNMENT_OPERATORS=void 0;var rSe=Re.STATEMENT_OR_BLOCK_KEYS=["consequent","body","alternate"],nSe=Re.FLATTENABLE_KEYS=["body","expressions"],sSe=Re.FOR_INIT_KEYS=["left","init"],iSe=Re.COMMENT_KEYS=["leadingComments","trailingComments","innerComments"],P4=Re.LOGICAL_OPERATORS=["||","&&","??"],aSe=Re.UPDATE_OPERATORS=["++","--"],A4=Re.BOOLEAN_NUMBER_BINARY_OPERATORS=[">","<",">=","<="],C4=Re.EQUALITY_BINARY_OPERATORS=["==","===","!=","!=="],D4=Re.COMPARISON_BINARY_OPERATORS=[...C4,"in","instanceof"],w4=Re.BOOLEAN_BINARY_OPERATORS=[...D4,...A4],tT=Re.NUMBER_BINARY_OPERATORS=["-","/","%","*","**","&","|",">>",">>>","<<","^"],oSe=Re.BINARY_OPERATORS=["+",...tT,...w4,"|>"],lSe=Re.ASSIGNMENT_OPERATORS=["=","+=",...tT.map(e=>e+"="),...P4.map(e=>e+"=")],I4=Re.BOOLEAN_UNARY_OPERATORS=["delete","!"],_4=Re.NUMBER_UNARY_OPERATORS=["+","-","~"],O4=Re.STRING_UNARY_OPERATORS=["typeof"],uSe=Re.UNARY_OPERATORS=["void","throw",...I4,..._4,...O4],cSe=Re.INHERIT_KEYS={optional:["typeAnnotation","typeParameters","returnType"],force:["start","loc","end"]},pSe=Re.BLOCK_SCOPED_SYMBOL=Symbol.for("var used to be block scoped"),fSe=Re.NOT_LOCAL_BINDING=Symbol.for("should not be considered a local binding")});var gn=T(it=>{"use strict";Object.defineProperty(it,"__esModule",{value:!0});it.VISITOR_KEYS=it.NODE_PARENT_VALIDATIONS=it.NODE_FIELDS=it.FLIPPED_ALIAS_KEYS=it.DEPRECATED_KEYS=it.BUILDER_KEYS=it.ALIAS_KEYS=void 0;it.arrayOf=nT;it.arrayOfType=sT;it.assertEach=iT;it.assertNodeOrValueType=$4;it.assertNodeType=Ul;it.assertOneOf=V4;it.assertOptionalChainStart=K4;it.assertShape=W4;it.assertValueType=pd;it.chain=aT;it.default=oT;it.defineAliasedType=Y4;it.validate=cd;it.validateArrayOfType=U4;it.validateOptional=R4;it.validateOptionalType=q4;it.validateType=M4;var rT=Ui(),ql=Vl(),N4=it.VISITOR_KEYS={},B4=it.ALIAS_KEYS={},ld=it.FLIPPED_ALIAS_KEYS={},k4=it.NODE_FIELDS={},F4=it.BUILDER_KEYS={},L4=it.DEPRECATED_KEYS={},j4=it.NODE_PARENT_VALIDATIONS={};function Rl(e){return Array.isArray(e)?"array":e===null?"null":typeof e}function cd(e){return{validate:e}}function M4(...e){return cd(Ul(...e))}function R4(e){return{validate:e,optional:!0}}function q4(...e){return{validate:Ul(...e),optional:!0}}function nT(e){return aT(pd("array"),iT(e))}function sT(...e){return nT(Ul(...e))}function U4(...e){return cd(sT(...e))}function iT(e){let t=process.env.BABEL_TYPES_8_BREAKING?ql.validateChild:()=>{};function r(n,s,i){if(Array.isArray(i))for(let a=0;a<i.length;a++){let o=`${s}[${a}]`,l=i[a];e(n,o,l),t(n,o,l)}}return r.each=e,r}function V4(...e){function t(r,n,s){if(!e.includes(s))throw new TypeError(`Property ${n} expected value to be one of ${JSON.stringify(e)} but got ${JSON.stringify(s)}`)}return t.oneOf=e,t}function Ul(...e){function t(r,n,s){for(let i of e)if((0,rT.default)(i,s)){(0,ql.validateChild)(r,n,s);return}throw new TypeError(`Property ${n} of ${r.type} expected node to be of a type ${JSON.stringify(e)} but instead got ${JSON.stringify(s==null?void 0:s.type)}`)}return t.oneOfNodeTypes=e,t}function $4(...e){function t(r,n,s){for(let i of e)if(Rl(s)===i||(0,rT.default)(i,s)){(0,ql.validateChild)(r,n,s);return}throw new TypeError(`Property ${n} of ${r.type} expected node to be of a type ${JSON.stringify(e)} but instead got ${JSON.stringify(s==null?void 0:s.type)}`)}return t.oneOfNodeOrValueTypes=e,t}function pd(e){function t(r,n,s){if(!(Rl(s)===e))throw new TypeError(`Property ${n} expected type of ${e} but got ${Rl(s)}`)}return t.type=e,t}function W4(e){function t(r,n,s){let i=[];for(let a of Object.keys(e))try{(0,ql.validateField)(r,a,s[a],e[a])}catch(o){if(o instanceof TypeError){i.push(o.message);continue}throw o}if(i.length)throw new TypeError(`Property ${n} of ${r.type} expected to have the following:
${i.join(`
`)}`)}return t.shapeOf=e,t}function K4(){function e(t){var r;let n=t;for(;t;){let{type:s}=n;if(s==="OptionalCallExpression"){if(n.optional)return;n=n.callee;continue}if(s==="OptionalMemberExpression"){if(n.optional)return;n=n.object;continue}break}throw new TypeError(`Non-optional ${t.type} must chain from an optional OptionalMemberExpression or OptionalCallExpression. Found chain from ${(r=n)==null?void 0:r.type}`)}return e}function aT(...e){function t(...r){for(let n of e)n(...r)}if(t.chainOf=e,e.length>=2&&"type"in e[0]&&e[0].type==="array"&&!("each"in e[1]))throw new Error('An assertValueType("array") validator can only be followed by an assertEach(...) validator.');return t}var G4=new Set(["aliases","builder","deprecatedAlias","fields","inherits","visitor","validate"]),H4=new Set(["default","optional","deprecated","validate"]),ud={};function Y4(...e){return(t,r={})=>{let n=r.aliases;if(!n){var s;r.inherits&&(n=(s=ud[r.inherits].aliases)==null?void 0:s.slice()),n!=null||(n=[]),r.aliases=n}let i=e.filter(a=>!n.includes(a));n.unshift(...i),oT(t,r)}}function oT(e,t={}){let r=t.inherits&&ud[t.inherits]||{},n=t.fields;if(!n&&(n={},r.fields)){let o=Object.getOwnPropertyNames(r.fields);for(let l of o){let u=r.fields[l],c=u.default;if(Array.isArray(c)?c.length>0:c&&typeof c=="object")throw new Error("field defaults can only be primitives or empty arrays currently");n[l]={default:Array.isArray(c)?[]:c,optional:u.optional,deprecated:u.deprecated,validate:u.validate}}}let s=t.visitor||r.visitor||[],i=t.aliases||r.aliases||[],a=t.builder||r.builder||t.visitor||[];for(let o of Object.keys(t))if(!G4.has(o))throw new Error(`Unknown type option "${o}" on ${e}`);t.deprecatedAlias&&(L4[t.deprecatedAlias]=e);for(let o of s.concat(a))n[o]=n[o]||{};for(let o of Object.keys(n)){let l=n[o];l.default!==void 0&&!a.includes(o)&&(l.optional=!0),l.default===void 0?l.default=null:!l.validate&&l.default!=null&&(l.validate=pd(Rl(l.default)));for(let u of Object.keys(l))if(!H4.has(u))throw new Error(`Unknown field key "${u}" on ${e}.${o}`)}N4[e]=t.visitor=s,F4[e]=t.builder=a,k4[e]=t.fields=n,B4[e]=t.aliases=i,i.forEach(o=>{ld[o]=ld[o]||[],ld[o].push(e)}),t.validate&&(j4[e]=t.validate),ud[e]=t}});var to=T(ir=>{"use strict";Object.defineProperty(ir,"__esModule",{value:!0});ir.patternLikeCommon=ir.importAttributes=ir.functionTypeAnnotationCommon=ir.functionDeclarationCommon=ir.functionCommon=ir.classMethodOrPropertyCommon=ir.classMethodOrDeclareMethodCommon=void 0;var dr=Ui(),X4=$i(),lT=Vi(),J4=eT(),eo=hs(),C=gn(),ee=(0,C.defineAliasedType)("Standardized");ee("ArrayExpression",{fields:{elements:{validate:(0,C.arrayOf)((0,C.assertNodeOrValueType)("null","Expression","SpreadElement")),default:process.env.BABEL_TYPES_8_BREAKING?void 0:[]}},visitor:["elements"],aliases:["Expression"]});ee("AssignmentExpression",{fields:{operator:{validate:process.env.BABEL_TYPES_8_BREAKING?Object.assign(function(){let e=(0,C.assertOneOf)(...eo.ASSIGNMENT_OPERATORS),t=(0,C.assertOneOf)("=");return function(r,n,s){((0,dr.default)("Pattern",r.left)?t:e)(r,n,s)}}(),{type:"string"}):(0,C.assertValueType)("string")},left:{validate:process.env.BABEL_TYPES_8_BREAKING?(0,C.assertNodeType)("Identifier","MemberExpression","OptionalMemberExpression","ArrayPattern","ObjectPattern","TSAsExpression","TSSatisfiesExpression","TSTypeAssertion","TSNonNullExpression"):(0,C.assertNodeType)("LVal","OptionalMemberExpression")},right:{validate:(0,C.assertNodeType)("Expression")}},builder:["operator","left","right"],visitor:["left","right"],aliases:["Expression"]});ee("BinaryExpression",{builder:["operator","left","right"],fields:{operator:{validate:(0,C.assertOneOf)(...eo.BINARY_OPERATORS)},left:{validate:function(){let e=(0,C.assertNodeType)("Expression"),t=(0,C.assertNodeType)("Expression","PrivateName");return Object.assign(function(n,s,i){(n.operator==="in"?t:e)(n,s,i)},{oneOfNodeTypes:["Expression","PrivateName"]})}()},right:{validate:(0,C.assertNodeType)("Expression")}},visitor:["left","right"],aliases:["Binary","Expression"]});ee("InterpreterDirective",{builder:["value"],fields:{value:{validate:(0,C.assertValueType)("string")}}});ee("Directive",{visitor:["value"],fields:{value:{validate:(0,C.assertNodeType)("DirectiveLiteral")}}});ee("DirectiveLiteral",{builder:["value"],fields:{value:{validate:(0,C.assertValueType)("string")}}});ee("BlockStatement",{builder:["body","directives"],visitor:["directives","body"],fields:{directives:{validate:(0,C.arrayOfType)("Directive"),default:[]},body:(0,C.validateArrayOfType)("Statement")},aliases:["Scopable","BlockParent","Block","Statement"]});ee("BreakStatement",{visitor:["label"],fields:{label:{validate:(0,C.assertNodeType)("Identifier"),optional:!0}},aliases:["Statement","Terminatorless","CompletionStatement"]});ee("CallExpression",{visitor:["callee","arguments","typeParameters","typeArguments"],builder:["callee","arguments"],aliases:["Expression"],fields:Object.assign({callee:{validate:(0,C.assertNodeType)("Expression","Super","V8IntrinsicIdentifier")},arguments:(0,C.validateArrayOfType)("Expression","SpreadElement","ArgumentPlaceholder"),typeArguments:{validate:(0,C.assertNodeType)("TypeParameterInstantiation"),optional:!0}},{optional:{validate:(0,C.assertValueType)("boolean"),optional:!0},typeParameters:{validate:(0,C.assertNodeType)("TSTypeParameterInstantiation"),optional:!0}},process.env.BABEL_TYPES_8_BREAKING?{}:{optional:{validate:(0,C.assertValueType)("boolean"),optional:!0}})});ee("CatchClause",{visitor:["param","body"],fields:{param:{validate:(0,C.assertNodeType)("Identifier","ArrayPattern","ObjectPattern"),optional:!0},body:{validate:(0,C.assertNodeType)("BlockStatement")}},aliases:["Scopable","BlockParent"]});ee("ConditionalExpression",{visitor:["test","consequent","alternate"],fields:{test:{validate:(0,C.assertNodeType)("Expression")},consequent:{validate:(0,C.assertNodeType)("Expression")},alternate:{validate:(0,C.assertNodeType)("Expression")}},aliases:["Expression","Conditional"]});ee("ContinueStatement",{visitor:["label"],fields:{label:{validate:(0,C.assertNodeType)("Identifier"),optional:!0}},aliases:["Statement","Terminatorless","CompletionStatement"]});ee("DebuggerStatement",{aliases:["Statement"]});ee("DoWhileStatement",{builder:["test","body"],visitor:["body","test"],fields:{test:{validate:(0,C.assertNodeType)("Expression")},body:{validate:(0,C.assertNodeType)("Statement")}},aliases:["Statement","BlockParent","Loop","While","Scopable"]});ee("EmptyStatement",{aliases:["Statement"]});ee("ExpressionStatement",{visitor:["expression"],fields:{expression:{validate:(0,C.assertNodeType)("Expression")}},aliases:["Statement","ExpressionWrapper"]});ee("File",{builder:["program","comments","tokens"],visitor:["program"],fields:{program:{validate:(0,C.assertNodeType)("Program")},comments:{validate:process.env.BABEL_TYPES_8_BREAKING?(0,C.assertEach)((0,C.assertNodeType)("CommentBlock","CommentLine")):Object.assign(()=>{},{each:{oneOfNodeTypes:["CommentBlock","CommentLine"]}}),optional:!0},tokens:{validate:(0,C.assertEach)(Object.assign(()=>{},{type:"any"})),optional:!0}}});ee("ForInStatement",{visitor:["left","right","body"],aliases:["Scopable","Statement","For","BlockParent","Loop","ForXStatement"],fields:{left:{validate:process.env.BABEL_TYPES_8_BREAKING?(0,C.assertNodeType)("VariableDeclaration","Identifier","MemberExpression","ArrayPattern","ObjectPattern","TSAsExpression","TSSatisfiesExpression","TSTypeAssertion","TSNonNullExpression"):(0,C.assertNodeType)("VariableDeclaration","LVal")},right:{validate:(0,C.assertNodeType)("Expression")},body:{validate:(0,C.assertNodeType)("Statement")}}});ee("ForStatement",{visitor:["init","test","update","body"],aliases:["Scopable","Statement","For","BlockParent","Loop"],fields:{init:{validate:(0,C.assertNodeType)("VariableDeclaration","Expression"),optional:!0},test:{validate:(0,C.assertNodeType)("Expression"),optional:!0},update:{validate:(0,C.assertNodeType)("Expression"),optional:!0},body:{validate:(0,C.assertNodeType)("Statement")}}});var Wi=()=>({params:(0,C.validateArrayOfType)("Identifier","Pattern","RestElement"),generator:{default:!1},async:{default:!1}});ir.functionCommon=Wi;var Zs=()=>({returnType:{validate:(0,C.assertNodeType)("TypeAnnotation","TSTypeAnnotation","Noop"),optional:!0},typeParameters:{validate:(0,C.assertNodeType)("TypeParameterDeclaration","TSTypeParameterDeclaration","Noop"),optional:!0}});ir.functionTypeAnnotationCommon=Zs;var uT=()=>Object.assign({},Wi(),{declare:{validate:(0,C.assertValueType)("boolean"),optional:!0},id:{validate:(0,C.assertNodeType)("Identifier"),optional:!0}});ir.functionDeclarationCommon=uT;ee("FunctionDeclaration",{builder:["id","params","body","generator","async"],visitor:["id","typeParameters","params","predicate","returnType","body"],fields:Object.assign({},uT(),Zs(),{body:{validate:(0,C.assertNodeType)("BlockStatement")},predicate:{validate:(0,C.assertNodeType)("DeclaredPredicate","InferredPredicate"),optional:!0}}),aliases:["Scopable","Function","BlockParent","FunctionParent","Statement","Pureish","Declaration"],validate:process.env.BABEL_TYPES_8_BREAKING?function(){let e=(0,C.assertNodeType)("Identifier");return function(t,r,n){(0,dr.default)("ExportDefaultDeclaration",t)||e(n,"id",n.id)}}():void 0});ee("FunctionExpression",{inherits:"FunctionDeclaration",aliases:["Scopable","Function","BlockParent","FunctionParent","Expression","Pureish"],fields:Object.assign({},Wi(),Zs(),{id:{validate:(0,C.assertNodeType)("Identifier"),optional:!0},body:{validate:(0,C.assertNodeType)("BlockStatement")},predicate:{validate:(0,C.assertNodeType)("DeclaredPredicate","InferredPredicate"),optional:!0}})});var Ki=()=>({typeAnnotation:{validate:(0,C.assertNodeType)("TypeAnnotation","TSTypeAnnotation","Noop"),optional:!0},optional:{validate:(0,C.assertValueType)("boolean"),optional:!0},decorators:{validate:(0,C.arrayOfType)("Decorator"),optional:!0}});ir.patternLikeCommon=Ki;ee("Identifier",{builder:["name"],visitor:["typeAnnotation","decorators"],aliases:["Expression","PatternLike","LVal","TSEntityName"],fields:Object.assign({},Ki(),{name:{validate:process.env.BABEL_TYPES_8_BREAKING?(0,C.chain)((0,C.assertValueType)("string"),Object.assign(function(e,t,r){if(!(0,X4.default)(r,!1))throw new TypeError(`"${r}" is not a valid identifier name`)},{type:"string"})):(0,C.assertValueType)("string")}}),validate:process.env.BABEL_TYPES_8_BREAKING?function(e,t,r){let n=/\.(\w+)$/.exec(t);if(!n)return;let[,s]=n,i={computed:!1};if(s==="property"){if((0,dr.default)("MemberExpression",e,i)||(0,dr.default)("OptionalMemberExpression",e,i))return}else if(s==="key"){if((0,dr.default)("Property",e,i)||(0,dr.default)("Method",e,i))return}else if(s==="exported"){if((0,dr.default)("ExportSpecifier",e))return}else if(s==="imported"){if((0,dr.default)("ImportSpecifier",e,{imported:r}))return}else if(s==="meta"&&(0,dr.default)("MetaProperty",e,{meta:r}))return;if(((0,lT.isKeyword)(r.name)||(0,lT.isReservedWord)(r.name,!1))&&r.name!=="this")throw new TypeError(`"${r.name}" is not a valid identifier`)}:void 0});ee("IfStatement",{visitor:["test","consequent","alternate"],aliases:["Statement","Conditional"],fields:{test:{validate:(0,C.assertNodeType)("Expression")},consequent:{validate:(0,C.assertNodeType)("Statement")},alternate:{optional:!0,validate:(0,C.assertNodeType)("Statement")}}});ee("LabeledStatement",{visitor:["label","body"],aliases:["Statement"],fields:{label:{validate:(0,C.assertNodeType)("Identifier")},body:{validate:(0,C.assertNodeType)("Statement")}}});ee("StringLiteral",{builder:["value"],fields:{value:{validate:(0,C.assertValueType)("string")}},aliases:["Expression","Pureish","Literal","Immutable"]});ee("NumericLiteral",{builder:["value"],deprecatedAlias:"NumberLiteral",fields:{value:{validate:(0,C.chain)((0,C.assertValueType)("number"),Object.assign(function(e,t,r){if(1/r<0||!Number.isFinite(r)){let n=new Error(`NumericLiterals must be non-negative finite numbers. You can use t.valueToNode(${r}) instead.`)}},{type:"number"}))}},aliases:["Expression","Pureish","Literal","Immutable"]});ee("NullLiteral",{aliases:["Expression","Pureish","Literal","Immutable"]});ee("BooleanLiteral",{builder:["value"],fields:{value:{validate:(0,C.assertValueType)("boolean")}},aliases:["Expression","Pureish","Literal","Immutable"]});ee("RegExpLiteral",{builder:["pattern","flags"],deprecatedAlias:"RegexLiteral",aliases:["Expression","Pureish","Literal"],fields:{pattern:{validate:(0,C.assertValueType)("string")},flags:{validate:process.env.BABEL_TYPES_8_BREAKING?(0,C.chain)((0,C.assertValueType)("string"),Object.assign(function(e,t,r){let n=/[^gimsuy]/.exec(r);if(n)throw new TypeError(`"${n[0]}" is not a valid RegExp flag`)},{type:"string"})):(0,C.assertValueType)("string"),default:""}}});ee("LogicalExpression",{builder:["operator","left","right"],visitor:["left","right"],aliases:["Binary","Expression"],fields:{operator:{validate:(0,C.assertOneOf)(...eo.LOGICAL_OPERATORS)},left:{validate:(0,C.assertNodeType)("Expression")},right:{validate:(0,C.assertNodeType)("Expression")}}});ee("MemberExpression",{builder:["object","property","computed",...process.env.BABEL_TYPES_8_BREAKING?[]:["optional"]],visitor:["object","property"],aliases:["Expression","LVal"],fields:Object.assign({object:{validate:(0,C.assertNodeType)("Expression","Super")},property:{validate:function(){let e=(0,C.assertNodeType)("Identifier","PrivateName"),t=(0,C.assertNodeType)("Expression"),r=function(n,s,i){(n.computed?t:e)(n,s,i)};return r.oneOfNodeTypes=["Expression","Identifier","PrivateName"],r}()},computed:{default:!1}},process.env.BABEL_TYPES_8_BREAKING?{}:{optional:{validate:(0,C.assertValueType)("boolean"),optional:!0}})});ee("NewExpression",{inherits:"CallExpression"});ee("Program",{visitor:["directives","body"],builder:["body","directives","sourceType","interpreter"],fields:{sourceType:{validate:(0,C.assertOneOf)("script","module"),default:"script"},interpreter:{validate:(0,C.assertNodeType)("InterpreterDirective"),default:null,optional:!0},directives:{validate:(0,C.arrayOfType)("Directive"),default:[]},body:(0,C.validateArrayOfType)("Statement")},aliases:["Scopable","BlockParent","Block"]});ee("ObjectExpression",{visitor:["properties"],aliases:["Expression"],fields:{properties:(0,C.validateArrayOfType)("ObjectMethod","ObjectProperty","SpreadElement")}});ee("ObjectMethod",{builder:["kind","key","params","body","computed","generator","async"],visitor:["decorators","key","typeParameters","params","returnType","body"],fields:Object.assign({},Wi(),Zs(),{kind:Object.assign({validate:(0,C.assertOneOf)("method","get","set")},process.env.BABEL_TYPES_8_BREAKING?{}:{default:"method"}),computed:{default:!1},key:{validate:function(){let e=(0,C.assertNodeType)("Identifier","StringLiteral","NumericLiteral","BigIntLiteral"),t=(0,C.assertNodeType)("Expression"),r=function(n,s,i){(n.computed?t:e)(n,s,i)};return r.oneOfNodeTypes=["Expression","Identifier","StringLiteral","NumericLiteral","BigIntLiteral"],r}()},decorators:{validate:(0,C.arrayOfType)("Decorator"),optional:!0},body:{validate:(0,C.assertNodeType)("BlockStatement")}}),aliases:["UserWhitespacable","Function","Scopable","BlockParent","FunctionParent","Method","ObjectMember"]});ee("ObjectProperty",{builder:["key","value","computed","shorthand",...process.env.BABEL_TYPES_8_BREAKING?[]:["decorators"]],fields:{computed:{default:!1},key:{validate:function(){let e=(0,C.assertNodeType)("Identifier","StringLiteral","NumericLiteral","BigIntLiteral","DecimalLiteral","PrivateName"),t=(0,C.assertNodeType)("Expression");return Object.assign(function(n,s,i){(n.computed?t:e)(n,s,i)},{oneOfNodeTypes:["Expression","Identifier","StringLiteral","NumericLiteral","BigIntLiteral","DecimalLiteral","PrivateName"]})}()},value:{validate:(0,C.assertNodeType)("Expression","PatternLike")},shorthand:{validate:process.env.BABEL_TYPES_8_BREAKING?(0,C.chain)((0,C.assertValueType)("boolean"),Object.assign(function(e,t,r){if(r){if(e.computed)throw new TypeError("Property shorthand of ObjectProperty cannot be true if computed is true");if(!(0,dr.default)("Identifier",e.key))throw new TypeError("Property shorthand of ObjectProperty cannot be true if key is not an Identifier")}},{type:"boolean"})):(0,C.assertValueType)("boolean"),default:!1},decorators:{validate:(0,C.arrayOfType)("Decorator"),optional:!0}},visitor:["key","value","decorators"],aliases:["UserWhitespacable","Property","ObjectMember"],validate:process.env.BABEL_TYPES_8_BREAKING?function(){let e=(0,C.assertNodeType)("Identifier","Pattern","TSAsExpression","TSSatisfiesExpression","TSNonNullExpression","TSTypeAssertion"),t=(0,C.assertNodeType)("Expression");return function(r,n,s){((0,dr.default)("ObjectPattern",r)?e:t)(s,"value",s.value)}}():void 0});ee("RestElement",{visitor:["argument","typeAnnotation"],builder:["argument"],aliases:["LVal","PatternLike"],deprecatedAlias:"RestProperty",fields:Object.assign({},Ki(),{argument:{validate:process.env.BABEL_TYPES_8_BREAKING?(0,C.assertNodeType)("Identifier","ArrayPattern","ObjectPattern","MemberExpression","TSAsExpression","TSSatisfiesExpression","TSTypeAssertion","TSNonNullExpression"):(0,C.assertNodeType)("LVal")}}),validate:process.env.BABEL_TYPES_8_BREAKING?function(e,t){let r=/(\w+)\[(\d+)\]/.exec(t);if(!r)throw new Error("Internal Babel error: malformed key.");let[,n,s]=r;if(e[n].length>+s+1)throw new TypeError(`RestElement must be last element of ${n}`)}:void 0});ee("ReturnStatement",{visitor:["argument"],aliases:["Statement","Terminatorless","CompletionStatement"],fields:{argument:{validate:(0,C.assertNodeType)("Expression"),optional:!0}}});ee("SequenceExpression",{visitor:["expressions"],fields:{expressions:(0,C.validateArrayOfType)("Expression")},aliases:["Expression"]});ee("ParenthesizedExpression",{visitor:["expression"],aliases:["Expression","ExpressionWrapper"],fields:{expression:{validate:(0,C.assertNodeType)("Expression")}}});ee("SwitchCase",{visitor:["test","consequent"],fields:{test:{validate:(0,C.assertNodeType)("Expression"),optional:!0},consequent:(0,C.validateArrayOfType)("Statement")}});ee("SwitchStatement",{visitor:["discriminant","cases"],aliases:["Statement","BlockParent","Scopable"],fields:{discriminant:{validate:(0,C.assertNodeType)("Expression")},cases:(0,C.validateArrayOfType)("SwitchCase")}});ee("ThisExpression",{aliases:["Expression"]});ee("ThrowStatement",{visitor:["argument"],aliases:["Statement","Terminatorless","CompletionStatement"],fields:{argument:{validate:(0,C.assertNodeType)("Expression")}}});ee("TryStatement",{visitor:["block","handler","finalizer"],aliases:["Statement"],fields:{block:{validate:process.env.BABEL_TYPES_8_BREAKING?(0,C.chain)((0,C.assertNodeType)("BlockStatement"),Object.assign(function(e){if(!e.handler&&!e.finalizer)throw new TypeError("TryStatement expects either a handler or finalizer, or both")},{oneOfNodeTypes:["BlockStatement"]})):(0,C.assertNodeType)("BlockStatement")},handler:{optional:!0,validate:(0,C.assertNodeType)("CatchClause")},finalizer:{optional:!0,validate:(0,C.assertNodeType)("BlockStatement")}}});ee("UnaryExpression",{builder:["operator","argument","prefix"],fields:{prefix:{default:!0},argument:{validate:(0,C.assertNodeType)("Expression")},operator:{validate:(0,C.assertOneOf)(...eo.UNARY_OPERATORS)}},visitor:["argument"],aliases:["UnaryLike","Expression"]});ee("UpdateExpression",{builder:["operator","argument","prefix"],fields:{prefix:{default:!1},argument:{validate:process.env.BABEL_TYPES_8_BREAKING?(0,C.assertNodeType)("Identifier","MemberExpression"):(0,C.assertNodeType)("Expression")},operator:{validate:(0,C.assertOneOf)(...eo.UPDATE_OPERATORS)}},visitor:["argument"],aliases:["Expression"]});ee("VariableDeclaration",{builder:["kind","declarations"],visitor:["declarations"],aliases:["Statement","Declaration"],fields:{declare:{validate:(0,C.assertValueType)("boolean"),optional:!0},kind:{validate:(0,C.assertOneOf)("var","let","const","using","await using")},declarations:(0,C.validateArrayOfType)("VariableDeclarator")},validate:process.env.BABEL_TYPES_8_BREAKING?(()=>{let e=(0,C.assertNodeType)("Identifier");return function(t,r,n){if((0,dr.default)("ForXStatement",t,{left:n})){if(n.declarations.length!==1)throw new TypeError(`Exactly one VariableDeclarator is required in the VariableDeclaration of a ${t.type}`)}else n.declarations.forEach(s=>{s.init||e(s,"id",s.id)})}})():void 0});ee("VariableDeclarator",{visitor:["id","init"],fields:{id:{validate:process.env.BABEL_TYPES_8_BREAKING?(0,C.assertNodeType)("Identifier","ArrayPattern","ObjectPattern"):(0,C.assertNodeType)("LVal")},definite:{optional:!0,validate:(0,C.assertValueType)("boolean")},init:{optional:!0,validate:(0,C.assertNodeType)("Expression")}}});ee("WhileStatement",{visitor:["test","body"],aliases:["Statement","BlockParent","Loop","While","Scopable"],fields:{test:{validate:(0,C.assertNodeType)("Expression")},body:{validate:(0,C.assertNodeType)("Statement")}}});ee("WithStatement",{visitor:["object","body"],aliases:["Statement"],fields:{object:{validate:(0,C.assertNodeType)("Expression")},body:{validate:(0,C.assertNodeType)("Statement")}}});ee("AssignmentPattern",{visitor:["left","right","decorators"],builder:["left","right"],aliases:["Pattern","PatternLike","LVal"],fields:Object.assign({},Ki(),{left:{validate:(0,C.assertNodeType)("Identifier","ObjectPattern","ArrayPattern","MemberExpression","TSAsExpression","TSSatisfiesExpression","TSTypeAssertion","TSNonNullExpression")},right:{validate:(0,C.assertNodeType)("Expression")},decorators:{validate:(0,C.arrayOfType)("Decorator"),optional:!0}})});ee("ArrayPattern",{visitor:["elements","typeAnnotation"],builder:["elements"],aliases:["Pattern","PatternLike","LVal"],fields:Object.assign({},Ki(),{elements:{validate:(0,C.chain)((0,C.assertValueType)("array"),(0,C.assertEach)((0,C.assertNodeOrValueType)("null","PatternLike","LVal")))}})});ee("ArrowFunctionExpression",{builder:["params","body","async"],visitor:["typeParameters","params","predicate","returnType","body"],aliases:["Scopable","Function","BlockParent","FunctionParent","Expression","Pureish"],fields:Object.assign({},Wi(),Zs(),{expression:{validate:(0,C.assertValueType)("boolean")},body:{validate:(0,C.assertNodeType)("BlockStatement","Expression")},predicate:{validate:(0,C.assertNodeType)("DeclaredPredicate","InferredPredicate"),optional:!0}})});ee("ClassBody",{visitor:["body"],fields:{body:(0,C.validateArrayOfType)("ClassMethod","ClassPrivateMethod","ClassProperty","ClassPrivateProperty","ClassAccessorProperty","TSDeclareMethod","TSIndexSignature","StaticBlock")}});ee("ClassExpression",{builder:["id","superClass","body","decorators"],visitor:["decorators","id","typeParameters","superClass","superTypeParameters","mixins","implements","body"],aliases:["Scopable","Class","Expression"],fields:{id:{validate:(0,C.assertNodeType)("Identifier"),optional:!0},typeParameters:{validate:(0,C.assertNodeType)("TypeParameterDeclaration","TSTypeParameterDeclaration","Noop"),optional:!0},body:{validate:(0,C.assertNodeType)("ClassBody")},superClass:{optional:!0,validate:(0,C.assertNodeType)("Expression")},superTypeParameters:{validate:(0,C.assertNodeType)("TypeParameterInstantiation","TSTypeParameterInstantiation"),optional:!0},implements:{validate:(0,C.arrayOfType)("TSExpressionWithTypeArguments","ClassImplements"),optional:!0},decorators:{validate:(0,C.arrayOfType)("Decorator"),optional:!0},mixins:{validate:(0,C.assertNodeType)("InterfaceExtends"),optional:!0}}});ee("ClassDeclaration",{inherits:"ClassExpression",aliases:["Scopable","Class","Statement","Declaration"],fields:{id:{validate:(0,C.assertNodeType)("Identifier"),optional:!0},typeParameters:{validate:(0,C.assertNodeType)("TypeParameterDeclaration","TSTypeParameterDeclaration","Noop"),optional:!0},body:{validate:(0,C.assertNodeType)("ClassBody")},superClass:{optional:!0,validate:(0,C.assertNodeType)("Expression")},superTypeParameters:{validate:(0,C.assertNodeType)("TypeParameterInstantiation","TSTypeParameterInstantiation"),optional:!0},implements:{validate:(0,C.arrayOfType)("TSExpressionWithTypeArguments","ClassImplements"),optional:!0},decorators:{validate:(0,C.arrayOfType)("Decorator"),optional:!0},mixins:{validate:(0,C.assertNodeType)("InterfaceExtends"),optional:!0},declare:{validate:(0,C.assertValueType)("boolean"),optional:!0},abstract:{validate:(0,C.assertValueType)("boolean"),optional:!0}},validate:process.env.BABEL_TYPES_8_BREAKING?function(){let e=(0,C.assertNodeType)("Identifier");return function(t,r,n){(0,dr.default)("ExportDefaultDeclaration",t)||e(n,"id",n.id)}}():void 0});var fd=ir.importAttributes={attributes:{optional:!0,validate:(0,C.arrayOfType)("ImportAttribute")},assertions:{deprecated:!0,optional:!0,validate:(0,C.arrayOfType)("ImportAttribute")}};ee("ExportAllDeclaration",{builder:["source"],visitor:["source","attributes","assertions"],aliases:["Statement","Declaration","ImportOrExportDeclaration","ExportDeclaration"],fields:Object.assign({source:{validate:(0,C.assertNodeType)("StringLiteral")},exportKind:(0,C.validateOptional)((0,C.assertOneOf)("type","value"))},fd)});ee("ExportDefaultDeclaration",{visitor:["declaration"],aliases:["Statement","Declaration","ImportOrExportDeclaration","ExportDeclaration"],fields:{declaration:(0,C.validateType)("TSDeclareFunction","FunctionDeclaration","ClassDeclaration","Expression"),exportKind:(0,C.validateOptional)((0,C.assertOneOf)("value"))}});ee("ExportNamedDeclaration",{builder:["declaration","specifiers","source"],visitor:process.env?["declaration","specifiers","source","attributes"]:["declaration","specifiers","source","attributes","assertions"],aliases:["Statement","Declaration","ImportOrExportDeclaration","ExportDeclaration"],fields:Object.assign({declaration:{optional:!0,validate:process.env.BABEL_TYPES_8_BREAKING?(0,C.chain)((0,C.assertNodeType)("Declaration"),Object.assign(function(e,t,r){if(r&&e.specifiers.length)throw new TypeError("Only declaration or specifiers is allowed on ExportNamedDeclaration");if(r&&e.source)throw new TypeError("Cannot export a declaration from a source")},{oneOfNodeTypes:["Declaration"]})):(0,C.assertNodeType)("Declaration")}},fd,{specifiers:{default:[],validate:(0,C.arrayOf)(function(){let e=(0,C.assertNodeType)("ExportSpecifier","ExportDefaultSpecifier","ExportNamespaceSpecifier"),t=(0,C.assertNodeType)("ExportSpecifier");return process.env.BABEL_TYPES_8_BREAKING?Object.assign(function(r,n,s){(r.source?e:t)(r,n,s)},{oneOfNodeTypes:["ExportSpecifier","ExportDefaultSpecifier","ExportNamespaceSpecifier"]}):e}())},source:{validate:(0,C.assertNodeType)("StringLiteral"),optional:!0},exportKind:(0,C.validateOptional)((0,C.assertOneOf)("type","value"))})});ee("ExportSpecifier",{visitor:["local","exported"],aliases:["ModuleSpecifier"],fields:{local:{validate:(0,C.assertNodeType)("Identifier")},exported:{validate:(0,C.assertNodeType)("Identifier","StringLiteral")},exportKind:{validate:(0,C.assertOneOf)("type","value"),optional:!0}}});ee("ForOfStatement",{visitor:["left","right","body"],builder:["left","right","body","await"],aliases:["Scopable","Statement","For","BlockParent","Loop","ForXStatement"],fields:{left:{validate:function(){if(!process.env.BABEL_TYPES_8_BREAKING)return(0,C.assertNodeType)("VariableDeclaration","LVal");let e=(0,C.assertNodeType)("VariableDeclaration"),t=(0,C.assertNodeType)("Identifier","MemberExpression","ArrayPattern","ObjectPattern","TSAsExpression","TSSatisfiesExpression","TSTypeAssertion","TSNonNullExpression");return Object.assign(function(r,n,s){(0,dr.default)("VariableDeclaration",s)?e(r,n,s):t(r,n,s)},{oneOfNodeTypes:["VariableDeclaration","Identifier","MemberExpression","ArrayPattern","ObjectPattern","TSAsExpression","TSSatisfiesExpression","TSTypeAssertion","TSNonNullExpression"]})}()},right:{validate:(0,C.assertNodeType)("Expression")},body:{validate:(0,C.assertNodeType)("Statement")},await:{default:!1}}});ee("ImportDeclaration",{builder:["specifiers","source"],visitor:["specifiers","source","attributes","assertions"],aliases:["Statement","Declaration","ImportOrExportDeclaration"],fields:Object.assign({},fd,{module:{optional:!0,validate:(0,C.assertValueType)("boolean")},phase:{default:null,validate:(0,C.assertOneOf)("source","defer")},specifiers:(0,C.validateArrayOfType)("ImportSpecifier","ImportDefaultSpecifier","ImportNamespaceSpecifier"),source:{validate:(0,C.assertNodeType)("StringLiteral")},importKind:{validate:(0,C.assertOneOf)("type","typeof","value"),optional:!0}})});ee("ImportDefaultSpecifier",{visitor:["local"],aliases:["ModuleSpecifier"],fields:{local:{validate:(0,C.assertNodeType)("Identifier")}}});ee("ImportNamespaceSpecifier",{visitor:["local"],aliases:["ModuleSpecifier"],fields:{local:{validate:(0,C.assertNodeType)("Identifier")}}});ee("ImportSpecifier",{visitor:["imported","local"],builder:["local","imported"],aliases:["ModuleSpecifier"],fields:{local:{validate:(0,C.assertNodeType)("Identifier")},imported:{validate:(0,C.assertNodeType)("Identifier","StringLiteral")},importKind:{validate:(0,C.assertOneOf)("type","typeof","value"),optional:!0}}});ee("ImportExpression",{visitor:["source","options"],aliases:["Expression"],fields:{phase:{default:null,validate:(0,C.assertOneOf)("source","defer")},source:{validate:(0,C.assertNodeType)("Expression")},options:{validate:(0,C.assertNodeType)("Expression"),optional:!0}}});ee("MetaProperty",{visitor:["meta","property"],aliases:["Expression"],fields:{meta:{validate:process.env.BABEL_TYPES_8_BREAKING?(0,C.chain)((0,C.assertNodeType)("Identifier"),Object.assign(function(e,t,r){let n;switch(r.name){case"function":n="sent";break;case"new":n="target";break;case"import":n="meta";break}if(!(0,dr.default)("Identifier",e.property,{name:n}))throw new TypeError("Unrecognised MetaProperty")},{oneOfNodeTypes:["Identifier"]})):(0,C.assertNodeType)("Identifier")},property:{validate:(0,C.assertNodeType)("Identifier")}}});var $l=()=>({abstract:{validate:(0,C.assertValueType)("boolean"),optional:!0},accessibility:{validate:(0,C.assertOneOf)("public","private","protected"),optional:!0},static:{default:!1},override:{default:!1},computed:{default:!1},optional:{validate:(0,C.assertValueType)("boolean"),optional:!0},key:{validate:(0,C.chain)(function(){let e=(0,C.assertNodeType)("Identifier","StringLiteral","NumericLiteral","BigIntLiteral"),t=(0,C.assertNodeType)("Expression");return function(r,n,s){(r.computed?t:e)(r,n,s)}}(),(0,C.assertNodeType)("Identifier","StringLiteral","NumericLiteral","BigIntLiteral","Expression"))}});ir.classMethodOrPropertyCommon=$l;var dd=()=>Object.assign({},Wi(),$l(),{params:(0,C.validateArrayOfType)("Identifier","Pattern","RestElement","TSParameterProperty"),kind:{validate:(0,C.assertOneOf)("get","set","method","constructor"),default:"method"},access:{validate:(0,C.chain)((0,C.assertValueType)("string"),(0,C.assertOneOf)("public","private","protected")),optional:!0},decorators:{validate:(0,C.arrayOfType)("Decorator"),optional:!0}});ir.classMethodOrDeclareMethodCommon=dd;ee("ClassMethod",{aliases:["Function","Scopable","BlockParent","FunctionParent","Method"],builder:["kind","key","params","body","computed","static","generator","async"],visitor:["decorators","key","typeParameters","params","returnType","body"],fields:Object.assign({},dd(),Zs(),{body:{validate:(0,C.assertNodeType)("BlockStatement")}})});ee("ObjectPattern",{visitor:["properties","typeAnnotation","decorators"],builder:["properties"],aliases:["Pattern","PatternLike","LVal"],fields:Object.assign({},Ki(),{properties:(0,C.validateArrayOfType)("RestElement","ObjectProperty")})});ee("SpreadElement",{visitor:["argument"],aliases:["UnaryLike"],deprecatedAlias:"SpreadProperty",fields:{argument:{validate:(0,C.assertNodeType)("Expression")}}});ee("Super",{aliases:["Expression"]});ee("TaggedTemplateExpression",{visitor:["tag","typeParameters","quasi"],builder:["tag","quasi"],aliases:["Expression"],fields:{tag:{validate:(0,C.assertNodeType)("Expression")},quasi:{validate:(0,C.assertNodeType)("TemplateLiteral")},typeParameters:{validate:(0,C.assertNodeType)("TypeParameterInstantiation","TSTypeParameterInstantiation"),optional:!0}}});ee("TemplateElement",{builder:["value","tail"],fields:{value:{validate:(0,C.chain)((0,C.assertShape)({raw:{validate:(0,C.assertValueType)("string")},cooked:{validate:(0,C.assertValueType)("string"),optional:!0}}),function(t){let r=t.value.raw,n=!1,s=()=>{throw new Error("Internal @babel/types error.")},{str:i,firstInvalidLoc:a}=(0,J4.readStringContents)("template",r,0,0,0,{unterminated(){n=!0},strictNumericEscape:s,invalidEscapeSequence:s,numericSeparatorInEscapeSequence:s,unexpectedNumericSeparator:s,invalidDigit:s,invalidCodePoint:s});if(!n)throw new Error("Invalid raw");t.value.cooked=a?null:i})},tail:{default:!1}}});ee("TemplateLiteral",{visitor:["quasis","expressions"],aliases:["Expression","Literal"],fields:{quasis:(0,C.validateArrayOfType)("TemplateElement"),expressions:{validate:(0,C.chain)((0,C.assertValueType)("array"),(0,C.assertEach)((0,C.assertNodeType)("Expression","TSType")),function(e,t,r){if(e.quasis.length!==r.length+1)throw new TypeError(`Number of ${e.type} quasis should be exactly one more than the number of expressions.
Expected ${r.length+1} quasis but got ${e.quasis.length}`)})}}});ee("YieldExpression",{builder:["argument","delegate"],visitor:["argument"],aliases:["Expression","Terminatorless"],fields:{delegate:{validate:process.env.BABEL_TYPES_8_BREAKING?(0,C.chain)((0,C.assertValueType)("boolean"),Object.assign(function(e,t,r){if(r&&!e.argument)throw new TypeError("Property delegate of YieldExpression cannot be true if there is no argument")},{type:"boolean"})):(0,C.assertValueType)("boolean"),default:!1},argument:{optional:!0,validate:(0,C.assertNodeType)("Expression")}}});ee("AwaitExpression",{builder:["argument"],visitor:["argument"],aliases:["Expression","Terminatorless"],fields:{argument:{validate:(0,C.assertNodeType)("Expression")}}});ee("Import",{aliases:["Expression"]});ee("BigIntLiteral",{builder:["value"],fields:{value:{validate:(0,C.assertValueType)("string")}},aliases:["Expression","Pureish","Literal","Immutable"]});ee("ExportNamespaceSpecifier",{visitor:["exported"],aliases:["ModuleSpecifier"],fields:{exported:{validate:(0,C.assertNodeType)("Identifier")}}});ee("OptionalMemberExpression",{builder:["object","property","computed","optional"],visitor:["object","property"],aliases:["Expression"],fields:{object:{validate:(0,C.assertNodeType)("Expression")},property:{validate:function(){let e=(0,C.assertNodeType)("Identifier"),t=(0,C.assertNodeType)("Expression");return Object.assign(function(n,s,i){(n.computed?t:e)(n,s,i)},{oneOfNodeTypes:["Expression","Identifier"]})}()},computed:{default:!1},optional:{validate:process.env.BABEL_TYPES_8_BREAKING?(0,C.chain)((0,C.assertValueType)("boolean"),(0,C.assertOptionalChainStart)()):(0,C.assertValueType)("boolean")}}});ee("OptionalCallExpression",{visitor:["callee","arguments","typeParameters","typeArguments"],builder:["callee","arguments","optional"],aliases:["Expression"],fields:Object.assign({callee:{validate:(0,C.assertNodeType)("Expression")},arguments:(0,C.validateArrayOfType)("Expression","SpreadElement","ArgumentPlaceholder"),optional:{validate:process.env.BABEL_TYPES_8_BREAKING?(0,C.chain)((0,C.assertValueType)("boolean"),(0,C.assertOptionalChainStart)()):(0,C.assertValueType)("boolean")},typeArguments:{validate:(0,C.assertNodeType)("TypeParameterInstantiation"),optional:!0}},{typeParameters:{validate:(0,C.assertNodeType)("TSTypeParameterInstantiation"),optional:!0}})});ee("ClassProperty",{visitor:["decorators","variance","key","typeAnnotation","value"],builder:["key","value","typeAnnotation","decorators","computed","static"],aliases:["Property"],fields:Object.assign({},$l(),{value:{validate:(0,C.assertNodeType)("Expression"),optional:!0},definite:{validate:(0,C.assertValueType)("boolean"),optional:!0},typeAnnotation:{validate:(0,C.assertNodeType)("TypeAnnotation","TSTypeAnnotation","Noop"),optional:!0},decorators:{validate:(0,C.arrayOfType)("Decorator"),optional:!0},readonly:{validate:(0,C.assertValueType)("boolean"),optional:!0},declare:{validate:(0,C.assertValueType)("boolean"),optional:!0},variance:{validate:(0,C.assertNodeType)("Variance"),optional:!0}})});ee("ClassAccessorProperty",{visitor:["decorators","key","typeAnnotation","value"],builder:["key","value","typeAnnotation","decorators","computed","static"],aliases:["Property","Accessor"],fields:Object.assign({},$l(),{key:{validate:(0,C.chain)(function(){let e=(0,C.assertNodeType)("Identifier","StringLiteral","NumericLiteral","BigIntLiteral","PrivateName"),t=(0,C.assertNodeType)("Expression");return function(r,n,s){(r.computed?t:e)(r,n,s)}}(),(0,C.assertNodeType)("Identifier","StringLiteral","NumericLiteral","BigIntLiteral","Expression","PrivateName"))},value:{validate:(0,C.assertNodeType)("Expression"),optional:!0},definite:{validate:(0,C.assertValueType)("boolean"),optional:!0},typeAnnotation:{validate:(0,C.assertNodeType)("TypeAnnotation","TSTypeAnnotation","Noop"),optional:!0},decorators:{validate:(0,C.arrayOfType)("Decorator"),optional:!0},readonly:{validate:(0,C.assertValueType)("boolean"),optional:!0},declare:{validate:(0,C.assertValueType)("boolean"),optional:!0},variance:{validate:(0,C.assertNodeType)("Variance"),optional:!0}})});ee("ClassPrivateProperty",{visitor:["decorators","variance","key","typeAnnotation","value"],builder:["key","value","decorators","static"],aliases:["Property","Private"],fields:{key:{validate:(0,C.assertNodeType)("PrivateName")},value:{validate:(0,C.assertNodeType)("Expression"),optional:!0},typeAnnotation:{validate:(0,C.assertNodeType)("TypeAnnotation","TSTypeAnnotation","Noop"),optional:!0},decorators:{validate:(0,C.arrayOfType)("Decorator"),optional:!0},static:{validate:(0,C.assertValueType)("boolean"),default:!1},readonly:{validate:(0,C.assertValueType)("boolean"),optional:!0},optional:{validate:(0,C.assertValueType)("boolean"),optional:!0},definite:{validate:(0,C.assertValueType)("boolean"),optional:!0},variance:{validate:(0,C.assertNodeType)("Variance"),optional:!0}}});ee("ClassPrivateMethod",{builder:["kind","key","params","body","static"],visitor:["decorators","key","typeParameters","params","returnType","body"],aliases:["Function","Scopable","BlockParent","FunctionParent","Method","Private"],fields:Object.assign({},dd(),Zs(),{kind:{validate:(0,C.assertOneOf)("get","set","method"),default:"method"},key:{validate:(0,C.assertNodeType)("PrivateName")},body:{validate:(0,C.assertNodeType)("BlockStatement")}})});ee("PrivateName",{visitor:["id"],aliases:["Private"],fields:{id:{validate:(0,C.assertNodeType)("Identifier")}}});ee("StaticBlock",{visitor:["body"],fields:{body:(0,C.validateArrayOfType)("Statement")},aliases:["Scopable","BlockParent","FunctionParent"]})});var pT=T(()=>{"use strict";var cT=to(),M=gn(),ye=(0,M.defineAliasedType)("Flow"),hd=e=>{let t=e==="DeclareClass";ye(e,{builder:["id","typeParameters","extends","body"],visitor:["id","typeParameters","extends",...t?["mixins","implements"]:[],"body"],aliases:["FlowDeclaration","Statement","Declaration"],fields:Object.assign({id:(0,M.validateType)("Identifier"),typeParameters:(0,M.validateOptionalType)("TypeParameterDeclaration"),extends:(0,M.validateOptional)((0,M.arrayOfType)("InterfaceExtends"))},t?{mixins:(0,M.validateOptional)((0,M.arrayOfType)("InterfaceExtends")),implements:(0,M.validateOptional)((0,M.arrayOfType)("ClassImplements"))}:{},{body:(0,M.validateType)("ObjectTypeAnnotation")})})};ye("AnyTypeAnnotation",{aliases:["FlowType","FlowBaseAnnotation"]});ye("ArrayTypeAnnotation",{visitor:["elementType"],aliases:["FlowType"],fields:{elementType:(0,M.validateType)("FlowType")}});ye("BooleanTypeAnnotation",{aliases:["FlowType","FlowBaseAnnotation"]});ye("BooleanLiteralTypeAnnotation",{builder:["value"],aliases:["FlowType"],fields:{value:(0,M.validate)((0,M.assertValueType)("boolean"))}});ye("NullLiteralTypeAnnotation",{aliases:["FlowType","FlowBaseAnnotation"]});ye("ClassImplements",{visitor:["id","typeParameters"],fields:{id:(0,M.validateType)("Identifier"),typeParameters:(0,M.validateOptionalType)("TypeParameterInstantiation")}});hd("DeclareClass");ye("DeclareFunction",{builder:["id"],visitor:["id","predicate"],aliases:["FlowDeclaration","Statement","Declaration"],fields:{id:(0,M.validateType)("Identifier"),predicate:(0,M.validateOptionalType)("DeclaredPredicate")}});hd("DeclareInterface");ye("DeclareModule",{builder:["id","body","kind"],visitor:["id","body"],aliases:["FlowDeclaration","Statement","Declaration"],fields:{id:(0,M.validateType)("Identifier","StringLiteral"),body:(0,M.validateType)("BlockStatement"),kind:(0,M.validateOptional)((0,M.assertOneOf)("CommonJS","ES"))}});ye("DeclareModuleExports",{visitor:["typeAnnotation"],aliases:["FlowDeclaration","Statement","Declaration"],fields:{typeAnnotation:(0,M.validateType)("TypeAnnotation")}});ye("DeclareTypeAlias",{visitor:["id","typeParameters","right"],aliases:["FlowDeclaration","Statement","Declaration"],fields:{id:(0,M.validateType)("Identifier"),typeParameters:(0,M.validateOptionalType)("TypeParameterDeclaration"),right:(0,M.validateType)("FlowType")}});ye("DeclareOpaqueType",{visitor:["id","typeParameters","supertype"],aliases:["FlowDeclaration","Statement","Declaration"],fields:{id:(0,M.validateType)("Identifier"),typeParameters:(0,M.validateOptionalType)("TypeParameterDeclaration"),supertype:(0,M.validateOptionalType)("FlowType"),impltype:(0,M.validateOptionalType)("FlowType")}});ye("DeclareVariable",{visitor:["id"],aliases:["FlowDeclaration","Statement","Declaration"],fields:{id:(0,M.validateType)("Identifier")}});ye("DeclareExportDeclaration",{visitor:["declaration","specifiers","source","attributes"],aliases:["FlowDeclaration","Statement","Declaration"],fields:Object.assign({declaration:(0,M.validateOptionalType)("Flow"),specifiers:(0,M.validateOptional)((0,M.arrayOfType)("ExportSpecifier","ExportNamespaceSpecifier")),source:(0,M.validateOptionalType)("StringLiteral"),default:(0,M.validateOptional)((0,M.assertValueType)("boolean"))},cT.importAttributes)});ye("DeclareExportAllDeclaration",{visitor:["source","attributes"],aliases:["FlowDeclaration","Statement","Declaration"],fields:Object.assign({source:(0,M.validateType)("StringLiteral"),exportKind:(0,M.validateOptional)((0,M.assertOneOf)("type","value"))},cT.importAttributes)});ye("DeclaredPredicate",{visitor:["value"],aliases:["FlowPredicate"],fields:{value:(0,M.validateType)("Flow")}});ye("ExistsTypeAnnotation",{aliases:["FlowType"]});ye("FunctionTypeAnnotation",{builder:["typeParameters","params","rest","returnType"],visitor:["typeParameters","this","params","rest","returnType"],aliases:["FlowType"],fields:{typeParameters:(0,M.validateOptionalType)("TypeParameterDeclaration"),params:(0,M.validateArrayOfType)("FunctionTypeParam"),rest:(0,M.validateOptionalType)("FunctionTypeParam"),this:(0,M.validateOptionalType)("FunctionTypeParam"),returnType:(0,M.validateType)("FlowType")}});ye("FunctionTypeParam",{visitor:["name","typeAnnotation"],fields:{name:(0,M.validateOptionalType)("Identifier"),typeAnnotation:(0,M.validateType)("FlowType"),optional:(0,M.validateOptional)((0,M.assertValueType)("boolean"))}});ye("GenericTypeAnnotation",{visitor:["id","typeParameters"],aliases:["FlowType"],fields:{id:(0,M.validateType)("Identifier","QualifiedTypeIdentifier"),typeParameters:(0,M.validateOptionalType)("TypeParameterInstantiation")}});ye("InferredPredicate",{aliases:["FlowPredicate"]});ye("InterfaceExtends",{visitor:["id","typeParameters"],fields:{id:(0,M.validateType)("Identifier","QualifiedTypeIdentifier"),typeParameters:(0,M.validateOptionalType)("TypeParameterInstantiation")}});hd("InterfaceDeclaration");ye("InterfaceTypeAnnotation",{visitor:["extends","body"],aliases:["FlowType"],fields:{extends:(0,M.validateOptional)((0,M.arrayOfType)("InterfaceExtends")),body:(0,M.validateType)("ObjectTypeAnnotation")}});ye("IntersectionTypeAnnotation",{visitor:["types"],aliases:["FlowType"],fields:{types:(0,M.validate)((0,M.arrayOfType)("FlowType"))}});ye("MixedTypeAnnotation",{aliases:["FlowType","FlowBaseAnnotation"]});ye("EmptyTypeAnnotation",{aliases:["FlowType","FlowBaseAnnotation"]});ye("NullableTypeAnnotation",{visitor:["typeAnnotation"],aliases:["FlowType"],fields:{typeAnnotation:(0,M.validateType)("FlowType")}});ye("NumberLiteralTypeAnnotation",{builder:["value"],aliases:["FlowType"],fields:{value:(0,M.validate)((0,M.assertValueType)("number"))}});ye("NumberTypeAnnotation",{aliases:["FlowType","FlowBaseAnnotation"]});ye("ObjectTypeAnnotation",{visitor:["properties","indexers","callProperties","internalSlots"],aliases:["FlowType"],builder:["properties","indexers","callProperties","internalSlots","exact"],fields:{properties:(0,M.validate)((0,M.arrayOfType)("ObjectTypeProperty","ObjectTypeSpreadProperty")),indexers:{validate:(0,M.arrayOfType)("ObjectTypeIndexer"),optional:!0,default:[]},callProperties:{validate:(0,M.arrayOfType)("ObjectTypeCallProperty"),optional:!0,default:[]},internalSlots:{validate:(0,M.arrayOfType)("ObjectTypeInternalSlot"),optional:!0,default:[]},exact:{validate:(0,M.assertValueType)("boolean"),default:!1},inexact:(0,M.validateOptional)((0,M.assertValueType)("boolean"))}});ye("ObjectTypeInternalSlot",{visitor:["id","value"],builder:["id","value","optional","static","method"],aliases:["UserWhitespacable"],fields:{id:(0,M.validateType)("Identifier"),value:(0,M.validateType)("FlowType"),optional:(0,M.validate)((0,M.assertValueType)("boolean")),static:(0,M.validate)((0,M.assertValueType)("boolean")),method:(0,M.validate)((0,M.assertValueType)("boolean"))}});ye("ObjectTypeCallProperty",{visitor:["value"],aliases:["UserWhitespacable"],fields:{value:(0,M.validateType)("FlowType"),static:(0,M.validate)((0,M.assertValueType)("boolean"))}});ye("ObjectTypeIndexer",{visitor:["variance","id","key","value"],builder:["id","key","value","variance"],aliases:["UserWhitespacable"],fields:{id:(0,M.validateOptionalType)("Identifier"),key:(0,M.validateType)("FlowType"),value:(0,M.validateType)("FlowType"),static:(0,M.validate)((0,M.assertValueType)("boolean")),variance:(0,M.validateOptionalType)("Variance")}});ye("ObjectTypeProperty",{visitor:["key","value","variance"],aliases:["UserWhitespacable"],fields:{key:(0,M.validateType)("Identifier","StringLiteral"),value:(0,M.validateType)("FlowType"),kind:(0,M.validate)((0,M.assertOneOf)("init","get","set")),static:(0,M.validate)((0,M.assertValueType)("boolean")),proto:(0,M.validate)((0,M.assertValueType)("boolean")),optional:(0,M.validate)((0,M.assertValueType)("boolean")),variance:(0,M.validateOptionalType)("Variance"),method:(0,M.validate)((0,M.assertValueType)("boolean"))}});ye("ObjectTypeSpreadProperty",{visitor:["argument"],aliases:["UserWhitespacable"],fields:{argument:(0,M.validateType)("FlowType")}});ye("OpaqueType",{visitor:["id","typeParameters","supertype","impltype"],aliases:["FlowDeclaration","Statement","Declaration"],fields:{id:(0,M.validateType)("Identifier"),typeParameters:(0,M.validateOptionalType)("TypeParameterDeclaration"),supertype:(0,M.validateOptionalType)("FlowType"),impltype:(0,M.validateType)("FlowType")}});ye("QualifiedTypeIdentifier",{visitor:["qualification","id"],builder:["id","qualification"],fields:{id:(0,M.validateType)("Identifier"),qualification:(0,M.validateType)("Identifier","QualifiedTypeIdentifier")}});ye("StringLiteralTypeAnnotation",{builder:["value"],aliases:["FlowType"],fields:{value:(0,M.validate)((0,M.assertValueType)("string"))}});ye("StringTypeAnnotation",{aliases:["FlowType","FlowBaseAnnotation"]});ye("SymbolTypeAnnotation",{aliases:["FlowType","FlowBaseAnnotation"]});ye("ThisTypeAnnotation",{aliases:["FlowType","FlowBaseAnnotation"]});ye("TupleTypeAnnotation",{visitor:["types"],aliases:["FlowType"],fields:{types:(0,M.validate)((0,M.arrayOfType)("FlowType"))}});ye("TypeofTypeAnnotation",{visitor:["argument"],aliases:["FlowType"],fields:{argument:(0,M.validateType)("FlowType")}});ye("TypeAlias",{visitor:["id","typeParameters","right"],aliases:["FlowDeclaration","Statement","Declaration"],fields:{id:(0,M.validateType)("Identifier"),typeParameters:(0,M.validateOptionalType)("TypeParameterDeclaration"),right:(0,M.validateType)("FlowType")}});ye("TypeAnnotation",{visitor:["typeAnnotation"],fields:{typeAnnotation:(0,M.validateType)("FlowType")}});ye("TypeCastExpression",{visitor:["expression","typeAnnotation"],aliases:["ExpressionWrapper","Expression"],fields:{expression:(0,M.validateType)("Expression"),typeAnnotation:(0,M.validateType)("TypeAnnotation")}});ye("TypeParameter",{visitor:["bound","default","variance"],fields:{name:(0,M.validate)((0,M.assertValueType)("string")),bound:(0,M.validateOptionalType)("TypeAnnotation"),default:(0,M.validateOptionalType)("FlowType"),variance:(0,M.validateOptionalType)("Variance")}});ye("TypeParameterDeclaration",{visitor:["params"],fields:{params:(0,M.validate)((0,M.arrayOfType)("TypeParameter"))}});ye("TypeParameterInstantiation",{visitor:["params"],fields:{params:(0,M.validate)((0,M.arrayOfType)("FlowType"))}});ye("UnionTypeAnnotation",{visitor:["types"],aliases:["FlowType"],fields:{types:(0,M.validate)((0,M.arrayOfType)("FlowType"))}});ye("Variance",{builder:["kind"],fields:{kind:(0,M.validate)((0,M.assertOneOf)("minus","plus"))}});ye("VoidTypeAnnotation",{aliases:["FlowType","FlowBaseAnnotation"]});ye("EnumDeclaration",{aliases:["Statement","Declaration"],visitor:["id","body"],fields:{id:(0,M.validateType)("Identifier"),body:(0,M.validateType)("EnumBooleanBody","EnumNumberBody","EnumStringBody","EnumSymbolBody")}});ye("EnumBooleanBody",{aliases:["EnumBody"],visitor:["members"],fields:{explicitType:(0,M.validate)((0,M.assertValueType)("boolean")),members:(0,M.validateArrayOfType)("EnumBooleanMember"),hasUnknownMembers:(0,M.validate)((0,M.assertValueType)("boolean"))}});ye("EnumNumberBody",{aliases:["EnumBody"],visitor:["members"],fields:{explicitType:(0,M.validate)((0,M.assertValueType)("boolean")),members:(0,M.validateArrayOfType)("EnumNumberMember"),hasUnknownMembers:(0,M.validate)((0,M.assertValueType)("boolean"))}});ye("EnumStringBody",{aliases:["EnumBody"],visitor:["members"],fields:{explicitType:(0,M.validate)((0,M.assertValueType)("boolean")),members:(0,M.validateArrayOfType)("EnumStringMember","EnumDefaultedMember"),hasUnknownMembers:(0,M.validate)((0,M.assertValueType)("boolean"))}});ye("EnumSymbolBody",{aliases:["EnumBody"],visitor:["members"],fields:{members:(0,M.validateArrayOfType)("EnumDefaultedMember"),hasUnknownMembers:(0,M.validate)((0,M.assertValueType)("boolean"))}});ye("EnumBooleanMember",{aliases:["EnumMember"],builder:["id"],visitor:["id","init"],fields:{id:(0,M.validateType)("Identifier"),init:(0,M.validateType)("BooleanLiteral")}});ye("EnumNumberMember",{aliases:["EnumMember"],visitor:["id","init"],fields:{id:(0,M.validateType)("Identifier"),init:(0,M.validateType)("NumericLiteral")}});ye("EnumStringMember",{aliases:["EnumMember"],visitor:["id","init"],fields:{id:(0,M.validateType)("Identifier"),init:(0,M.validateType)("StringLiteral")}});ye("EnumDefaultedMember",{aliases:["EnumMember"],visitor:["id"],fields:{id:(0,M.validateType)("Identifier")}});ye("IndexedAccessType",{visitor:["objectType","indexType"],aliases:["FlowType"],fields:{objectType:(0,M.validateType)("FlowType"),indexType:(0,M.validateType)("FlowType")}});ye("OptionalIndexedAccessType",{visitor:["objectType","indexType"],aliases:["FlowType"],fields:{objectType:(0,M.validateType)("FlowType"),indexType:(0,M.validateType)("FlowType"),optional:(0,M.validate)((0,M.assertValueType)("boolean"))}})});var fT=T(()=>{"use strict";var At=gn(),hr=(0,At.defineAliasedType)("JSX");hr("JSXAttribute",{visitor:["name","value"],aliases:["Immutable"],fields:{name:{validate:(0,At.assertNodeType)("JSXIdentifier","JSXNamespacedName")},value:{optional:!0,validate:(0,At.assertNodeType)("JSXElement","JSXFragment","StringLiteral","JSXExpressionContainer")}}});hr("JSXClosingElement",{visitor:["name"],aliases:["Immutable"],fields:{name:{validate:(0,At.assertNodeType)("JSXIdentifier","JSXMemberExpression","JSXNamespacedName")}}});hr("JSXElement",{builder:["openingElement","closingElement","children","selfClosing"],visitor:["openingElement","children","closingElement"],aliases:["Immutable","Expression"],fields:Object.assign({openingElement:{validate:(0,At.assertNodeType)("JSXOpeningElement")},closingElement:{optional:!0,validate:(0,At.assertNodeType)("JSXClosingElement")},children:(0,At.validateArrayOfType)("JSXText","JSXExpressionContainer","JSXSpreadChild","JSXElement","JSXFragment")},{selfClosing:{validate:(0,At.assertValueType)("boolean"),optional:!0}})});hr("JSXEmptyExpression",{});hr("JSXExpressionContainer",{visitor:["expression"],aliases:["Immutable"],fields:{expression:{validate:(0,At.assertNodeType)("Expression","JSXEmptyExpression")}}});hr("JSXSpreadChild",{visitor:["expression"],aliases:["Immutable"],fields:{expression:{validate:(0,At.assertNodeType)("Expression")}}});hr("JSXIdentifier",{builder:["name"],fields:{name:{validate:(0,At.assertValueType)("string")}}});hr("JSXMemberExpression",{visitor:["object","property"],fields:{object:{validate:(0,At.assertNodeType)("JSXMemberExpression","JSXIdentifier")},property:{validate:(0,At.assertNodeType)("JSXIdentifier")}}});hr("JSXNamespacedName",{visitor:["namespace","name"],fields:{namespace:{validate:(0,At.assertNodeType)("JSXIdentifier")},name:{validate:(0,At.assertNodeType)("JSXIdentifier")}}});hr("JSXOpeningElement",{builder:["name","attributes","selfClosing"],visitor:["name","typeParameters","typeArguments","attributes"],aliases:["Immutable"],fields:Object.assign({name:{validate:(0,At.assertNodeType)("JSXIdentifier","JSXMemberExpression","JSXNamespacedName")},selfClosing:{default:!1},attributes:(0,At.validateArrayOfType)("JSXAttribute","JSXSpreadAttribute"),typeArguments:{validate:(0,At.assertNodeType)("TypeParameterInstantiation"),optional:!0}},{typeParameters:{validate:(0,At.assertNodeType)("TSTypeParameterInstantiation"),optional:!0}})});hr("JSXSpreadAttribute",{visitor:["argument"],fields:{argument:{validate:(0,At.assertNodeType)("Expression")}}});hr("JSXText",{aliases:["Immutable"],builder:["value"],fields:{value:{validate:(0,At.assertValueType)("string")}}});hr("JSXFragment",{builder:["openingFragment","closingFragment","children"],visitor:["openingFragment","children","closingFragment"],aliases:["Immutable","Expression"],fields:{openingFragment:{validate:(0,At.assertNodeType)("JSXOpeningFragment")},closingFragment:{validate:(0,At.assertNodeType)("JSXClosingFragment")},children:(0,At.validateArrayOfType)("JSXText","JSXExpressionContainer","JSXSpreadChild","JSXElement","JSXFragment")}});hr("JSXOpeningFragment",{aliases:["Immutable"]});hr("JSXClosingFragment",{aliases:["Immutable"]})});var gd=T(ms=>{"use strict";Object.defineProperty(ms,"__esModule",{value:!0});ms.PLACEHOLDERS_FLIPPED_ALIAS=ms.PLACEHOLDERS_ALIAS=ms.PLACEHOLDERS=void 0;var z4=gn(),Q4=ms.PLACEHOLDERS=["Identifier","StringLiteral","Expression","Statement","Declaration","BlockStatement","ClassBody","Pattern"],yd=ms.PLACEHOLDERS_ALIAS={Declaration:["Statement"],Pattern:["PatternLike","LVal"]};for(let e of Q4){let t=z4.ALIAS_KEYS[e];t!=null&&t.length&&(yd[e]=t)}var md=ms.PLACEHOLDERS_FLIPPED_ALIAS={};Object.keys(yd).forEach(e=>{yd[e].forEach(t=>{hasOwnProperty.call(md,t)||(md[t]=[]),md[t].push(e)})})});var dT=T(()=>{"use strict";var Wl=gn(),Z4=gd(),ej=to(),bd=(0,Wl.defineAliasedType)("Miscellaneous");bd("Noop",{visitor:[]});bd("Placeholder",{visitor:[],builder:["expectedNode","name"],fields:Object.assign({name:{validate:(0,Wl.assertNodeType)("Identifier")},expectedNode:{validate:(0,Wl.assertOneOf)(...Z4.PLACEHOLDERS)}},(0,ej.patternLikeCommon)())});bd("V8IntrinsicIdentifier",{builder:["name"],fields:{name:{validate:(0,Wl.assertValueType)("string")}}})});var hT=T(()=>{"use strict";var yt=gn();(0,yt.default)("ArgumentPlaceholder",{});(0,yt.default)("BindExpression",{visitor:["object","callee"],aliases:["Expression"],fields:process.env.BABEL_TYPES_8_BREAKING?{object:{validate:(0,yt.assertNodeType)("Expression")},callee:{validate:(0,yt.assertNodeType)("Expression")}}:{object:{validate:Object.assign(()=>{},{oneOfNodeTypes:["Expression"]})},callee:{validate:Object.assign(()=>{},{oneOfNodeTypes:["Expression"]})}}});(0,yt.default)("ImportAttribute",{visitor:["key","value"],fields:{key:{validate:(0,yt.assertNodeType)("Identifier","StringLiteral")},value:{validate:(0,yt.assertNodeType)("StringLiteral")}}});(0,yt.default)("Decorator",{visitor:["expression"],fields:{expression:{validate:(0,yt.assertNodeType)("Expression")}}});(0,yt.default)("DoExpression",{visitor:["body"],builder:["body","async"],aliases:["Expression"],fields:{body:{validate:(0,yt.assertNodeType)("BlockStatement")},async:{validate:(0,yt.assertValueType)("boolean"),default:!1}}});(0,yt.default)("ExportDefaultSpecifier",{visitor:["exported"],aliases:["ModuleSpecifier"],fields:{exported:{validate:(0,yt.assertNodeType)("Identifier")}}});(0,yt.default)("RecordExpression",{visitor:["properties"],aliases:["Expression"],fields:{properties:(0,yt.validateArrayOfType)("ObjectProperty","SpreadElement")}});(0,yt.default)("TupleExpression",{fields:{elements:{validate:(0,yt.arrayOfType)("Expression","SpreadElement"),default:[]}},visitor:["elements"],aliases:["Expression"]});(0,yt.default)("DecimalLiteral",{builder:["value"],fields:{value:{validate:(0,yt.assertValueType)("string")}},aliases:["Expression","Pureish","Literal","Immutable"]});(0,yt.default)("ModuleExpression",{visitor:["body"],fields:{body:{validate:(0,yt.assertNodeType)("Program")}},aliases:["Expression"]});(0,yt.default)("TopicReference",{aliases:["Expression"]});(0,yt.default)("PipelineTopicExpression",{builder:["expression"],visitor:["expression"],fields:{expression:{validate:(0,yt.assertNodeType)("Expression")}},aliases:["Expression"]});(0,yt.default)("PipelineBareFunction",{builder:["callee"],visitor:["callee"],fields:{callee:{validate:(0,yt.assertNodeType)("Expression")}},aliases:["Expression"]});(0,yt.default)("PipelinePrimaryTopicReference",{aliases:["Expression"]})});var xT=T(()=>{"use strict";var W=gn(),mT=to(),tj=Ui(),Ce=(0,W.defineAliasedType)("TypeScript"),vr=(0,W.assertValueType)("boolean"),yT=()=>({returnType:{validate:(0,W.assertNodeType)("TSTypeAnnotation","Noop"),optional:!0},typeParameters:{validate:(0,W.assertNodeType)("TSTypeParameterDeclaration","Noop"),optional:!0}});Ce("TSParameterProperty",{aliases:["LVal"],visitor:["parameter"],fields:{accessibility:{validate:(0,W.assertOneOf)("public","private","protected"),optional:!0},readonly:{validate:(0,W.assertValueType)("boolean"),optional:!0},parameter:{validate:(0,W.assertNodeType)("Identifier","AssignmentPattern")},override:{validate:(0,W.assertValueType)("boolean"),optional:!0},decorators:{validate:(0,W.arrayOfType)("Decorator"),optional:!0}}});Ce("TSDeclareFunction",{aliases:["Statement","Declaration"],visitor:["id","typeParameters","params","returnType"],fields:Object.assign({},(0,mT.functionDeclarationCommon)(),yT())});Ce("TSDeclareMethod",{visitor:["decorators","key","typeParameters","params","returnType"],fields:Object.assign({},(0,mT.classMethodOrDeclareMethodCommon)(),yT())});Ce("TSQualifiedName",{aliases:["TSEntityName"],visitor:["left","right"],fields:{left:(0,W.validateType)("TSEntityName"),right:(0,W.validateType)("Identifier")}});var Kl=()=>({typeParameters:(0,W.validateOptionalType)("TSTypeParameterDeclaration"),parameters:(0,W.validateArrayOfType)("ArrayPattern","Identifier","ObjectPattern","RestElement"),typeAnnotation:(0,W.validateOptionalType)("TSTypeAnnotation")}),gT={aliases:["TSTypeElement"],visitor:["typeParameters","parameters","typeAnnotation"],fields:Kl()};Ce("TSCallSignatureDeclaration",gT);Ce("TSConstructSignatureDeclaration",gT);var bT=()=>({key:(0,W.validateType)("Expression"),computed:{default:!1},optional:(0,W.validateOptional)(vr)});Ce("TSPropertySignature",{aliases:["TSTypeElement"],visitor:["key","typeAnnotation"],fields:Object.assign({},bT(),{readonly:(0,W.validateOptional)(vr),typeAnnotation:(0,W.validateOptionalType)("TSTypeAnnotation"),kind:{optional:!0,validate:(0,W.assertOneOf)("get","set")}})});Ce("TSMethodSignature",{aliases:["TSTypeElement"],visitor:["key","typeParameters","parameters","typeAnnotation"],fields:Object.assign({},Kl(),bT(),{kind:{validate:(0,W.assertOneOf)("method","get","set")}})});Ce("TSIndexSignature",{aliases:["TSTypeElement"],visitor:["parameters","typeAnnotation"],fields:{readonly:(0,W.validateOptional)(vr),static:(0,W.validateOptional)(vr),parameters:(0,W.validateArrayOfType)("Identifier"),typeAnnotation:(0,W.validateOptionalType)("TSTypeAnnotation")}});var rj=["TSAnyKeyword","TSBooleanKeyword","TSBigIntKeyword","TSIntrinsicKeyword","TSNeverKeyword","TSNullKeyword","TSNumberKeyword","TSObjectKeyword","TSStringKeyword","TSSymbolKeyword","TSUndefinedKeyword","TSUnknownKeyword","TSVoidKeyword"];for(let e of rj)Ce(e,{aliases:["TSType","TSBaseType"],visitor:[],fields:{}});Ce("TSThisType",{aliases:["TSType","TSBaseType"],visitor:[],fields:{}});var ET={aliases:["TSType"],visitor:["typeParameters","parameters","typeAnnotation"]};Ce("TSFunctionType",Object.assign({},ET,{fields:Kl()}));Ce("TSConstructorType",Object.assign({},ET,{fields:Object.assign({},Kl(),{abstract:(0,W.validateOptional)(vr)})}));Ce("TSTypeReference",{aliases:["TSType"],visitor:["typeName","typeParameters"],fields:{typeName:(0,W.validateType)("TSEntityName"),typeParameters:(0,W.validateOptionalType)("TSTypeParameterInstantiation")}});Ce("TSTypePredicate",{aliases:["TSType"],visitor:["parameterName","typeAnnotation"],builder:["parameterName","typeAnnotation","asserts"],fields:{parameterName:(0,W.validateType)("Identifier","TSThisType"),typeAnnotation:(0,W.validateOptionalType)("TSTypeAnnotation"),asserts:(0,W.validateOptional)(vr)}});Ce("TSTypeQuery",{aliases:["TSType"],visitor:["exprName","typeParameters"],fields:{exprName:(0,W.validateType)("TSEntityName","TSImportType"),typeParameters:(0,W.validateOptionalType)("TSTypeParameterInstantiation")}});Ce("TSTypeLiteral",{aliases:["TSType"],visitor:["members"],fields:{members:(0,W.validateArrayOfType)("TSTypeElement")}});Ce("TSArrayType",{aliases:["TSType"],visitor:["elementType"],fields:{elementType:(0,W.validateType)("TSType")}});Ce("TSTupleType",{aliases:["TSType"],visitor:["elementTypes"],fields:{elementTypes:(0,W.validateArrayOfType)("TSType","TSNamedTupleMember")}});Ce("TSOptionalType",{aliases:["TSType"],visitor:["typeAnnotation"],fields:{typeAnnotation:(0,W.validateType)("TSType")}});Ce("TSRestType",{aliases:["TSType"],visitor:["typeAnnotation"],fields:{typeAnnotation:(0,W.validateType)("TSType")}});Ce("TSNamedTupleMember",{visitor:["label","elementType"],builder:["label","elementType","optional"],fields:{label:(0,W.validateType)("Identifier"),optional:{validate:vr,default:!1},elementType:(0,W.validateType)("TSType")}});var TT={aliases:["TSType"],visitor:["types"],fields:{types:(0,W.validateArrayOfType)("TSType")}};Ce("TSUnionType",TT);Ce("TSIntersectionType",TT);Ce("TSConditionalType",{aliases:["TSType"],visitor:["checkType","extendsType","trueType","falseType"],fields:{checkType:(0,W.validateType)("TSType"),extendsType:(0,W.validateType)("TSType"),trueType:(0,W.validateType)("TSType"),falseType:(0,W.validateType)("TSType")}});Ce("TSInferType",{aliases:["TSType"],visitor:["typeParameter"],fields:{typeParameter:(0,W.validateType)("TSTypeParameter")}});Ce("TSParenthesizedType",{aliases:["TSType"],visitor:["typeAnnotation"],fields:{typeAnnotation:(0,W.validateType)("TSType")}});Ce("TSTypeOperator",{aliases:["TSType"],visitor:["typeAnnotation"],fields:{operator:(0,W.validate)((0,W.assertValueType)("string")),typeAnnotation:(0,W.validateType)("TSType")}});Ce("TSIndexedAccessType",{aliases:["TSType"],visitor:["objectType","indexType"],fields:{objectType:(0,W.validateType)("TSType"),indexType:(0,W.validateType)("TSType")}});Ce("TSMappedType",{aliases:["TSType"],visitor:["typeParameter","nameType","typeAnnotation"],builder:["typeParameter","typeAnnotation","nameType"],fields:Object.assign({},{typeParameter:(0,W.validateType)("TSTypeParameter")},{readonly:(0,W.validateOptional)((0,W.assertOneOf)(!0,!1,"+","-")),optional:(0,W.validateOptional)((0,W.assertOneOf)(!0,!1,"+","-")),typeAnnotation:(0,W.validateOptionalType)("TSType"),nameType:(0,W.validateOptionalType)("TSType")})});Ce("TSTemplateLiteralType",{aliases:["TSType","TSBaseType"],visitor:["quasis","types"],fields:{quasis:(0,W.validateArrayOfType)("TemplateElement"),types:{validate:(0,W.chain)((0,W.assertValueType)("array"),(0,W.assertEach)((0,W.assertNodeType)("TSType")),function(e,t,r){if(e.quasis.length!==r.length+1)throw new TypeError(`Number of ${e.type} quasis should be exactly one more than the number of types.
Expected ${r.length+1} quasis but got ${e.quasis.length}`)})}}});Ce("TSLiteralType",{aliases:["TSType","TSBaseType"],visitor:["literal"],fields:{literal:{validate:function(){let e=(0,W.assertNodeType)("NumericLiteral","BigIntLiteral"),t=(0,W.assertOneOf)("-"),r=(0,W.assertNodeType)("NumericLiteral","StringLiteral","BooleanLiteral","BigIntLiteral","TemplateLiteral");function n(s,i,a){(0,tj.default)("UnaryExpression",a)?(t(a,"operator",a.operator),e(a,"argument",a.argument)):r(s,i,a)}return n.oneOfNodeTypes=["NumericLiteral","StringLiteral","BooleanLiteral","BigIntLiteral","TemplateLiteral","UnaryExpression"],n}()}}});Ce("TSExpressionWithTypeArguments",{aliases:["TSType"],visitor:["expression","typeParameters"],fields:{expression:(0,W.validateType)("TSEntityName"),typeParameters:(0,W.validateOptionalType)("TSTypeParameterInstantiation")}});Ce("TSInterfaceDeclaration",{aliases:["Statement","Declaration"],visitor:["id","typeParameters","extends","body"],fields:{declare:(0,W.validateOptional)(vr),id:(0,W.validateType)("Identifier"),typeParameters:(0,W.validateOptionalType)("TSTypeParameterDeclaration"),extends:(0,W.validateOptional)((0,W.arrayOfType)("TSExpressionWithTypeArguments")),body:(0,W.validateType)("TSInterfaceBody")}});Ce("TSInterfaceBody",{visitor:["body"],fields:{body:(0,W.validateArrayOfType)("TSTypeElement")}});Ce("TSTypeAliasDeclaration",{aliases:["Statement","Declaration"],visitor:["id","typeParameters","typeAnnotation"],fields:{declare:(0,W.validateOptional)(vr),id:(0,W.validateType)("Identifier"),typeParameters:(0,W.validateOptionalType)("TSTypeParameterDeclaration"),typeAnnotation:(0,W.validateType)("TSType")}});Ce("TSInstantiationExpression",{aliases:["Expression"],visitor:["expression","typeParameters"],fields:{expression:(0,W.validateType)("Expression"),typeParameters:(0,W.validateOptionalType)("TSTypeParameterInstantiation")}});var ST={aliases:["Expression","LVal","PatternLike"],visitor:["expression","typeAnnotation"],fields:{expression:(0,W.validateType)("Expression"),typeAnnotation:(0,W.validateType)("TSType")}};Ce("TSAsExpression",ST);Ce("TSSatisfiesExpression",ST);Ce("TSTypeAssertion",{aliases:["Expression","LVal","PatternLike"],visitor:["typeAnnotation","expression"],fields:{typeAnnotation:(0,W.validateType)("TSType"),expression:(0,W.validateType)("Expression")}});Ce("TSEnumBody",{visitor:["members"],fields:{members:(0,W.validateArrayOfType)("TSEnumMember")}});Ce("TSEnumDeclaration",{aliases:["Statement","Declaration"],visitor:["id","members"],fields:{declare:(0,W.validateOptional)(vr),const:(0,W.validateOptional)(vr),id:(0,W.validateType)("Identifier"),members:(0,W.validateArrayOfType)("TSEnumMember"),initializer:(0,W.validateOptionalType)("Expression"),body:(0,W.validateOptionalType)("TSEnumBody")}});Ce("TSEnumMember",{visitor:["id","initializer"],fields:{id:(0,W.validateType)("Identifier","StringLiteral"),initializer:(0,W.validateOptionalType)("Expression")}});Ce("TSModuleDeclaration",{aliases:["Statement","Declaration"],visitor:["id","body"],fields:Object.assign({kind:{validate:(0,W.assertOneOf)("global","module","namespace")},declare:(0,W.validateOptional)(vr)},{global:(0,W.validateOptional)(vr)},{id:(0,W.validateType)("Identifier","StringLiteral"),body:(0,W.validateType)("TSModuleBlock","TSModuleDeclaration")})});Ce("TSModuleBlock",{aliases:["Scopable","Block","BlockParent","FunctionParent"],visitor:["body"],fields:{body:(0,W.validateArrayOfType)("Statement")}});Ce("TSImportType",{aliases:["TSType"],builder:["argument","qualifier","typeParameters"],visitor:["argument","options","qualifier","typeParameters"],fields:{argument:(0,W.validateType)("StringLiteral"),qualifier:(0,W.validateOptionalType)("TSEntityName"),typeParameters:(0,W.validateOptionalType)("TSTypeParameterInstantiation"),options:{validate:(0,W.assertNodeType)("Expression"),optional:!0}}});Ce("TSImportEqualsDeclaration",{aliases:["Statement","Declaration"],visitor:["id","moduleReference"],fields:Object.assign({},{isExport:(0,W.validate)(vr)},{id:(0,W.validateType)("Identifier"),moduleReference:(0,W.validateType)("TSEntityName","TSExternalModuleReference"),importKind:{validate:(0,W.assertOneOf)("type","value"),optional:!0}})});Ce("TSExternalModuleReference",{visitor:["expression"],fields:{expression:(0,W.validateType)("StringLiteral")}});Ce("TSNonNullExpression",{aliases:["Expression","LVal","PatternLike"],visitor:["expression"],fields:{expression:(0,W.validateType)("Expression")}});Ce("TSExportAssignment",{aliases:["Statement"],visitor:["expression"],fields:{expression:(0,W.validateType)("Expression")}});Ce("TSNamespaceExportDeclaration",{aliases:["Statement"],visitor:["id"],fields:{id:(0,W.validateType)("Identifier")}});Ce("TSTypeAnnotation",{visitor:["typeAnnotation"],fields:{typeAnnotation:{validate:(0,W.assertNodeType)("TSType")}}});Ce("TSTypeParameterInstantiation",{visitor:["params"],fields:{params:(0,W.validateArrayOfType)("TSType")}});Ce("TSTypeParameterDeclaration",{visitor:["params"],fields:{params:(0,W.validateArrayOfType)("TSTypeParameter")}});Ce("TSTypeParameter",{builder:["constraint","default","name"],visitor:["constraint","default"],fields:{name:{validate:(0,W.assertValueType)("string")},in:{validate:(0,W.assertValueType)("boolean"),optional:!0},out:{validate:(0,W.assertValueType)("boolean"),optional:!0},const:{validate:(0,W.assertValueType)("boolean"),optional:!0},constraint:{validate:(0,W.assertNodeType)("TSType"),optional:!0},default:{validate:(0,W.assertNodeType)("TSType"),optional:!0}}})});var vT=T(Gl=>{"use strict";Object.defineProperty(Gl,"__esModule",{value:!0});Gl.DEPRECATED_ALIASES=void 0;var DSe=Gl.DEPRECATED_ALIASES={ModuleDeclaration:"ImportOrExportDeclaration"}});var Fr=T(mr=>{"use strict";Object.defineProperty(mr,"__esModule",{value:!0});Object.defineProperty(mr,"ALIAS_KEYS",{enumerable:!0,get:function(){return Lr.ALIAS_KEYS}});Object.defineProperty(mr,"BUILDER_KEYS",{enumerable:!0,get:function(){return Lr.BUILDER_KEYS}});Object.defineProperty(mr,"DEPRECATED_ALIASES",{enumerable:!0,get:function(){return Ed.DEPRECATED_ALIASES}});Object.defineProperty(mr,"DEPRECATED_KEYS",{enumerable:!0,get:function(){return Lr.DEPRECATED_KEYS}});Object.defineProperty(mr,"FLIPPED_ALIAS_KEYS",{enumerable:!0,get:function(){return Lr.FLIPPED_ALIAS_KEYS}});Object.defineProperty(mr,"NODE_FIELDS",{enumerable:!0,get:function(){return Lr.NODE_FIELDS}});Object.defineProperty(mr,"NODE_PARENT_VALIDATIONS",{enumerable:!0,get:function(){return Lr.NODE_PARENT_VALIDATIONS}});Object.defineProperty(mr,"PLACEHOLDERS",{enumerable:!0,get:function(){return Td.PLACEHOLDERS}});Object.defineProperty(mr,"PLACEHOLDERS_ALIAS",{enumerable:!0,get:function(){return Td.PLACEHOLDERS_ALIAS}});Object.defineProperty(mr,"PLACEHOLDERS_FLIPPED_ALIAS",{enumerable:!0,get:function(){return Td.PLACEHOLDERS_FLIPPED_ALIAS}});mr.TYPES=void 0;Object.defineProperty(mr,"VISITOR_KEYS",{enumerable:!0,get:function(){return Lr.VISITOR_KEYS}});to();pT();fT();dT();hT();xT();var Lr=gn(),Td=gd(),Ed=vT();Object.keys(Ed.DEPRECATED_ALIASES).forEach(e=>{Lr.FLIPPED_ALIAS_KEYS[e]=Lr.FLIPPED_ALIAS_KEYS[Ed.DEPRECATED_ALIASES[e]]});var ISe=mr.TYPES=[].concat(Object.keys(Lr.VISITOR_KEYS),Object.keys(Lr.FLIPPED_ALIAS_KEYS),Object.keys(Lr.DEPRECATED_KEYS))});var Vl=T(Gi=>{"use strict";Object.defineProperty(Gi,"__esModule",{value:!0});Gi.default=nj;Gi.validateChild=AT;Gi.validateField=PT;Gi.validateInternal=sj;var ro=Fr();function nj(e,t,r){if(!e)return;let n=ro.NODE_FIELDS[e.type];if(!n)return;let s=n[t];PT(e,t,r,s),AT(e,t,r)}function sj(e,t,r,n,s){if(e!=null&&e.validate&&!(e.optional&&n==null)&&(e.validate(t,r,n),s)){var i;let a=n.type;if(a==null)return;(i=ro.NODE_PARENT_VALIDATIONS[a])==null||i.call(ro.NODE_PARENT_VALIDATIONS,t,r,n)}}function PT(e,t,r,n){n!=null&&n.validate&&(n.optional&&r==null||n.validate(e,t,r))}function AT(e,t,r){var n;let s=r==null?void 0:r.type;s!=null&&((n=ro.NODE_PARENT_VALIDATIONS[s])==null||n.call(ro.NODE_PARENT_VALIDATIONS,e,t,r))}});var Sd=T(b=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0});b.anyTypeAnnotation=j6;b.argumentPlaceholder=fR;b.arrayExpression=oj;b.arrayPattern=r6;b.arrayTypeAnnotation=M6;b.arrowFunctionExpression=n6;b.assignmentExpression=lj;b.assignmentPattern=t6;b.awaitExpression=A6;b.bigIntLiteral=D6;b.binaryExpression=uj;b.bindExpression=dR;b.blockStatement=dj;b.booleanLiteral=Bj;b.booleanLiteralTypeAnnotation=q6;b.booleanTypeAnnotation=R6;b.breakStatement=hj;b.callExpression=mj;b.catchClause=yj;b.classAccessorProperty=N6;b.classBody=s6;b.classDeclaration=a6;b.classExpression=i6;b.classImplements=V6;b.classMethod=b6;b.classPrivateMethod=k6;b.classPrivateProperty=B6;b.classProperty=O6;b.conditionalExpression=gj;b.continueStatement=bj;b.debuggerStatement=Ej;b.decimalLiteral=TR;b.declareClass=$6;b.declareExportAllDeclaration=Q6;b.declareExportDeclaration=z6;b.declareFunction=W6;b.declareInterface=K6;b.declareModule=G6;b.declareModuleExports=H6;b.declareOpaqueType=X6;b.declareTypeAlias=Y6;b.declareVariable=J6;b.declaredPredicate=Z6;b.decorator=mR;b.directive=pj;b.directiveLiteral=fj;b.doExpression=yR;b.doWhileStatement=Tj;b.emptyStatement=Sj;b.emptyTypeAnnotation=cM;b.enumBooleanBody=MM;b.enumBooleanMember=VM;b.enumDeclaration=jM;b.enumDefaultedMember=KM;b.enumNumberBody=RM;b.enumNumberMember=$M;b.enumStringBody=qM;b.enumStringMember=WM;b.enumSymbolBody=UM;b.existsTypeAnnotation=eM;b.exportAllDeclaration=o6;b.exportDefaultDeclaration=l6;b.exportDefaultSpecifier=gR;b.exportNamedDeclaration=u6;b.exportNamespaceSpecifier=w6;b.exportSpecifier=c6;b.expressionStatement=xj;b.file=vj;b.forInStatement=Pj;b.forOfStatement=p6;b.forStatement=Aj;b.functionDeclaration=Cj;b.functionExpression=Dj;b.functionTypeAnnotation=tM;b.functionTypeParam=rM;b.genericTypeAnnotation=nM;b.identifier=wj;b.ifStatement=Ij;b.import=C6;b.importAttribute=hR;b.importDeclaration=f6;b.importDefaultSpecifier=d6;b.importExpression=y6;b.importNamespaceSpecifier=h6;b.importSpecifier=m6;b.indexedAccessType=GM;b.inferredPredicate=sM;b.interfaceDeclaration=aM;b.interfaceExtends=iM;b.interfaceTypeAnnotation=oM;b.interpreterDirective=cj;b.intersectionTypeAnnotation=lM;b.jSXAttribute=b.jsxAttribute=YM;b.jSXClosingElement=b.jsxClosingElement=XM;b.jSXClosingFragment=b.jsxClosingFragment=lR;b.jSXElement=b.jsxElement=JM;b.jSXEmptyExpression=b.jsxEmptyExpression=zM;b.jSXExpressionContainer=b.jsxExpressionContainer=QM;b.jSXFragment=b.jsxFragment=aR;b.jSXIdentifier=b.jsxIdentifier=eR;b.jSXMemberExpression=b.jsxMemberExpression=tR;b.jSXNamespacedName=b.jsxNamespacedName=rR;b.jSXOpeningElement=b.jsxOpeningElement=nR;b.jSXOpeningFragment=b.jsxOpeningFragment=oR;b.jSXSpreadAttribute=b.jsxSpreadAttribute=sR;b.jSXSpreadChild=b.jsxSpreadChild=ZM;b.jSXText=b.jsxText=iR;b.labeledStatement=_j;b.logicalExpression=kj;b.memberExpression=Fj;b.metaProperty=g6;b.mixedTypeAnnotation=uM;b.moduleExpression=SR;b.newExpression=Lj;b.noop=uR;b.nullLiteral=Nj;b.nullLiteralTypeAnnotation=U6;b.nullableTypeAnnotation=pM;b.numberLiteral=R5;b.numberLiteralTypeAnnotation=fM;b.numberTypeAnnotation=dM;b.numericLiteral=CT;b.objectExpression=Mj;b.objectMethod=Rj;b.objectPattern=E6;b.objectProperty=qj;b.objectTypeAnnotation=hM;b.objectTypeCallProperty=yM;b.objectTypeIndexer=gM;b.objectTypeInternalSlot=mM;b.objectTypeProperty=bM;b.objectTypeSpreadProperty=EM;b.opaqueType=TM;b.optionalCallExpression=_6;b.optionalIndexedAccessType=HM;b.optionalMemberExpression=I6;b.parenthesizedExpression=$j;b.pipelineBareFunction=PR;b.pipelinePrimaryTopicReference=AR;b.pipelineTopicExpression=vR;b.placeholder=cR;b.privateName=F6;b.program=jj;b.qualifiedTypeIdentifier=SM;b.recordExpression=bR;b.regExpLiteral=DT;b.regexLiteral=q5;b.restElement=wT;b.restProperty=U5;b.returnStatement=Uj;b.sequenceExpression=Vj;b.spreadElement=IT;b.spreadProperty=V5;b.staticBlock=L6;b.stringLiteral=Oj;b.stringLiteralTypeAnnotation=xM;b.stringTypeAnnotation=vM;b.super=T6;b.switchCase=Wj;b.switchStatement=Kj;b.symbolTypeAnnotation=PM;b.taggedTemplateExpression=S6;b.templateElement=x6;b.templateLiteral=v6;b.thisExpression=Gj;b.thisTypeAnnotation=AM;b.throwStatement=Hj;b.topicReference=xR;b.tryStatement=Yj;b.tSAnyKeyword=b.tsAnyKeyword=FR;b.tSArrayType=b.tsArrayType=t5;b.tSAsExpression=b.tsAsExpression=S5;b.tSBigIntKeyword=b.tsBigIntKeyword=jR;b.tSBooleanKeyword=b.tsBooleanKeyword=LR;b.tSCallSignatureDeclaration=b.tsCallSignatureDeclaration=_R;b.tSConditionalType=b.tsConditionalType=l5;b.tSConstructSignatureDeclaration=b.tsConstructSignatureDeclaration=OR;b.tSConstructorType=b.tsConstructorType=JR;b.tSDeclareFunction=b.tsDeclareFunction=DR;b.tSDeclareMethod=b.tsDeclareMethod=wR;b.tSEnumBody=b.tsEnumBody=P5;b.tSEnumDeclaration=b.tsEnumDeclaration=A5;b.tSEnumMember=b.tsEnumMember=C5;b.tSExportAssignment=b.tsExportAssignment=B5;b.tSExpressionWithTypeArguments=b.tsExpressionWithTypeArguments=y5;b.tSExternalModuleReference=b.tsExternalModuleReference=O5;b.tSFunctionType=b.tsFunctionType=XR;b.tSImportEqualsDeclaration=b.tsImportEqualsDeclaration=_5;b.tSImportType=b.tsImportType=I5;b.tSIndexSignature=b.tsIndexSignature=kR;b.tSIndexedAccessType=b.tsIndexedAccessType=f5;b.tSInferType=b.tsInferType=u5;b.tSInstantiationExpression=b.tsInstantiationExpression=T5;b.tSInterfaceBody=b.tsInterfaceBody=b5;b.tSInterfaceDeclaration=b.tsInterfaceDeclaration=g5;b.tSIntersectionType=b.tsIntersectionType=o5;b.tSIntrinsicKeyword=b.tsIntrinsicKeyword=MR;b.tSLiteralType=b.tsLiteralType=m5;b.tSMappedType=b.tsMappedType=d5;b.tSMethodSignature=b.tsMethodSignature=BR;b.tSModuleBlock=b.tsModuleBlock=w5;b.tSModuleDeclaration=b.tsModuleDeclaration=D5;b.tSNamedTupleMember=b.tsNamedTupleMember=i5;b.tSNamespaceExportDeclaration=b.tsNamespaceExportDeclaration=k5;b.tSNeverKeyword=b.tsNeverKeyword=RR;b.tSNonNullExpression=b.tsNonNullExpression=N5;b.tSNullKeyword=b.tsNullKeyword=qR;b.tSNumberKeyword=b.tsNumberKeyword=UR;b.tSObjectKeyword=b.tsObjectKeyword=VR;b.tSOptionalType=b.tsOptionalType=n5;b.tSParameterProperty=b.tsParameterProperty=CR;b.tSParenthesizedType=b.tsParenthesizedType=c5;b.tSPropertySignature=b.tsPropertySignature=NR;b.tSQualifiedName=b.tsQualifiedName=IR;b.tSRestType=b.tsRestType=s5;b.tSSatisfiesExpression=b.tsSatisfiesExpression=x5;b.tSStringKeyword=b.tsStringKeyword=$R;b.tSSymbolKeyword=b.tsSymbolKeyword=WR;b.tSTemplateLiteralType=b.tsTemplateLiteralType=h5;b.tSThisType=b.tsThisType=YR;b.tSTupleType=b.tsTupleType=r5;b.tSTypeAliasDeclaration=b.tsTypeAliasDeclaration=E5;b.tSTypeAnnotation=b.tsTypeAnnotation=F5;b.tSTypeAssertion=b.tsTypeAssertion=v5;b.tSTypeLiteral=b.tsTypeLiteral=e5;b.tSTypeOperator=b.tsTypeOperator=p5;b.tSTypeParameter=b.tsTypeParameter=M5;b.tSTypeParameterDeclaration=b.tsTypeParameterDeclaration=j5;b.tSTypeParameterInstantiation=b.tsTypeParameterInstantiation=L5;b.tSTypePredicate=b.tsTypePredicate=QR;b.tSTypeQuery=b.tsTypeQuery=ZR;b.tSTypeReference=b.tsTypeReference=zR;b.tSUndefinedKeyword=b.tsUndefinedKeyword=KR;b.tSUnionType=b.tsUnionType=a5;b.tSUnknownKeyword=b.tsUnknownKeyword=GR;b.tSVoidKeyword=b.tsVoidKeyword=HR;b.tupleExpression=ER;b.tupleTypeAnnotation=CM;b.typeAlias=wM;b.typeAnnotation=IM;b.typeCastExpression=_M;b.typeParameter=OM;b.typeParameterDeclaration=NM;b.typeParameterInstantiation=BM;b.typeofTypeAnnotation=DM;b.unaryExpression=Xj;b.unionTypeAnnotation=kM;b.updateExpression=Jj;b.v8IntrinsicIdentifier=pR;b.variableDeclaration=zj;b.variableDeclarator=Qj;b.variance=FM;b.voidTypeAnnotation=LM;b.whileStatement=Zj;b.withStatement=e6;b.yieldExpression=P6;var ij=Vl(),Hl=Ya(),aj=gn(),{validateInternal:h}=ij,{NODE_FIELDS:k}=aj;function oj(e=[]){let t={type:"ArrayExpression",elements:e},r=k.ArrayExpression;return h(r.elements,t,"elements",e,1),t}function lj(e,t,r){let n={type:"AssignmentExpression",operator:e,left:t,right:r},s=k.AssignmentExpression;return h(s.operator,n,"operator",e),h(s.left,n,"left",t,1),h(s.right,n,"right",r,1),n}function uj(e,t,r){let n={type:"BinaryExpression",operator:e,left:t,right:r},s=k.BinaryExpression;return h(s.operator,n,"operator",e),h(s.left,n,"left",t,1),h(s.right,n,"right",r,1),n}function cj(e){let t={type:"InterpreterDirective",value:e},r=k.InterpreterDirective;return h(r.value,t,"value",e),t}function pj(e){let t={type:"Directive",value:e},r=k.Directive;return h(r.value,t,"value",e,1),t}function fj(e){let t={type:"DirectiveLiteral",value:e},r=k.DirectiveLiteral;return h(r.value,t,"value",e),t}function dj(e,t=[]){let r={type:"BlockStatement",body:e,directives:t},n=k.BlockStatement;return h(n.body,r,"body",e,1),h(n.directives,r,"directives",t,1),r}function hj(e=null){let t={type:"BreakStatement",label:e},r=k.BreakStatement;return h(r.label,t,"label",e,1),t}function mj(e,t){let r={type:"CallExpression",callee:e,arguments:t},n=k.CallExpression;return h(n.callee,r,"callee",e,1),h(n.arguments,r,"arguments",t,1),r}function yj(e=null,t){let r={type:"CatchClause",param:e,body:t},n=k.CatchClause;return h(n.param,r,"param",e,1),h(n.body,r,"body",t,1),r}function gj(e,t,r){let n={type:"ConditionalExpression",test:e,consequent:t,alternate:r},s=k.ConditionalExpression;return h(s.test,n,"test",e,1),h(s.consequent,n,"consequent",t,1),h(s.alternate,n,"alternate",r,1),n}function bj(e=null){let t={type:"ContinueStatement",label:e},r=k.ContinueStatement;return h(r.label,t,"label",e,1),t}function Ej(){return{type:"DebuggerStatement"}}function Tj(e,t){let r={type:"DoWhileStatement",test:e,body:t},n=k.DoWhileStatement;return h(n.test,r,"test",e,1),h(n.body,r,"body",t,1),r}function Sj(){return{type:"EmptyStatement"}}function xj(e){let t={type:"ExpressionStatement",expression:e},r=k.ExpressionStatement;return h(r.expression,t,"expression",e,1),t}function vj(e,t=null,r=null){let n={type:"File",program:e,comments:t,tokens:r},s=k.File;return h(s.program,n,"program",e,1),h(s.comments,n,"comments",t,1),h(s.tokens,n,"tokens",r),n}function Pj(e,t,r){let n={type:"ForInStatement",left:e,right:t,body:r},s=k.ForInStatement;return h(s.left,n,"left",e,1),h(s.right,n,"right",t,1),h(s.body,n,"body",r,1),n}function Aj(e=null,t=null,r=null,n){let s={type:"ForStatement",init:e,test:t,update:r,body:n},i=k.ForStatement;return h(i.init,s,"init",e,1),h(i.test,s,"test",t,1),h(i.update,s,"update",r,1),h(i.body,s,"body",n,1),s}function Cj(e=null,t,r,n=!1,s=!1){let i={type:"FunctionDeclaration",id:e,params:t,body:r,generator:n,async:s},a=k.FunctionDeclaration;return h(a.id,i,"id",e,1),h(a.params,i,"params",t,1),h(a.body,i,"body",r,1),h(a.generator,i,"generator",n),h(a.async,i,"async",s),i}function Dj(e=null,t,r,n=!1,s=!1){let i={type:"FunctionExpression",id:e,params:t,body:r,generator:n,async:s},a=k.FunctionExpression;return h(a.id,i,"id",e,1),h(a.params,i,"params",t,1),h(a.body,i,"body",r,1),h(a.generator,i,"generator",n),h(a.async,i,"async",s),i}function wj(e){let t={type:"Identifier",name:e},r=k.Identifier;return h(r.name,t,"name",e),t}function Ij(e,t,r=null){let n={type:"IfStatement",test:e,consequent:t,alternate:r},s=k.IfStatement;return h(s.test,n,"test",e,1),h(s.consequent,n,"consequent",t,1),h(s.alternate,n,"alternate",r,1),n}function _j(e,t){let r={type:"LabeledStatement",label:e,body:t},n=k.LabeledStatement;return h(n.label,r,"label",e,1),h(n.body,r,"body",t,1),r}function Oj(e){let t={type:"StringLiteral",value:e},r=k.StringLiteral;return h(r.value,t,"value",e),t}function CT(e){let t={type:"NumericLiteral",value:e},r=k.NumericLiteral;return h(r.value,t,"value",e),t}function Nj(){return{type:"NullLiteral"}}function Bj(e){let t={type:"BooleanLiteral",value:e},r=k.BooleanLiteral;return h(r.value,t,"value",e),t}function DT(e,t=""){let r={type:"RegExpLiteral",pattern:e,flags:t},n=k.RegExpLiteral;return h(n.pattern,r,"pattern",e),h(n.flags,r,"flags",t),r}function kj(e,t,r){let n={type:"LogicalExpression",operator:e,left:t,right:r},s=k.LogicalExpression;return h(s.operator,n,"operator",e),h(s.left,n,"left",t,1),h(s.right,n,"right",r,1),n}function Fj(e,t,r=!1,n=null){let s={type:"MemberExpression",object:e,property:t,computed:r,optional:n},i=k.MemberExpression;return h(i.object,s,"object",e,1),h(i.property,s,"property",t,1),h(i.computed,s,"computed",r),h(i.optional,s,"optional",n),s}function Lj(e,t){let r={type:"NewExpression",callee:e,arguments:t},n=k.NewExpression;return h(n.callee,r,"callee",e,1),h(n.arguments,r,"arguments",t,1),r}function jj(e,t=[],r="script",n=null){let s={type:"Program",body:e,directives:t,sourceType:r,interpreter:n},i=k.Program;return h(i.body,s,"body",e,1),h(i.directives,s,"directives",t,1),h(i.sourceType,s,"sourceType",r),h(i.interpreter,s,"interpreter",n,1),s}function Mj(e){let t={type:"ObjectExpression",properties:e},r=k.ObjectExpression;return h(r.properties,t,"properties",e,1),t}function Rj(e="method",t,r,n,s=!1,i=!1,a=!1){let o={type:"ObjectMethod",kind:e,key:t,params:r,body:n,computed:s,generator:i,async:a},l=k.ObjectMethod;return h(l.kind,o,"kind",e),h(l.key,o,"key",t,1),h(l.params,o,"params",r,1),h(l.body,o,"body",n,1),h(l.computed,o,"computed",s),h(l.generator,o,"generator",i),h(l.async,o,"async",a),o}function qj(e,t,r=!1,n=!1,s=null){let i={type:"ObjectProperty",key:e,value:t,computed:r,shorthand:n,decorators:s},a=k.ObjectProperty;return h(a.key,i,"key",e,1),h(a.value,i,"value",t,1),h(a.computed,i,"computed",r),h(a.shorthand,i,"shorthand",n),h(a.decorators,i,"decorators",s,1),i}function wT(e){let t={type:"RestElement",argument:e},r=k.RestElement;return h(r.argument,t,"argument",e,1),t}function Uj(e=null){let t={type:"ReturnStatement",argument:e},r=k.ReturnStatement;return h(r.argument,t,"argument",e,1),t}function Vj(e){let t={type:"SequenceExpression",expressions:e},r=k.SequenceExpression;return h(r.expressions,t,"expressions",e,1),t}function $j(e){let t={type:"ParenthesizedExpression",expression:e},r=k.ParenthesizedExpression;return h(r.expression,t,"expression",e,1),t}function Wj(e=null,t){let r={type:"SwitchCase",test:e,consequent:t},n=k.SwitchCase;return h(n.test,r,"test",e,1),h(n.consequent,r,"consequent",t,1),r}function Kj(e,t){let r={type:"SwitchStatement",discriminant:e,cases:t},n=k.SwitchStatement;return h(n.discriminant,r,"discriminant",e,1),h(n.cases,r,"cases",t,1),r}function Gj(){return{type:"ThisExpression"}}function Hj(e){let t={type:"ThrowStatement",argument:e},r=k.ThrowStatement;return h(r.argument,t,"argument",e,1),t}function Yj(e,t=null,r=null){let n={type:"TryStatement",block:e,handler:t,finalizer:r},s=k.TryStatement;return h(s.block,n,"block",e,1),h(s.handler,n,"handler",t,1),h(s.finalizer,n,"finalizer",r,1),n}function Xj(e,t,r=!0){let n={type:"UnaryExpression",operator:e,argument:t,prefix:r},s=k.UnaryExpression;return h(s.operator,n,"operator",e),h(s.argument,n,"argument",t,1),h(s.prefix,n,"prefix",r),n}function Jj(e,t,r=!1){let n={type:"UpdateExpression",operator:e,argument:t,prefix:r},s=k.UpdateExpression;return h(s.operator,n,"operator",e),h(s.argument,n,"argument",t,1),h(s.prefix,n,"prefix",r),n}function zj(e,t){let r={type:"VariableDeclaration",kind:e,declarations:t},n=k.VariableDeclaration;return h(n.kind,r,"kind",e),h(n.declarations,r,"declarations",t,1),r}function Qj(e,t=null){let r={type:"VariableDeclarator",id:e,init:t},n=k.VariableDeclarator;return h(n.id,r,"id",e,1),h(n.init,r,"init",t,1),r}function Zj(e,t){let r={type:"WhileStatement",test:e,body:t},n=k.WhileStatement;return h(n.test,r,"test",e,1),h(n.body,r,"body",t,1),r}function e6(e,t){let r={type:"WithStatement",object:e,body:t},n=k.WithStatement;return h(n.object,r,"object",e,1),h(n.body,r,"body",t,1),r}function t6(e,t){let r={type:"AssignmentPattern",left:e,right:t},n=k.AssignmentPattern;return h(n.left,r,"left",e,1),h(n.right,r,"right",t,1),r}function r6(e){let t={type:"ArrayPattern",elements:e},r=k.ArrayPattern;return h(r.elements,t,"elements",e,1),t}function n6(e,t,r=!1){let n={type:"ArrowFunctionExpression",params:e,body:t,async:r,expression:null},s=k.ArrowFunctionExpression;return h(s.params,n,"params",e,1),h(s.body,n,"body",t,1),h(s.async,n,"async",r),n}function s6(e){let t={type:"ClassBody",body:e},r=k.ClassBody;return h(r.body,t,"body",e,1),t}function i6(e=null,t=null,r,n=null){let s={type:"ClassExpression",id:e,superClass:t,body:r,decorators:n},i=k.ClassExpression;return h(i.id,s,"id",e,1),h(i.superClass,s,"superClass",t,1),h(i.body,s,"body",r,1),h(i.decorators,s,"decorators",n,1),s}function a6(e=null,t=null,r,n=null){let s={type:"ClassDeclaration",id:e,superClass:t,body:r,decorators:n},i=k.ClassDeclaration;return h(i.id,s,"id",e,1),h(i.superClass,s,"superClass",t,1),h(i.body,s,"body",r,1),h(i.decorators,s,"decorators",n,1),s}function o6(e){let t={type:"ExportAllDeclaration",source:e},r=k.ExportAllDeclaration;return h(r.source,t,"source",e,1),t}function l6(e){let t={type:"ExportDefaultDeclaration",declaration:e},r=k.ExportDefaultDeclaration;return h(r.declaration,t,"declaration",e,1),t}function u6(e=null,t=[],r=null){let n={type:"ExportNamedDeclaration",declaration:e,specifiers:t,source:r},s=k.ExportNamedDeclaration;return h(s.declaration,n,"declaration",e,1),h(s.specifiers,n,"specifiers",t,1),h(s.source,n,"source",r,1),n}function c6(e,t){let r={type:"ExportSpecifier",local:e,exported:t},n=k.ExportSpecifier;return h(n.local,r,"local",e,1),h(n.exported,r,"exported",t,1),r}function p6(e,t,r,n=!1){let s={type:"ForOfStatement",left:e,right:t,body:r,await:n},i=k.ForOfStatement;return h(i.left,s,"left",e,1),h(i.right,s,"right",t,1),h(i.body,s,"body",r,1),h(i.await,s,"await",n),s}function f6(e,t){let r={type:"ImportDeclaration",specifiers:e,source:t},n=k.ImportDeclaration;return h(n.specifiers,r,"specifiers",e,1),h(n.source,r,"source",t,1),r}function d6(e){let t={type:"ImportDefaultSpecifier",local:e},r=k.ImportDefaultSpecifier;return h(r.local,t,"local",e,1),t}function h6(e){let t={type:"ImportNamespaceSpecifier",local:e},r=k.ImportNamespaceSpecifier;return h(r.local,t,"local",e,1),t}function m6(e,t){let r={type:"ImportSpecifier",local:e,imported:t},n=k.ImportSpecifier;return h(n.local,r,"local",e,1),h(n.imported,r,"imported",t,1),r}function y6(e,t=null){let r={type:"ImportExpression",source:e,options:t},n=k.ImportExpression;return h(n.source,r,"source",e,1),h(n.options,r,"options",t,1),r}function g6(e,t){let r={type:"MetaProperty",meta:e,property:t},n=k.MetaProperty;return h(n.meta,r,"meta",e,1),h(n.property,r,"property",t,1),r}function b6(e="method",t,r,n,s=!1,i=!1,a=!1,o=!1){let l={type:"ClassMethod",kind:e,key:t,params:r,body:n,computed:s,static:i,generator:a,async:o},u=k.ClassMethod;return h(u.kind,l,"kind",e),h(u.key,l,"key",t,1),h(u.params,l,"params",r,1),h(u.body,l,"body",n,1),h(u.computed,l,"computed",s),h(u.static,l,"static",i),h(u.generator,l,"generator",a),h(u.async,l,"async",o),l}function E6(e){let t={type:"ObjectPattern",properties:e},r=k.ObjectPattern;return h(r.properties,t,"properties",e,1),t}function IT(e){let t={type:"SpreadElement",argument:e},r=k.SpreadElement;return h(r.argument,t,"argument",e,1),t}function T6(){return{type:"Super"}}function S6(e,t){let r={type:"TaggedTemplateExpression",tag:e,quasi:t},n=k.TaggedTemplateExpression;return h(n.tag,r,"tag",e,1),h(n.quasi,r,"quasi",t,1),r}function x6(e,t=!1){let r={type:"TemplateElement",value:e,tail:t},n=k.TemplateElement;return h(n.value,r,"value",e),h(n.tail,r,"tail",t),r}function v6(e,t){let r={type:"TemplateLiteral",quasis:e,expressions:t},n=k.TemplateLiteral;return h(n.quasis,r,"quasis",e,1),h(n.expressions,r,"expressions",t,1),r}function P6(e=null,t=!1){let r={type:"YieldExpression",argument:e,delegate:t},n=k.YieldExpression;return h(n.argument,r,"argument",e,1),h(n.delegate,r,"delegate",t),r}function A6(e){let t={type:"AwaitExpression",argument:e},r=k.AwaitExpression;return h(r.argument,t,"argument",e,1),t}function C6(){return{type:"Import"}}function D6(e){let t={type:"BigIntLiteral",value:e},r=k.BigIntLiteral;return h(r.value,t,"value",e),t}function w6(e){let t={type:"ExportNamespaceSpecifier",exported:e},r=k.ExportNamespaceSpecifier;return h(r.exported,t,"exported",e,1),t}function I6(e,t,r=!1,n){let s={type:"OptionalMemberExpression",object:e,property:t,computed:r,optional:n},i=k.OptionalMemberExpression;return h(i.object,s,"object",e,1),h(i.property,s,"property",t,1),h(i.computed,s,"computed",r),h(i.optional,s,"optional",n),s}function _6(e,t,r){let n={type:"OptionalCallExpression",callee:e,arguments:t,optional:r},s=k.OptionalCallExpression;return h(s.callee,n,"callee",e,1),h(s.arguments,n,"arguments",t,1),h(s.optional,n,"optional",r),n}function O6(e,t=null,r=null,n=null,s=!1,i=!1){let a={type:"ClassProperty",key:e,value:t,typeAnnotation:r,decorators:n,computed:s,static:i},o=k.ClassProperty;return h(o.key,a,"key",e,1),h(o.value,a,"value",t,1),h(o.typeAnnotation,a,"typeAnnotation",r,1),h(o.decorators,a,"decorators",n,1),h(o.computed,a,"computed",s),h(o.static,a,"static",i),a}function N6(e,t=null,r=null,n=null,s=!1,i=!1){let a={type:"ClassAccessorProperty",key:e,value:t,typeAnnotation:r,decorators:n,computed:s,static:i},o=k.ClassAccessorProperty;return h(o.key,a,"key",e,1),h(o.value,a,"value",t,1),h(o.typeAnnotation,a,"typeAnnotation",r,1),h(o.decorators,a,"decorators",n,1),h(o.computed,a,"computed",s),h(o.static,a,"static",i),a}function B6(e,t=null,r=null,n=!1){let s={type:"ClassPrivateProperty",key:e,value:t,decorators:r,static:n},i=k.ClassPrivateProperty;return h(i.key,s,"key",e,1),h(i.value,s,"value",t,1),h(i.decorators,s,"decorators",r,1),h(i.static,s,"static",n),s}function k6(e="method",t,r,n,s=!1){let i={type:"ClassPrivateMethod",kind:e,key:t,params:r,body:n,static:s},a=k.ClassPrivateMethod;return h(a.kind,i,"kind",e),h(a.key,i,"key",t,1),h(a.params,i,"params",r,1),h(a.body,i,"body",n,1),h(a.static,i,"static",s),i}function F6(e){let t={type:"PrivateName",id:e},r=k.PrivateName;return h(r.id,t,"id",e,1),t}function L6(e){let t={type:"StaticBlock",body:e},r=k.StaticBlock;return h(r.body,t,"body",e,1),t}function j6(){return{type:"AnyTypeAnnotation"}}function M6(e){let t={type:"ArrayTypeAnnotation",elementType:e},r=k.ArrayTypeAnnotation;return h(r.elementType,t,"elementType",e,1),t}function R6(){return{type:"BooleanTypeAnnotation"}}function q6(e){let t={type:"BooleanLiteralTypeAnnotation",value:e},r=k.BooleanLiteralTypeAnnotation;return h(r.value,t,"value",e),t}function U6(){return{type:"NullLiteralTypeAnnotation"}}function V6(e,t=null){let r={type:"ClassImplements",id:e,typeParameters:t},n=k.ClassImplements;return h(n.id,r,"id",e,1),h(n.typeParameters,r,"typeParameters",t,1),r}function $6(e,t=null,r=null,n){let s={type:"DeclareClass",id:e,typeParameters:t,extends:r,body:n},i=k.DeclareClass;return h(i.id,s,"id",e,1),h(i.typeParameters,s,"typeParameters",t,1),h(i.extends,s,"extends",r,1),h(i.body,s,"body",n,1),s}function W6(e){let t={type:"DeclareFunction",id:e},r=k.DeclareFunction;return h(r.id,t,"id",e,1),t}function K6(e,t=null,r=null,n){let s={type:"DeclareInterface",id:e,typeParameters:t,extends:r,body:n},i=k.DeclareInterface;return h(i.id,s,"id",e,1),h(i.typeParameters,s,"typeParameters",t,1),h(i.extends,s,"extends",r,1),h(i.body,s,"body",n,1),s}function G6(e,t,r=null){let n={type:"DeclareModule",id:e,body:t,kind:r},s=k.DeclareModule;return h(s.id,n,"id",e,1),h(s.body,n,"body",t,1),h(s.kind,n,"kind",r),n}function H6(e){let t={type:"DeclareModuleExports",typeAnnotation:e},r=k.DeclareModuleExports;return h(r.typeAnnotation,t,"typeAnnotation",e,1),t}function Y6(e,t=null,r){let n={type:"DeclareTypeAlias",id:e,typeParameters:t,right:r},s=k.DeclareTypeAlias;return h(s.id,n,"id",e,1),h(s.typeParameters,n,"typeParameters",t,1),h(s.right,n,"right",r,1),n}function X6(e,t=null,r=null){let n={type:"DeclareOpaqueType",id:e,typeParameters:t,supertype:r},s=k.DeclareOpaqueType;return h(s.id,n,"id",e,1),h(s.typeParameters,n,"typeParameters",t,1),h(s.supertype,n,"supertype",r,1),n}function J6(e){let t={type:"DeclareVariable",id:e},r=k.DeclareVariable;return h(r.id,t,"id",e,1),t}function z6(e=null,t=null,r=null,n=null){let s={type:"DeclareExportDeclaration",declaration:e,specifiers:t,source:r,attributes:n},i=k.DeclareExportDeclaration;return h(i.declaration,s,"declaration",e,1),h(i.specifiers,s,"specifiers",t,1),h(i.source,s,"source",r,1),h(i.attributes,s,"attributes",n,1),s}function Q6(e,t=null){let r={type:"DeclareExportAllDeclaration",source:e,attributes:t},n=k.DeclareExportAllDeclaration;return h(n.source,r,"source",e,1),h(n.attributes,r,"attributes",t,1),r}function Z6(e){let t={type:"DeclaredPredicate",value:e},r=k.DeclaredPredicate;return h(r.value,t,"value",e,1),t}function eM(){return{type:"ExistsTypeAnnotation"}}function tM(e=null,t,r=null,n){let s={type:"FunctionTypeAnnotation",typeParameters:e,params:t,rest:r,returnType:n},i=k.FunctionTypeAnnotation;return h(i.typeParameters,s,"typeParameters",e,1),h(i.params,s,"params",t,1),h(i.rest,s,"rest",r,1),h(i.returnType,s,"returnType",n,1),s}function rM(e=null,t){let r={type:"FunctionTypeParam",name:e,typeAnnotation:t},n=k.FunctionTypeParam;return h(n.name,r,"name",e,1),h(n.typeAnnotation,r,"typeAnnotation",t,1),r}function nM(e,t=null){let r={type:"GenericTypeAnnotation",id:e,typeParameters:t},n=k.GenericTypeAnnotation;return h(n.id,r,"id",e,1),h(n.typeParameters,r,"typeParameters",t,1),r}function sM(){return{type:"InferredPredicate"}}function iM(e,t=null){let r={type:"InterfaceExtends",id:e,typeParameters:t},n=k.InterfaceExtends;return h(n.id,r,"id",e,1),h(n.typeParameters,r,"typeParameters",t,1),r}function aM(e,t=null,r=null,n){let s={type:"InterfaceDeclaration",id:e,typeParameters:t,extends:r,body:n},i=k.InterfaceDeclaration;return h(i.id,s,"id",e,1),h(i.typeParameters,s,"typeParameters",t,1),h(i.extends,s,"extends",r,1),h(i.body,s,"body",n,1),s}function oM(e=null,t){let r={type:"InterfaceTypeAnnotation",extends:e,body:t},n=k.InterfaceTypeAnnotation;return h(n.extends,r,"extends",e,1),h(n.body,r,"body",t,1),r}function lM(e){let t={type:"IntersectionTypeAnnotation",types:e},r=k.IntersectionTypeAnnotation;return h(r.types,t,"types",e,1),t}function uM(){return{type:"MixedTypeAnnotation"}}function cM(){return{type:"EmptyTypeAnnotation"}}function pM(e){let t={type:"NullableTypeAnnotation",typeAnnotation:e},r=k.NullableTypeAnnotation;return h(r.typeAnnotation,t,"typeAnnotation",e,1),t}function fM(e){let t={type:"NumberLiteralTypeAnnotation",value:e},r=k.NumberLiteralTypeAnnotation;return h(r.value,t,"value",e),t}function dM(){return{type:"NumberTypeAnnotation"}}function hM(e,t=[],r=[],n=[],s=!1){let i={type:"ObjectTypeAnnotation",properties:e,indexers:t,callProperties:r,internalSlots:n,exact:s},a=k.ObjectTypeAnnotation;return h(a.properties,i,"properties",e,1),h(a.indexers,i,"indexers",t,1),h(a.callProperties,i,"callProperties",r,1),h(a.internalSlots,i,"internalSlots",n,1),h(a.exact,i,"exact",s),i}function mM(e,t,r,n,s){let i={type:"ObjectTypeInternalSlot",id:e,value:t,optional:r,static:n,method:s},a=k.ObjectTypeInternalSlot;return h(a.id,i,"id",e,1),h(a.value,i,"value",t,1),h(a.optional,i,"optional",r),h(a.static,i,"static",n),h(a.method,i,"method",s),i}function yM(e){let t={type:"ObjectTypeCallProperty",value:e,static:null},r=k.ObjectTypeCallProperty;return h(r.value,t,"value",e,1),t}function gM(e=null,t,r,n=null){let s={type:"ObjectTypeIndexer",id:e,key:t,value:r,variance:n,static:null},i=k.ObjectTypeIndexer;return h(i.id,s,"id",e,1),h(i.key,s,"key",t,1),h(i.value,s,"value",r,1),h(i.variance,s,"variance",n,1),s}function bM(e,t,r=null){let n={type:"ObjectTypeProperty",key:e,value:t,variance:r,kind:null,method:null,optional:null,proto:null,static:null},s=k.ObjectTypeProperty;return h(s.key,n,"key",e,1),h(s.value,n,"value",t,1),h(s.variance,n,"variance",r,1),n}function EM(e){let t={type:"ObjectTypeSpreadProperty",argument:e},r=k.ObjectTypeSpreadProperty;return h(r.argument,t,"argument",e,1),t}function TM(e,t=null,r=null,n){let s={type:"OpaqueType",id:e,typeParameters:t,supertype:r,impltype:n},i=k.OpaqueType;return h(i.id,s,"id",e,1),h(i.typeParameters,s,"typeParameters",t,1),h(i.supertype,s,"supertype",r,1),h(i.impltype,s,"impltype",n,1),s}function SM(e,t){let r={type:"QualifiedTypeIdentifier",id:e,qualification:t},n=k.QualifiedTypeIdentifier;return h(n.id,r,"id",e,1),h(n.qualification,r,"qualification",t,1),r}function xM(e){let t={type:"StringLiteralTypeAnnotation",value:e},r=k.StringLiteralTypeAnnotation;return h(r.value,t,"value",e),t}function vM(){return{type:"StringTypeAnnotation"}}function PM(){return{type:"SymbolTypeAnnotation"}}function AM(){return{type:"ThisTypeAnnotation"}}function CM(e){let t={type:"TupleTypeAnnotation",types:e},r=k.TupleTypeAnnotation;return h(r.types,t,"types",e,1),t}function DM(e){let t={type:"TypeofTypeAnnotation",argument:e},r=k.TypeofTypeAnnotation;return h(r.argument,t,"argument",e,1),t}function wM(e,t=null,r){let n={type:"TypeAlias",id:e,typeParameters:t,right:r},s=k.TypeAlias;return h(s.id,n,"id",e,1),h(s.typeParameters,n,"typeParameters",t,1),h(s.right,n,"right",r,1),n}function IM(e){let t={type:"TypeAnnotation",typeAnnotation:e},r=k.TypeAnnotation;return h(r.typeAnnotation,t,"typeAnnotation",e,1),t}function _M(e,t){let r={type:"TypeCastExpression",expression:e,typeAnnotation:t},n=k.TypeCastExpression;return h(n.expression,r,"expression",e,1),h(n.typeAnnotation,r,"typeAnnotation",t,1),r}function OM(e=null,t=null,r=null){let n={type:"TypeParameter",bound:e,default:t,variance:r,name:null},s=k.TypeParameter;return h(s.bound,n,"bound",e,1),h(s.default,n,"default",t,1),h(s.variance,n,"variance",r,1),n}function NM(e){let t={type:"TypeParameterDeclaration",params:e},r=k.TypeParameterDeclaration;return h(r.params,t,"params",e,1),t}function BM(e){let t={type:"TypeParameterInstantiation",params:e},r=k.TypeParameterInstantiation;return h(r.params,t,"params",e,1),t}function kM(e){let t={type:"UnionTypeAnnotation",types:e},r=k.UnionTypeAnnotation;return h(r.types,t,"types",e,1),t}function FM(e){let t={type:"Variance",kind:e},r=k.Variance;return h(r.kind,t,"kind",e),t}function LM(){return{type:"VoidTypeAnnotation"}}function jM(e,t){let r={type:"EnumDeclaration",id:e,body:t},n=k.EnumDeclaration;return h(n.id,r,"id",e,1),h(n.body,r,"body",t,1),r}function MM(e){let t={type:"EnumBooleanBody",members:e,explicitType:null,hasUnknownMembers:null},r=k.EnumBooleanBody;return h(r.members,t,"members",e,1),t}function RM(e){let t={type:"EnumNumberBody",members:e,explicitType:null,hasUnknownMembers:null},r=k.EnumNumberBody;return h(r.members,t,"members",e,1),t}function qM(e){let t={type:"EnumStringBody",members:e,explicitType:null,hasUnknownMembers:null},r=k.EnumStringBody;return h(r.members,t,"members",e,1),t}function UM(e){let t={type:"EnumSymbolBody",members:e,hasUnknownMembers:null},r=k.EnumSymbolBody;return h(r.members,t,"members",e,1),t}function VM(e){let t={type:"EnumBooleanMember",id:e,init:null},r=k.EnumBooleanMember;return h(r.id,t,"id",e,1),t}function $M(e,t){let r={type:"EnumNumberMember",id:e,init:t},n=k.EnumNumberMember;return h(n.id,r,"id",e,1),h(n.init,r,"init",t,1),r}function WM(e,t){let r={type:"EnumStringMember",id:e,init:t},n=k.EnumStringMember;return h(n.id,r,"id",e,1),h(n.init,r,"init",t,1),r}function KM(e){let t={type:"EnumDefaultedMember",id:e},r=k.EnumDefaultedMember;return h(r.id,t,"id",e,1),t}function GM(e,t){let r={type:"IndexedAccessType",objectType:e,indexType:t},n=k.IndexedAccessType;return h(n.objectType,r,"objectType",e,1),h(n.indexType,r,"indexType",t,1),r}function HM(e,t){let r={type:"OptionalIndexedAccessType",objectType:e,indexType:t,optional:null},n=k.OptionalIndexedAccessType;return h(n.objectType,r,"objectType",e,1),h(n.indexType,r,"indexType",t,1),r}function YM(e,t=null){let r={type:"JSXAttribute",name:e,value:t},n=k.JSXAttribute;return h(n.name,r,"name",e,1),h(n.value,r,"value",t,1),r}function XM(e){let t={type:"JSXClosingElement",name:e},r=k.JSXClosingElement;return h(r.name,t,"name",e,1),t}function JM(e,t=null,r,n=null){let s={type:"JSXElement",openingElement:e,closingElement:t,children:r,selfClosing:n},i=k.JSXElement;return h(i.openingElement,s,"openingElement",e,1),h(i.closingElement,s,"closingElement",t,1),h(i.children,s,"children",r,1),h(i.selfClosing,s,"selfClosing",n),s}function zM(){return{type:"JSXEmptyExpression"}}function QM(e){let t={type:"JSXExpressionContainer",expression:e},r=k.JSXExpressionContainer;return h(r.expression,t,"expression",e,1),t}function ZM(e){let t={type:"JSXSpreadChild",expression:e},r=k.JSXSpreadChild;return h(r.expression,t,"expression",e,1),t}function eR(e){let t={type:"JSXIdentifier",name:e},r=k.JSXIdentifier;return h(r.name,t,"name",e),t}function tR(e,t){let r={type:"JSXMemberExpression",object:e,property:t},n=k.JSXMemberExpression;return h(n.object,r,"object",e,1),h(n.property,r,"property",t,1),r}function rR(e,t){let r={type:"JSXNamespacedName",namespace:e,name:t},n=k.JSXNamespacedName;return h(n.namespace,r,"namespace",e,1),h(n.name,r,"name",t,1),r}function nR(e,t,r=!1){let n={type:"JSXOpeningElement",name:e,attributes:t,selfClosing:r},s=k.JSXOpeningElement;return h(s.name,n,"name",e,1),h(s.attributes,n,"attributes",t,1),h(s.selfClosing,n,"selfClosing",r),n}function sR(e){let t={type:"JSXSpreadAttribute",argument:e},r=k.JSXSpreadAttribute;return h(r.argument,t,"argument",e,1),t}function iR(e){let t={type:"JSXText",value:e},r=k.JSXText;return h(r.value,t,"value",e),t}function aR(e,t,r){let n={type:"JSXFragment",openingFragment:e,closingFragment:t,children:r},s=k.JSXFragment;return h(s.openingFragment,n,"openingFragment",e,1),h(s.closingFragment,n,"closingFragment",t,1),h(s.children,n,"children",r,1),n}function oR(){return{type:"JSXOpeningFragment"}}function lR(){return{type:"JSXClosingFragment"}}function uR(){return{type:"Noop"}}function cR(e,t){let r={type:"Placeholder",expectedNode:e,name:t},n=k.Placeholder;return h(n.expectedNode,r,"expectedNode",e),h(n.name,r,"name",t,1),r}function pR(e){let t={type:"V8IntrinsicIdentifier",name:e},r=k.V8IntrinsicIdentifier;return h(r.name,t,"name",e),t}function fR(){return{type:"ArgumentPlaceholder"}}function dR(e,t){let r={type:"BindExpression",object:e,callee:t},n=k.BindExpression;return h(n.object,r,"object",e,1),h(n.callee,r,"callee",t,1),r}function hR(e,t){let r={type:"ImportAttribute",key:e,value:t},n=k.ImportAttribute;return h(n.key,r,"key",e,1),h(n.value,r,"value",t,1),r}function mR(e){let t={type:"Decorator",expression:e},r=k.Decorator;return h(r.expression,t,"expression",e,1),t}function yR(e,t=!1){let r={type:"DoExpression",body:e,async:t},n=k.DoExpression;return h(n.body,r,"body",e,1),h(n.async,r,"async",t),r}function gR(e){let t={type:"ExportDefaultSpecifier",exported:e},r=k.ExportDefaultSpecifier;return h(r.exported,t,"exported",e,1),t}function bR(e){let t={type:"RecordExpression",properties:e},r=k.RecordExpression;return h(r.properties,t,"properties",e,1),t}function ER(e=[]){let t={type:"TupleExpression",elements:e},r=k.TupleExpression;return h(r.elements,t,"elements",e,1),t}function TR(e){let t={type:"DecimalLiteral",value:e},r=k.DecimalLiteral;return h(r.value,t,"value",e),t}function SR(e){let t={type:"ModuleExpression",body:e},r=k.ModuleExpression;return h(r.body,t,"body",e,1),t}function xR(){return{type:"TopicReference"}}function vR(e){let t={type:"PipelineTopicExpression",expression:e},r=k.PipelineTopicExpression;return h(r.expression,t,"expression",e,1),t}function PR(e){let t={type:"PipelineBareFunction",callee:e},r=k.PipelineBareFunction;return h(r.callee,t,"callee",e,1),t}function AR(){return{type:"PipelinePrimaryTopicReference"}}function CR(e){let t={type:"TSParameterProperty",parameter:e},r=k.TSParameterProperty;return h(r.parameter,t,"parameter",e,1),t}function DR(e=null,t=null,r,n=null){let s={type:"TSDeclareFunction",id:e,typeParameters:t,params:r,returnType:n},i=k.TSDeclareFunction;return h(i.id,s,"id",e,1),h(i.typeParameters,s,"typeParameters",t,1),h(i.params,s,"params",r,1),h(i.returnType,s,"returnType",n,1),s}function wR(e=null,t,r=null,n,s=null){let i={type:"TSDeclareMethod",decorators:e,key:t,typeParameters:r,params:n,returnType:s},a=k.TSDeclareMethod;return h(a.decorators,i,"decorators",e,1),h(a.key,i,"key",t,1),h(a.typeParameters,i,"typeParameters",r,1),h(a.params,i,"params",n,1),h(a.returnType,i,"returnType",s,1),i}function IR(e,t){let r={type:"TSQualifiedName",left:e,right:t},n=k.TSQualifiedName;return h(n.left,r,"left",e,1),h(n.right,r,"right",t,1),r}function _R(e=null,t,r=null){let n={type:"TSCallSignatureDeclaration",typeParameters:e,parameters:t,typeAnnotation:r},s=k.TSCallSignatureDeclaration;return h(s.typeParameters,n,"typeParameters",e,1),h(s.parameters,n,"parameters",t,1),h(s.typeAnnotation,n,"typeAnnotation",r,1),n}function OR(e=null,t,r=null){let n={type:"TSConstructSignatureDeclaration",typeParameters:e,parameters:t,typeAnnotation:r},s=k.TSConstructSignatureDeclaration;return h(s.typeParameters,n,"typeParameters",e,1),h(s.parameters,n,"parameters",t,1),h(s.typeAnnotation,n,"typeAnnotation",r,1),n}function NR(e,t=null){let r={type:"TSPropertySignature",key:e,typeAnnotation:t},n=k.TSPropertySignature;return h(n.key,r,"key",e,1),h(n.typeAnnotation,r,"typeAnnotation",t,1),r}function BR(e,t=null,r,n=null){let s={type:"TSMethodSignature",key:e,typeParameters:t,parameters:r,typeAnnotation:n,kind:null},i=k.TSMethodSignature;return h(i.key,s,"key",e,1),h(i.typeParameters,s,"typeParameters",t,1),h(i.parameters,s,"parameters",r,1),h(i.typeAnnotation,s,"typeAnnotation",n,1),s}function kR(e,t=null){let r={type:"TSIndexSignature",parameters:e,typeAnnotation:t},n=k.TSIndexSignature;return h(n.parameters,r,"parameters",e,1),h(n.typeAnnotation,r,"typeAnnotation",t,1),r}function FR(){return{type:"TSAnyKeyword"}}function LR(){return{type:"TSBooleanKeyword"}}function jR(){return{type:"TSBigIntKeyword"}}function MR(){return{type:"TSIntrinsicKeyword"}}function RR(){return{type:"TSNeverKeyword"}}function qR(){return{type:"TSNullKeyword"}}function UR(){return{type:"TSNumberKeyword"}}function VR(){return{type:"TSObjectKeyword"}}function $R(){return{type:"TSStringKeyword"}}function WR(){return{type:"TSSymbolKeyword"}}function KR(){return{type:"TSUndefinedKeyword"}}function GR(){return{type:"TSUnknownKeyword"}}function HR(){return{type:"TSVoidKeyword"}}function YR(){return{type:"TSThisType"}}function XR(e=null,t,r=null){let n={type:"TSFunctionType",typeParameters:e,parameters:t,typeAnnotation:r},s=k.TSFunctionType;return h(s.typeParameters,n,"typeParameters",e,1),h(s.parameters,n,"parameters",t,1),h(s.typeAnnotation,n,"typeAnnotation",r,1),n}function JR(e=null,t,r=null){let n={type:"TSConstructorType",typeParameters:e,parameters:t,typeAnnotation:r},s=k.TSConstructorType;return h(s.typeParameters,n,"typeParameters",e,1),h(s.parameters,n,"parameters",t,1),h(s.typeAnnotation,n,"typeAnnotation",r,1),n}function zR(e,t=null){let r={type:"TSTypeReference",typeName:e,typeParameters:t},n=k.TSTypeReference;return h(n.typeName,r,"typeName",e,1),h(n.typeParameters,r,"typeParameters",t,1),r}function QR(e,t=null,r=null){let n={type:"TSTypePredicate",parameterName:e,typeAnnotation:t,asserts:r},s=k.TSTypePredicate;return h(s.parameterName,n,"parameterName",e,1),h(s.typeAnnotation,n,"typeAnnotation",t,1),h(s.asserts,n,"asserts",r),n}function ZR(e,t=null){let r={type:"TSTypeQuery",exprName:e,typeParameters:t},n=k.TSTypeQuery;return h(n.exprName,r,"exprName",e,1),h(n.typeParameters,r,"typeParameters",t,1),r}function e5(e){let t={type:"TSTypeLiteral",members:e},r=k.TSTypeLiteral;return h(r.members,t,"members",e,1),t}function t5(e){let t={type:"TSArrayType",elementType:e},r=k.TSArrayType;return h(r.elementType,t,"elementType",e,1),t}function r5(e){let t={type:"TSTupleType",elementTypes:e},r=k.TSTupleType;return h(r.elementTypes,t,"elementTypes",e,1),t}function n5(e){let t={type:"TSOptionalType",typeAnnotation:e},r=k.TSOptionalType;return h(r.typeAnnotation,t,"typeAnnotation",e,1),t}function s5(e){let t={type:"TSRestType",typeAnnotation:e},r=k.TSRestType;return h(r.typeAnnotation,t,"typeAnnotation",e,1),t}function i5(e,t,r=!1){let n={type:"TSNamedTupleMember",label:e,elementType:t,optional:r},s=k.TSNamedTupleMember;return h(s.label,n,"label",e,1),h(s.elementType,n,"elementType",t,1),h(s.optional,n,"optional",r),n}function a5(e){let t={type:"TSUnionType",types:e},r=k.TSUnionType;return h(r.types,t,"types",e,1),t}function o5(e){let t={type:"TSIntersectionType",types:e},r=k.TSIntersectionType;return h(r.types,t,"types",e,1),t}function l5(e,t,r,n){let s={type:"TSConditionalType",checkType:e,extendsType:t,trueType:r,falseType:n},i=k.TSConditionalType;return h(i.checkType,s,"checkType",e,1),h(i.extendsType,s,"extendsType",t,1),h(i.trueType,s,"trueType",r,1),h(i.falseType,s,"falseType",n,1),s}function u5(e){let t={type:"TSInferType",typeParameter:e},r=k.TSInferType;return h(r.typeParameter,t,"typeParameter",e,1),t}function c5(e){let t={type:"TSParenthesizedType",typeAnnotation:e},r=k.TSParenthesizedType;return h(r.typeAnnotation,t,"typeAnnotation",e,1),t}function p5(e){let t={type:"TSTypeOperator",typeAnnotation:e,operator:null},r=k.TSTypeOperator;return h(r.typeAnnotation,t,"typeAnnotation",e,1),t}function f5(e,t){let r={type:"TSIndexedAccessType",objectType:e,indexType:t},n=k.TSIndexedAccessType;return h(n.objectType,r,"objectType",e,1),h(n.indexType,r,"indexType",t,1),r}function d5(e,t=null,r=null){let n={type:"TSMappedType",typeParameter:e,typeAnnotation:t,nameType:r},s=k.TSMappedType;return h(s.typeParameter,n,"typeParameter",e,1),h(s.typeAnnotation,n,"typeAnnotation",t,1),h(s.nameType,n,"nameType",r,1),n}function h5(e,t){let r={type:"TSTemplateLiteralType",quasis:e,types:t},n=k.TSTemplateLiteralType;return h(n.quasis,r,"quasis",e,1),h(n.types,r,"types",t,1),r}function m5(e){let t={type:"TSLiteralType",literal:e},r=k.TSLiteralType;return h(r.literal,t,"literal",e,1),t}function y5(e,t=null){let r={type:"TSExpressionWithTypeArguments",expression:e,typeParameters:t},n=k.TSExpressionWithTypeArguments;return h(n.expression,r,"expression",e,1),h(n.typeParameters,r,"typeParameters",t,1),r}function g5(e,t=null,r=null,n){let s={type:"TSInterfaceDeclaration",id:e,typeParameters:t,extends:r,body:n},i=k.TSInterfaceDeclaration;return h(i.id,s,"id",e,1),h(i.typeParameters,s,"typeParameters",t,1),h(i.extends,s,"extends",r,1),h(i.body,s,"body",n,1),s}function b5(e){let t={type:"TSInterfaceBody",body:e},r=k.TSInterfaceBody;return h(r.body,t,"body",e,1),t}function E5(e,t=null,r){let n={type:"TSTypeAliasDeclaration",id:e,typeParameters:t,typeAnnotation:r},s=k.TSTypeAliasDeclaration;return h(s.id,n,"id",e,1),h(s.typeParameters,n,"typeParameters",t,1),h(s.typeAnnotation,n,"typeAnnotation",r,1),n}function T5(e,t=null){let r={type:"TSInstantiationExpression",expression:e,typeParameters:t},n=k.TSInstantiationExpression;return h(n.expression,r,"expression",e,1),h(n.typeParameters,r,"typeParameters",t,1),r}function S5(e,t){let r={type:"TSAsExpression",expression:e,typeAnnotation:t},n=k.TSAsExpression;return h(n.expression,r,"expression",e,1),h(n.typeAnnotation,r,"typeAnnotation",t,1),r}function x5(e,t){let r={type:"TSSatisfiesExpression",expression:e,typeAnnotation:t},n=k.TSSatisfiesExpression;return h(n.expression,r,"expression",e,1),h(n.typeAnnotation,r,"typeAnnotation",t,1),r}function v5(e,t){let r={type:"TSTypeAssertion",typeAnnotation:e,expression:t},n=k.TSTypeAssertion;return h(n.typeAnnotation,r,"typeAnnotation",e,1),h(n.expression,r,"expression",t,1),r}function P5(e){let t={type:"TSEnumBody",members:e},r=k.TSEnumBody;return h(r.members,t,"members",e,1),t}function A5(e,t){let r={type:"TSEnumDeclaration",id:e,members:t},n=k.TSEnumDeclaration;return h(n.id,r,"id",e,1),h(n.members,r,"members",t,1),r}function C5(e,t=null){let r={type:"TSEnumMember",id:e,initializer:t},n=k.TSEnumMember;return h(n.id,r,"id",e,1),h(n.initializer,r,"initializer",t,1),r}function D5(e,t){let r={type:"TSModuleDeclaration",id:e,body:t,kind:null},n=k.TSModuleDeclaration;return h(n.id,r,"id",e,1),h(n.body,r,"body",t,1),r}function w5(e){let t={type:"TSModuleBlock",body:e},r=k.TSModuleBlock;return h(r.body,t,"body",e,1),t}function I5(e,t=null,r=null){let n={type:"TSImportType",argument:e,qualifier:t,typeParameters:r},s=k.TSImportType;return h(s.argument,n,"argument",e,1),h(s.qualifier,n,"qualifier",t,1),h(s.typeParameters,n,"typeParameters",r,1),n}function _5(e,t){let r={type:"TSImportEqualsDeclaration",id:e,moduleReference:t,isExport:null},n=k.TSImportEqualsDeclaration;return h(n.id,r,"id",e,1),h(n.moduleReference,r,"moduleReference",t,1),r}function O5(e){let t={type:"TSExternalModuleReference",expression:e},r=k.TSExternalModuleReference;return h(r.expression,t,"expression",e,1),t}function N5(e){let t={type:"TSNonNullExpression",expression:e},r=k.TSNonNullExpression;return h(r.expression,t,"expression",e,1),t}function B5(e){let t={type:"TSExportAssignment",expression:e},r=k.TSExportAssignment;return h(r.expression,t,"expression",e,1),t}function k5(e){let t={type:"TSNamespaceExportDeclaration",id:e},r=k.TSNamespaceExportDeclaration;return h(r.id,t,"id",e,1),t}function F5(e){let t={type:"TSTypeAnnotation",typeAnnotation:e},r=k.TSTypeAnnotation;return h(r.typeAnnotation,t,"typeAnnotation",e,1),t}function L5(e){let t={type:"TSTypeParameterInstantiation",params:e},r=k.TSTypeParameterInstantiation;return h(r.params,t,"params",e,1),t}function j5(e){let t={type:"TSTypeParameterDeclaration",params:e},r=k.TSTypeParameterDeclaration;return h(r.params,t,"params",e,1),t}function M5(e=null,t=null,r){let n={type:"TSTypeParameter",constraint:e,default:t,name:r},s=k.TSTypeParameter;return h(s.constraint,n,"constraint",e,1),h(s.default,n,"default",t,1),h(s.name,n,"name",r),n}function R5(e){return(0,Hl.default)("NumberLiteral","NumericLiteral","The node type "),CT(e)}function q5(e,t=""){return(0,Hl.default)("RegexLiteral","RegExpLiteral","The node type "),DT(e,t)}function U5(e){return(0,Hl.default)("RestProperty","RestElement","The node type "),wT(e)}function V5(e){return(0,Hl.default)("SpreadProperty","SpreadElement","The node type "),IT(e)}});var _T=T(w=>{"use strict";Object.defineProperty(w,"__esModule",{value:!0});Object.defineProperty(w,"AnyTypeAnnotation",{enumerable:!0,get:function(){return I.anyTypeAnnotation}});Object.defineProperty(w,"ArgumentPlaceholder",{enumerable:!0,get:function(){return I.argumentPlaceholder}});Object.defineProperty(w,"ArrayExpression",{enumerable:!0,get:function(){return I.arrayExpression}});Object.defineProperty(w,"ArrayPattern",{enumerable:!0,get:function(){return I.arrayPattern}});Object.defineProperty(w,"ArrayTypeAnnotation",{enumerable:!0,get:function(){return I.arrayTypeAnnotation}});Object.defineProperty(w,"ArrowFunctionExpression",{enumerable:!0,get:function(){return I.arrowFunctionExpression}});Object.defineProperty(w,"AssignmentExpression",{enumerable:!0,get:function(){return I.assignmentExpression}});Object.defineProperty(w,"AssignmentPattern",{enumerable:!0,get:function(){return I.assignmentPattern}});Object.defineProperty(w,"AwaitExpression",{enumerable:!0,get:function(){return I.awaitExpression}});Object.defineProperty(w,"BigIntLiteral",{enumerable:!0,get:function(){return I.bigIntLiteral}});Object.defineProperty(w,"BinaryExpression",{enumerable:!0,get:function(){return I.binaryExpression}});Object.defineProperty(w,"BindExpression",{enumerable:!0,get:function(){return I.bindExpression}});Object.defineProperty(w,"BlockStatement",{enumerable:!0,get:function(){return I.blockStatement}});Object.defineProperty(w,"BooleanLiteral",{enumerable:!0,get:function(){return I.booleanLiteral}});Object.defineProperty(w,"BooleanLiteralTypeAnnotation",{enumerable:!0,get:function(){return I.booleanLiteralTypeAnnotation}});Object.defineProperty(w,"BooleanTypeAnnotation",{enumerable:!0,get:function(){return I.booleanTypeAnnotation}});Object.defineProperty(w,"BreakStatement",{enumerable:!0,get:function(){return I.breakStatement}});Object.defineProperty(w,"CallExpression",{enumerable:!0,get:function(){return I.callExpression}});Object.defineProperty(w,"CatchClause",{enumerable:!0,get:function(){return I.catchClause}});Object.defineProperty(w,"ClassAccessorProperty",{enumerable:!0,get:function(){return I.classAccessorProperty}});Object.defineProperty(w,"ClassBody",{enumerable:!0,get:function(){return I.classBody}});Object.defineProperty(w,"ClassDeclaration",{enumerable:!0,get:function(){return I.classDeclaration}});Object.defineProperty(w,"ClassExpression",{enumerable:!0,get:function(){return I.classExpression}});Object.defineProperty(w,"ClassImplements",{enumerable:!0,get:function(){return I.classImplements}});Object.defineProperty(w,"ClassMethod",{enumerable:!0,get:function(){return I.classMethod}});Object.defineProperty(w,"ClassPrivateMethod",{enumerable:!0,get:function(){return I.classPrivateMethod}});Object.defineProperty(w,"ClassPrivateProperty",{enumerable:!0,get:function(){return I.classPrivateProperty}});Object.defineProperty(w,"ClassProperty",{enumerable:!0,get:function(){return I.classProperty}});Object.defineProperty(w,"ConditionalExpression",{enumerable:!0,get:function(){return I.conditionalExpression}});Object.defineProperty(w,"ContinueStatement",{enumerable:!0,get:function(){return I.continueStatement}});Object.defineProperty(w,"DebuggerStatement",{enumerable:!0,get:function(){return I.debuggerStatement}});Object.defineProperty(w,"DecimalLiteral",{enumerable:!0,get:function(){return I.decimalLiteral}});Object.defineProperty(w,"DeclareClass",{enumerable:!0,get:function(){return I.declareClass}});Object.defineProperty(w,"DeclareExportAllDeclaration",{enumerable:!0,get:function(){return I.declareExportAllDeclaration}});Object.defineProperty(w,"DeclareExportDeclaration",{enumerable:!0,get:function(){return I.declareExportDeclaration}});Object.defineProperty(w,"DeclareFunction",{enumerable:!0,get:function(){return I.declareFunction}});Object.defineProperty(w,"DeclareInterface",{enumerable:!0,get:function(){return I.declareInterface}});Object.defineProperty(w,"DeclareModule",{enumerable:!0,get:function(){return I.declareModule}});Object.defineProperty(w,"DeclareModuleExports",{enumerable:!0,get:function(){return I.declareModuleExports}});Object.defineProperty(w,"DeclareOpaqueType",{enumerable:!0,get:function(){return I.declareOpaqueType}});Object.defineProperty(w,"DeclareTypeAlias",{enumerable:!0,get:function(){return I.declareTypeAlias}});Object.defineProperty(w,"DeclareVariable",{enumerable:!0,get:function(){return I.declareVariable}});Object.defineProperty(w,"DeclaredPredicate",{enumerable:!0,get:function(){return I.declaredPredicate}});Object.defineProperty(w,"Decorator",{enumerable:!0,get:function(){return I.decorator}});Object.defineProperty(w,"Directive",{enumerable:!0,get:function(){return I.directive}});Object.defineProperty(w,"DirectiveLiteral",{enumerable:!0,get:function(){return I.directiveLiteral}});Object.defineProperty(w,"DoExpression",{enumerable:!0,get:function(){return I.doExpression}});Object.defineProperty(w,"DoWhileStatement",{enumerable:!0,get:function(){return I.doWhileStatement}});Object.defineProperty(w,"EmptyStatement",{enumerable:!0,get:function(){return I.emptyStatement}});Object.defineProperty(w,"EmptyTypeAnnotation",{enumerable:!0,get:function(){return I.emptyTypeAnnotation}});Object.defineProperty(w,"EnumBooleanBody",{enumerable:!0,get:function(){return I.enumBooleanBody}});Object.defineProperty(w,"EnumBooleanMember",{enumerable:!0,get:function(){return I.enumBooleanMember}});Object.defineProperty(w,"EnumDeclaration",{enumerable:!0,get:function(){return I.enumDeclaration}});Object.defineProperty(w,"EnumDefaultedMember",{enumerable:!0,get:function(){return I.enumDefaultedMember}});Object.defineProperty(w,"EnumNumberBody",{enumerable:!0,get:function(){return I.enumNumberBody}});Object.defineProperty(w,"EnumNumberMember",{enumerable:!0,get:function(){return I.enumNumberMember}});Object.defineProperty(w,"EnumStringBody",{enumerable:!0,get:function(){return I.enumStringBody}});Object.defineProperty(w,"EnumStringMember",{enumerable:!0,get:function(){return I.enumStringMember}});Object.defineProperty(w,"EnumSymbolBody",{enumerable:!0,get:function(){return I.enumSymbolBody}});Object.defineProperty(w,"ExistsTypeAnnotation",{enumerable:!0,get:function(){return I.existsTypeAnnotation}});Object.defineProperty(w,"ExportAllDeclaration",{enumerable:!0,get:function(){return I.exportAllDeclaration}});Object.defineProperty(w,"ExportDefaultDeclaration",{enumerable:!0,get:function(){return I.exportDefaultDeclaration}});Object.defineProperty(w,"ExportDefaultSpecifier",{enumerable:!0,get:function(){return I.exportDefaultSpecifier}});Object.defineProperty(w,"ExportNamedDeclaration",{enumerable:!0,get:function(){return I.exportNamedDeclaration}});Object.defineProperty(w,"ExportNamespaceSpecifier",{enumerable:!0,get:function(){return I.exportNamespaceSpecifier}});Object.defineProperty(w,"ExportSpecifier",{enumerable:!0,get:function(){return I.exportSpecifier}});Object.defineProperty(w,"ExpressionStatement",{enumerable:!0,get:function(){return I.expressionStatement}});Object.defineProperty(w,"File",{enumerable:!0,get:function(){return I.file}});Object.defineProperty(w,"ForInStatement",{enumerable:!0,get:function(){return I.forInStatement}});Object.defineProperty(w,"ForOfStatement",{enumerable:!0,get:function(){return I.forOfStatement}});Object.defineProperty(w,"ForStatement",{enumerable:!0,get:function(){return I.forStatement}});Object.defineProperty(w,"FunctionDeclaration",{enumerable:!0,get:function(){return I.functionDeclaration}});Object.defineProperty(w,"FunctionExpression",{enumerable:!0,get:function(){return I.functionExpression}});Object.defineProperty(w,"FunctionTypeAnnotation",{enumerable:!0,get:function(){return I.functionTypeAnnotation}});Object.defineProperty(w,"FunctionTypeParam",{enumerable:!0,get:function(){return I.functionTypeParam}});Object.defineProperty(w,"GenericTypeAnnotation",{enumerable:!0,get:function(){return I.genericTypeAnnotation}});Object.defineProperty(w,"Identifier",{enumerable:!0,get:function(){return I.identifier}});Object.defineProperty(w,"IfStatement",{enumerable:!0,get:function(){return I.ifStatement}});Object.defineProperty(w,"Import",{enumerable:!0,get:function(){return I.import}});Object.defineProperty(w,"ImportAttribute",{enumerable:!0,get:function(){return I.importAttribute}});Object.defineProperty(w,"ImportDeclaration",{enumerable:!0,get:function(){return I.importDeclaration}});Object.defineProperty(w,"ImportDefaultSpecifier",{enumerable:!0,get:function(){return I.importDefaultSpecifier}});Object.defineProperty(w,"ImportExpression",{enumerable:!0,get:function(){return I.importExpression}});Object.defineProperty(w,"ImportNamespaceSpecifier",{enumerable:!0,get:function(){return I.importNamespaceSpecifier}});Object.defineProperty(w,"ImportSpecifier",{enumerable:!0,get:function(){return I.importSpecifier}});Object.defineProperty(w,"IndexedAccessType",{enumerable:!0,get:function(){return I.indexedAccessType}});Object.defineProperty(w,"InferredPredicate",{enumerable:!0,get:function(){return I.inferredPredicate}});Object.defineProperty(w,"InterfaceDeclaration",{enumerable:!0,get:function(){return I.interfaceDeclaration}});Object.defineProperty(w,"InterfaceExtends",{enumerable:!0,get:function(){return I.interfaceExtends}});Object.defineProperty(w,"InterfaceTypeAnnotation",{enumerable:!0,get:function(){return I.interfaceTypeAnnotation}});Object.defineProperty(w,"InterpreterDirective",{enumerable:!0,get:function(){return I.interpreterDirective}});Object.defineProperty(w,"IntersectionTypeAnnotation",{enumerable:!0,get:function(){return I.intersectionTypeAnnotation}});Object.defineProperty(w,"JSXAttribute",{enumerable:!0,get:function(){return I.jsxAttribute}});Object.defineProperty(w,"JSXClosingElement",{enumerable:!0,get:function(){return I.jsxClosingElement}});Object.defineProperty(w,"JSXClosingFragment",{enumerable:!0,get:function(){return I.jsxClosingFragment}});Object.defineProperty(w,"JSXElement",{enumerable:!0,get:function(){return I.jsxElement}});Object.defineProperty(w,"JSXEmptyExpression",{enumerable:!0,get:function(){return I.jsxEmptyExpression}});Object.defineProperty(w,"JSXExpressionContainer",{enumerable:!0,get:function(){return I.jsxExpressionContainer}});Object.defineProperty(w,"JSXFragment",{enumerable:!0,get:function(){return I.jsxFragment}});Object.defineProperty(w,"JSXIdentifier",{enumerable:!0,get:function(){return I.jsxIdentifier}});Object.defineProperty(w,"JSXMemberExpression",{enumerable:!0,get:function(){return I.jsxMemberExpression}});Object.defineProperty(w,"JSXNamespacedName",{enumerable:!0,get:function(){return I.jsxNamespacedName}});Object.defineProperty(w,"JSXOpeningElement",{enumerable:!0,get:function(){return I.jsxOpeningElement}});Object.defineProperty(w,"JSXOpeningFragment",{enumerable:!0,get:function(){return I.jsxOpeningFragment}});Object.defineProperty(w,"JSXSpreadAttribute",{enumerable:!0,get:function(){return I.jsxSpreadAttribute}});Object.defineProperty(w,"JSXSpreadChild",{enumerable:!0,get:function(){return I.jsxSpreadChild}});Object.defineProperty(w,"JSXText",{enumerable:!0,get:function(){return I.jsxText}});Object.defineProperty(w,"LabeledStatement",{enumerable:!0,get:function(){return I.labeledStatement}});Object.defineProperty(w,"LogicalExpression",{enumerable:!0,get:function(){return I.logicalExpression}});Object.defineProperty(w,"MemberExpression",{enumerable:!0,get:function(){return I.memberExpression}});Object.defineProperty(w,"MetaProperty",{enumerable:!0,get:function(){return I.metaProperty}});Object.defineProperty(w,"MixedTypeAnnotation",{enumerable:!0,get:function(){return I.mixedTypeAnnotation}});Object.defineProperty(w,"ModuleExpression",{enumerable:!0,get:function(){return I.moduleExpression}});Object.defineProperty(w,"NewExpression",{enumerable:!0,get:function(){return I.newExpression}});Object.defineProperty(w,"Noop",{enumerable:!0,get:function(){return I.noop}});Object.defineProperty(w,"NullLiteral",{enumerable:!0,get:function(){return I.nullLiteral}});Object.defineProperty(w,"NullLiteralTypeAnnotation",{enumerable:!0,get:function(){return I.nullLiteralTypeAnnotation}});Object.defineProperty(w,"NullableTypeAnnotation",{enumerable:!0,get:function(){return I.nullableTypeAnnotation}});Object.defineProperty(w,"NumberLiteral",{enumerable:!0,get:function(){return I.numberLiteral}});Object.defineProperty(w,"NumberLiteralTypeAnnotation",{enumerable:!0,get:function(){return I.numberLiteralTypeAnnotation}});Object.defineProperty(w,"NumberTypeAnnotation",{enumerable:!0,get:function(){return I.numberTypeAnnotation}});Object.defineProperty(w,"NumericLiteral",{enumerable:!0,get:function(){return I.numericLiteral}});Object.defineProperty(w,"ObjectExpression",{enumerable:!0,get:function(){return I.objectExpression}});Object.defineProperty(w,"ObjectMethod",{enumerable:!0,get:function(){return I.objectMethod}});Object.defineProperty(w,"ObjectPattern",{enumerable:!0,get:function(){return I.objectPattern}});Object.defineProperty(w,"ObjectProperty",{enumerable:!0,get:function(){return I.objectProperty}});Object.defineProperty(w,"ObjectTypeAnnotation",{enumerable:!0,get:function(){return I.objectTypeAnnotation}});Object.defineProperty(w,"ObjectTypeCallProperty",{enumerable:!0,get:function(){return I.objectTypeCallProperty}});Object.defineProperty(w,"ObjectTypeIndexer",{enumerable:!0,get:function(){return I.objectTypeIndexer}});Object.defineProperty(w,"ObjectTypeInternalSlot",{enumerable:!0,get:function(){return I.objectTypeInternalSlot}});Object.defineProperty(w,"ObjectTypeProperty",{enumerable:!0,get:function(){return I.objectTypeProperty}});Object.defineProperty(w,"ObjectTypeSpreadProperty",{enumerable:!0,get:function(){return I.objectTypeSpreadProperty}});Object.defineProperty(w,"OpaqueType",{enumerable:!0,get:function(){return I.opaqueType}});Object.defineProperty(w,"OptionalCallExpression",{enumerable:!0,get:function(){return I.optionalCallExpression}});Object.defineProperty(w,"OptionalIndexedAccessType",{enumerable:!0,get:function(){return I.optionalIndexedAccessType}});Object.defineProperty(w,"OptionalMemberExpression",{enumerable:!0,get:function(){return I.optionalMemberExpression}});Object.defineProperty(w,"ParenthesizedExpression",{enumerable:!0,get:function(){return I.parenthesizedExpression}});Object.defineProperty(w,"PipelineBareFunction",{enumerable:!0,get:function(){return I.pipelineBareFunction}});Object.defineProperty(w,"PipelinePrimaryTopicReference",{enumerable:!0,get:function(){return I.pipelinePrimaryTopicReference}});Object.defineProperty(w,"PipelineTopicExpression",{enumerable:!0,get:function(){return I.pipelineTopicExpression}});Object.defineProperty(w,"Placeholder",{enumerable:!0,get:function(){return I.placeholder}});Object.defineProperty(w,"PrivateName",{enumerable:!0,get:function(){return I.privateName}});Object.defineProperty(w,"Program",{enumerable:!0,get:function(){return I.program}});Object.defineProperty(w,"QualifiedTypeIdentifier",{enumerable:!0,get:function(){return I.qualifiedTypeIdentifier}});Object.defineProperty(w,"RecordExpression",{enumerable:!0,get:function(){return I.recordExpression}});Object.defineProperty(w,"RegExpLiteral",{enumerable:!0,get:function(){return I.regExpLiteral}});Object.defineProperty(w,"RegexLiteral",{enumerable:!0,get:function(){return I.regexLiteral}});Object.defineProperty(w,"RestElement",{enumerable:!0,get:function(){return I.restElement}});Object.defineProperty(w,"RestProperty",{enumerable:!0,get:function(){return I.restProperty}});Object.defineProperty(w,"ReturnStatement",{enumerable:!0,get:function(){return I.returnStatement}});Object.defineProperty(w,"SequenceExpression",{enumerable:!0,get:function(){return I.sequenceExpression}});Object.defineProperty(w,"SpreadElement",{enumerable:!0,get:function(){return I.spreadElement}});Object.defineProperty(w,"SpreadProperty",{enumerable:!0,get:function(){return I.spreadProperty}});Object.defineProperty(w,"StaticBlock",{enumerable:!0,get:function(){return I.staticBlock}});Object.defineProperty(w,"StringLiteral",{enumerable:!0,get:function(){return I.stringLiteral}});Object.defineProperty(w,"StringLiteralTypeAnnotation",{enumerable:!0,get:function(){return I.stringLiteralTypeAnnotation}});Object.defineProperty(w,"StringTypeAnnotation",{enumerable:!0,get:function(){return I.stringTypeAnnotation}});Object.defineProperty(w,"Super",{enumerable:!0,get:function(){return I.super}});Object.defineProperty(w,"SwitchCase",{enumerable:!0,get:function(){return I.switchCase}});Object.defineProperty(w,"SwitchStatement",{enumerable:!0,get:function(){return I.switchStatement}});Object.defineProperty(w,"SymbolTypeAnnotation",{enumerable:!0,get:function(){return I.symbolTypeAnnotation}});Object.defineProperty(w,"TSAnyKeyword",{enumerable:!0,get:function(){return I.tsAnyKeyword}});Object.defineProperty(w,"TSArrayType",{enumerable:!0,get:function(){return I.tsArrayType}});Object.defineProperty(w,"TSAsExpression",{enumerable:!0,get:function(){return I.tsAsExpression}});Object.defineProperty(w,"TSBigIntKeyword",{enumerable:!0,get:function(){return I.tsBigIntKeyword}});Object.defineProperty(w,"TSBooleanKeyword",{enumerable:!0,get:function(){return I.tsBooleanKeyword}});Object.defineProperty(w,"TSCallSignatureDeclaration",{enumerable:!0,get:function(){return I.tsCallSignatureDeclaration}});Object.defineProperty(w,"TSConditionalType",{enumerable:!0,get:function(){return I.tsConditionalType}});Object.defineProperty(w,"TSConstructSignatureDeclaration",{enumerable:!0,get:function(){return I.tsConstructSignatureDeclaration}});Object.defineProperty(w,"TSConstructorType",{enumerable:!0,get:function(){return I.tsConstructorType}});Object.defineProperty(w,"TSDeclareFunction",{enumerable:!0,get:function(){return I.tsDeclareFunction}});Object.defineProperty(w,"TSDeclareMethod",{enumerable:!0,get:function(){return I.tsDeclareMethod}});Object.defineProperty(w,"TSEnumBody",{enumerable:!0,get:function(){return I.tsEnumBody}});Object.defineProperty(w,"TSEnumDeclaration",{enumerable:!0,get:function(){return I.tsEnumDeclaration}});Object.defineProperty(w,"TSEnumMember",{enumerable:!0,get:function(){return I.tsEnumMember}});Object.defineProperty(w,"TSExportAssignment",{enumerable:!0,get:function(){return I.tsExportAssignment}});Object.defineProperty(w,"TSExpressionWithTypeArguments",{enumerable:!0,get:function(){return I.tsExpressionWithTypeArguments}});Object.defineProperty(w,"TSExternalModuleReference",{enumerable:!0,get:function(){return I.tsExternalModuleReference}});Object.defineProperty(w,"TSFunctionType",{enumerable:!0,get:function(){return I.tsFunctionType}});Object.defineProperty(w,"TSImportEqualsDeclaration",{enumerable:!0,get:function(){return I.tsImportEqualsDeclaration}});Object.defineProperty(w,"TSImportType",{enumerable:!0,get:function(){return I.tsImportType}});Object.defineProperty(w,"TSIndexSignature",{enumerable:!0,get:function(){return I.tsIndexSignature}});Object.defineProperty(w,"TSIndexedAccessType",{enumerable:!0,get:function(){return I.tsIndexedAccessType}});Object.defineProperty(w,"TSInferType",{enumerable:!0,get:function(){return I.tsInferType}});Object.defineProperty(w,"TSInstantiationExpression",{enumerable:!0,get:function(){return I.tsInstantiationExpression}});Object.defineProperty(w,"TSInterfaceBody",{enumerable:!0,get:function(){return I.tsInterfaceBody}});Object.defineProperty(w,"TSInterfaceDeclaration",{enumerable:!0,get:function(){return I.tsInterfaceDeclaration}});Object.defineProperty(w,"TSIntersectionType",{enumerable:!0,get:function(){return I.tsIntersectionType}});Object.defineProperty(w,"TSIntrinsicKeyword",{enumerable:!0,get:function(){return I.tsIntrinsicKeyword}});Object.defineProperty(w,"TSLiteralType",{enumerable:!0,get:function(){return I.tsLiteralType}});Object.defineProperty(w,"TSMappedType",{enumerable:!0,get:function(){return I.tsMappedType}});Object.defineProperty(w,"TSMethodSignature",{enumerable:!0,get:function(){return I.tsMethodSignature}});Object.defineProperty(w,"TSModuleBlock",{enumerable:!0,get:function(){return I.tsModuleBlock}});Object.defineProperty(w,"TSModuleDeclaration",{enumerable:!0,get:function(){return I.tsModuleDeclaration}});Object.defineProperty(w,"TSNamedTupleMember",{enumerable:!0,get:function(){return I.tsNamedTupleMember}});Object.defineProperty(w,"TSNamespaceExportDeclaration",{enumerable:!0,get:function(){return I.tsNamespaceExportDeclaration}});Object.defineProperty(w,"TSNeverKeyword",{enumerable:!0,get:function(){return I.tsNeverKeyword}});Object.defineProperty(w,"TSNonNullExpression",{enumerable:!0,get:function(){return I.tsNonNullExpression}});Object.defineProperty(w,"TSNullKeyword",{enumerable:!0,get:function(){return I.tsNullKeyword}});Object.defineProperty(w,"TSNumberKeyword",{enumerable:!0,get:function(){return I.tsNumberKeyword}});Object.defineProperty(w,"TSObjectKeyword",{enumerable:!0,get:function(){return I.tsObjectKeyword}});Object.defineProperty(w,"TSOptionalType",{enumerable:!0,get:function(){return I.tsOptionalType}});Object.defineProperty(w,"TSParameterProperty",{enumerable:!0,get:function(){return I.tsParameterProperty}});Object.defineProperty(w,"TSParenthesizedType",{enumerable:!0,get:function(){return I.tsParenthesizedType}});Object.defineProperty(w,"TSPropertySignature",{enumerable:!0,get:function(){return I.tsPropertySignature}});Object.defineProperty(w,"TSQualifiedName",{enumerable:!0,get:function(){return I.tsQualifiedName}});Object.defineProperty(w,"TSRestType",{enumerable:!0,get:function(){return I.tsRestType}});Object.defineProperty(w,"TSSatisfiesExpression",{enumerable:!0,get:function(){return I.tsSatisfiesExpression}});Object.defineProperty(w,"TSStringKeyword",{enumerable:!0,get:function(){return I.tsStringKeyword}});Object.defineProperty(w,"TSSymbolKeyword",{enumerable:!0,get:function(){return I.tsSymbolKeyword}});Object.defineProperty(w,"TSTemplateLiteralType",{enumerable:!0,get:function(){return I.tsTemplateLiteralType}});Object.defineProperty(w,"TSThisType",{enumerable:!0,get:function(){return I.tsThisType}});Object.defineProperty(w,"TSTupleType",{enumerable:!0,get:function(){return I.tsTupleType}});Object.defineProperty(w,"TSTypeAliasDeclaration",{enumerable:!0,get:function(){return I.tsTypeAliasDeclaration}});Object.defineProperty(w,"TSTypeAnnotation",{enumerable:!0,get:function(){return I.tsTypeAnnotation}});Object.defineProperty(w,"TSTypeAssertion",{enumerable:!0,get:function(){return I.tsTypeAssertion}});Object.defineProperty(w,"TSTypeLiteral",{enumerable:!0,get:function(){return I.tsTypeLiteral}});Object.defineProperty(w,"TSTypeOperator",{enumerable:!0,get:function(){return I.tsTypeOperator}});Object.defineProperty(w,"TSTypeParameter",{enumerable:!0,get:function(){return I.tsTypeParameter}});Object.defineProperty(w,"TSTypeParameterDeclaration",{enumerable:!0,get:function(){return I.tsTypeParameterDeclaration}});Object.defineProperty(w,"TSTypeParameterInstantiation",{enumerable:!0,get:function(){return I.tsTypeParameterInstantiation}});Object.defineProperty(w,"TSTypePredicate",{enumerable:!0,get:function(){return I.tsTypePredicate}});Object.defineProperty(w,"TSTypeQuery",{enumerable:!0,get:function(){return I.tsTypeQuery}});Object.defineProperty(w,"TSTypeReference",{enumerable:!0,get:function(){return I.tsTypeReference}});Object.defineProperty(w,"TSUndefinedKeyword",{enumerable:!0,get:function(){return I.tsUndefinedKeyword}});Object.defineProperty(w,"TSUnionType",{enumerable:!0,get:function(){return I.tsUnionType}});Object.defineProperty(w,"TSUnknownKeyword",{enumerable:!0,get:function(){return I.tsUnknownKeyword}});Object.defineProperty(w,"TSVoidKeyword",{enumerable:!0,get:function(){return I.tsVoidKeyword}});Object.defineProperty(w,"TaggedTemplateExpression",{enumerable:!0,get:function(){return I.taggedTemplateExpression}});Object.defineProperty(w,"TemplateElement",{enumerable:!0,get:function(){return I.templateElement}});Object.defineProperty(w,"TemplateLiteral",{enumerable:!0,get:function(){return I.templateLiteral}});Object.defineProperty(w,"ThisExpression",{enumerable:!0,get:function(){return I.thisExpression}});Object.defineProperty(w,"ThisTypeAnnotation",{enumerable:!0,get:function(){return I.thisTypeAnnotation}});Object.defineProperty(w,"ThrowStatement",{enumerable:!0,get:function(){return I.throwStatement}});Object.defineProperty(w,"TopicReference",{enumerable:!0,get:function(){return I.topicReference}});Object.defineProperty(w,"TryStatement",{enumerable:!0,get:function(){return I.tryStatement}});Object.defineProperty(w,"TupleExpression",{enumerable:!0,get:function(){return I.tupleExpression}});Object.defineProperty(w,"TupleTypeAnnotation",{enumerable:!0,get:function(){return I.tupleTypeAnnotation}});Object.defineProperty(w,"TypeAlias",{enumerable:!0,get:function(){return I.typeAlias}});Object.defineProperty(w,"TypeAnnotation",{enumerable:!0,get:function(){return I.typeAnnotation}});Object.defineProperty(w,"TypeCastExpression",{enumerable:!0,get:function(){return I.typeCastExpression}});Object.defineProperty(w,"TypeParameter",{enumerable:!0,get:function(){return I.typeParameter}});Object.defineProperty(w,"TypeParameterDeclaration",{enumerable:!0,get:function(){return I.typeParameterDeclaration}});Object.defineProperty(w,"TypeParameterInstantiation",{enumerable:!0,get:function(){return I.typeParameterInstantiation}});Object.defineProperty(w,"TypeofTypeAnnotation",{enumerable:!0,get:function(){return I.typeofTypeAnnotation}});Object.defineProperty(w,"UnaryExpression",{enumerable:!0,get:function(){return I.unaryExpression}});Object.defineProperty(w,"UnionTypeAnnotation",{enumerable:!0,get:function(){return I.unionTypeAnnotation}});Object.defineProperty(w,"UpdateExpression",{enumerable:!0,get:function(){return I.updateExpression}});Object.defineProperty(w,"V8IntrinsicIdentifier",{enumerable:!0,get:function(){return I.v8IntrinsicIdentifier}});Object.defineProperty(w,"VariableDeclaration",{enumerable:!0,get:function(){return I.variableDeclaration}});Object.defineProperty(w,"VariableDeclarator",{enumerable:!0,get:function(){return I.variableDeclarator}});Object.defineProperty(w,"Variance",{enumerable:!0,get:function(){return I.variance}});Object.defineProperty(w,"VoidTypeAnnotation",{enumerable:!0,get:function(){return I.voidTypeAnnotation}});Object.defineProperty(w,"WhileStatement",{enumerable:!0,get:function(){return I.whileStatement}});Object.defineProperty(w,"WithStatement",{enumerable:!0,get:function(){return I.withStatement}});Object.defineProperty(w,"YieldExpression",{enumerable:!0,get:function(){return I.yieldExpression}});var I=Sd()});var Pr=T(ys=>{"use strict";Object.defineProperty(ys,"__esModule",{value:!0});var xd=Sd();Object.keys(xd).forEach(function(e){e==="default"||e==="__esModule"||e in ys&&ys[e]===xd[e]||Object.defineProperty(ys,e,{enumerable:!0,get:function(){return xd[e]}})});var vd=_T();Object.keys(vd).forEach(function(e){e==="default"||e==="__esModule"||e in ys&&ys[e]===vd[e]||Object.defineProperty(ys,e,{enumerable:!0,get:function(){return vd[e]}})})});var OT=T(Pd=>{"use strict";Object.defineProperty(Pd,"__esModule",{value:!0});Pd.default=K5;var $5=Pr(),W5=Ne();function K5(e,t){let r=e.value.split(/\r\n|\n|\r/),n=0;for(let i=0;i<r.length;i++)/[^ \t]/.exec(r[i])&&(n=i);let s="";for(let i=0;i<r.length;i++){let a=r[i],o=i===0,l=i===r.length-1,u=i===n,c=a.replace(/\t/g," ");o||(c=c.replace(/^ +/,"")),l||(c=c.replace(/ +$/,"")),c&&(u||(c+=" "),s+=c)}s&&t.push((0,W5.inherits)((0,$5.stringLiteral)(s),e))}});var NT=T(Cd=>{"use strict";Object.defineProperty(Cd,"__esModule",{value:!0});Cd.default=H5;var Ad=Nt(),G5=OT();function H5(e){let t=[];for(let r=0;r<e.children.length;r++){let n=e.children[r];if((0,Ad.isJSXText)(n)){(0,G5.default)(n,t);continue}(0,Ad.isJSXExpressionContainer)(n)&&(n=n.expression),!(0,Ad.isJSXEmptyExpression)(n)&&t.push(n)}return t}});var wd=T(Dd=>{"use strict";Object.defineProperty(Dd,"__esModule",{value:!0});Dd.default=X5;var Y5=Fr();function X5(e){return!!(e&&Y5.VISITOR_KEYS[e.type])}});var BT=T(Id=>{"use strict";Object.defineProperty(Id,"__esModule",{value:!0});Id.default=z5;var J5=wd();function z5(e){if(!(0,J5.default)(e)){var t;let r=(t=e==null?void 0:e.type)!=null?t:JSON.stringify(e);throw new TypeError(`Not a valid node of type "${r}"`)}}});var kT=T(x=>{"use strict";Object.defineProperty(x,"__esModule",{value:!0});x.assertAccessor=m$;x.assertAnyTypeAnnotation=B9;x.assertArgumentPlaceholder=lU;x.assertArrayExpression=Z5;x.assertArrayPattern=z7;x.assertArrayTypeAnnotation=k9;x.assertArrowFunctionExpression=Q7;x.assertAssignmentExpression=e7;x.assertAssignmentPattern=J7;x.assertAwaitExpression=S9;x.assertBigIntLiteral=v9;x.assertBinary=jV;x.assertBinaryExpression=t7;x.assertBindExpression=uU;x.assertBlock=qV;x.assertBlockParent=RV;x.assertBlockStatement=i7;x.assertBooleanLiteral=C7;x.assertBooleanLiteralTypeAnnotation=L9;x.assertBooleanTypeAnnotation=F9;x.assertBreakStatement=a7;x.assertCallExpression=o7;x.assertCatchClause=l7;x.assertClass=p$;x.assertClassAccessorProperty=w9;x.assertClassBody=Z7;x.assertClassDeclaration=t9;x.assertClassExpression=e9;x.assertClassImplements=M9;x.assertClassMethod=d9;x.assertClassPrivateMethod=_9;x.assertClassPrivateProperty=I9;x.assertClassProperty=D9;x.assertCompletionStatement=$V;x.assertConditional=WV;x.assertConditionalExpression=u7;x.assertContinueStatement=c7;x.assertDebuggerStatement=p7;x.assertDecimalLiteral=yU;x.assertDeclaration=ZV;x.assertDeclareClass=R9;x.assertDeclareExportAllDeclaration=Y9;x.assertDeclareExportDeclaration=H9;x.assertDeclareFunction=q9;x.assertDeclareInterface=U9;x.assertDeclareModule=V9;x.assertDeclareModuleExports=$9;x.assertDeclareOpaqueType=K9;x.assertDeclareTypeAlias=W9;x.assertDeclareVariable=G9;x.assertDeclaredPredicate=X9;x.assertDecorator=pU;x.assertDirective=n7;x.assertDirectiveLiteral=s7;x.assertDoExpression=fU;x.assertDoWhileStatement=f7;x.assertEmptyStatement=d7;x.assertEmptyTypeAnnotation=aq;x.assertEnumBody=x$;x.assertEnumBooleanBody=kq;x.assertEnumBooleanMember=Mq;x.assertEnumDeclaration=Bq;x.assertEnumDefaultedMember=Uq;x.assertEnumMember=v$;x.assertEnumNumberBody=Fq;x.assertEnumNumberMember=Rq;x.assertEnumStringBody=Lq;x.assertEnumStringMember=qq;x.assertEnumSymbolBody=jq;x.assertExistsTypeAnnotation=J9;x.assertExportAllDeclaration=r9;x.assertExportDeclaration=d$;x.assertExportDefaultDeclaration=n9;x.assertExportDefaultSpecifier=dU;x.assertExportNamedDeclaration=s9;x.assertExportNamespaceSpecifier=P9;x.assertExportSpecifier=i9;x.assertExpression=LV;x.assertExpressionStatement=h7;x.assertExpressionWrapper=HV;x.assertFile=m7;x.assertFlow=g$;x.assertFlowBaseAnnotation=E$;x.assertFlowDeclaration=T$;x.assertFlowPredicate=S$;x.assertFlowType=b$;x.assertFor=YV;x.assertForInStatement=y7;x.assertForOfStatement=a9;x.assertForStatement=g7;x.assertForXStatement=XV;x.assertFunction=JV;x.assertFunctionDeclaration=b7;x.assertFunctionExpression=E7;x.assertFunctionParent=zV;x.assertFunctionTypeAnnotation=z9;x.assertFunctionTypeParam=Q9;x.assertGenericTypeAnnotation=Z9;x.assertIdentifier=T7;x.assertIfStatement=S7;x.assertImmutable=s$;x.assertImport=x9;x.assertImportAttribute=cU;x.assertImportDeclaration=o9;x.assertImportDefaultSpecifier=l9;x.assertImportExpression=p9;x.assertImportNamespaceSpecifier=u9;x.assertImportOrExportDeclaration=f$;x.assertImportSpecifier=c9;x.assertIndexedAccessType=Vq;x.assertInferredPredicate=eq;x.assertInterfaceDeclaration=rq;x.assertInterfaceExtends=tq;x.assertInterfaceTypeAnnotation=nq;x.assertInterpreterDirective=r7;x.assertIntersectionTypeAnnotation=sq;x.assertJSX=P$;x.assertJSXAttribute=Wq;x.assertJSXClosingElement=Kq;x.assertJSXClosingFragment=sU;x.assertJSXElement=Gq;x.assertJSXEmptyExpression=Hq;x.assertJSXExpressionContainer=Yq;x.assertJSXFragment=rU;x.assertJSXIdentifier=Jq;x.assertJSXMemberExpression=zq;x.assertJSXNamespacedName=Qq;x.assertJSXOpeningElement=Zq;x.assertJSXOpeningFragment=nU;x.assertJSXSpreadAttribute=eU;x.assertJSXSpreadChild=Xq;x.assertJSXText=tU;x.assertLVal=t$;x.assertLabeledStatement=x7;x.assertLiteral=n$;x.assertLogicalExpression=w7;x.assertLoop=KV;x.assertMemberExpression=I7;x.assertMetaProperty=f9;x.assertMethod=a$;x.assertMiscellaneous=A$;x.assertMixedTypeAnnotation=iq;x.assertModuleDeclaration=k$;x.assertModuleExpression=gU;x.assertModuleSpecifier=h$;x.assertNewExpression=_7;x.assertNoop=iU;x.assertNullLiteral=A7;x.assertNullLiteralTypeAnnotation=j9;x.assertNullableTypeAnnotation=oq;x.assertNumberLiteral=_$;x.assertNumberLiteralTypeAnnotation=lq;x.assertNumberTypeAnnotation=uq;x.assertNumericLiteral=P7;x.assertObjectExpression=N7;x.assertObjectMember=o$;x.assertObjectMethod=B7;x.assertObjectPattern=h9;x.assertObjectProperty=k7;x.assertObjectTypeAnnotation=cq;x.assertObjectTypeCallProperty=fq;x.assertObjectTypeIndexer=dq;x.assertObjectTypeInternalSlot=pq;x.assertObjectTypeProperty=hq;x.assertObjectTypeSpreadProperty=mq;x.assertOpaqueType=yq;x.assertOptionalCallExpression=C9;x.assertOptionalIndexedAccessType=$q;x.assertOptionalMemberExpression=A9;x.assertParenthesizedExpression=M7;x.assertPattern=c$;x.assertPatternLike=e$;x.assertPipelineBareFunction=TU;x.assertPipelinePrimaryTopicReference=SU;x.assertPipelineTopicExpression=EU;x.assertPlaceholder=aU;x.assertPrivate=y$;x.assertPrivateName=O9;x.assertProgram=O7;x.assertProperty=l$;x.assertPureish=QV;x.assertQualifiedTypeIdentifier=gq;x.assertRecordExpression=hU;x.assertRegExpLiteral=D7;x.assertRegexLiteral=O$;x.assertRestElement=F7;x.assertRestProperty=N$;x.assertReturnStatement=L7;x.assertScopable=MV;x.assertSequenceExpression=j7;x.assertSpreadElement=m9;x.assertSpreadProperty=B$;x.assertStandardized=FV;x.assertStatement=UV;x.assertStaticBlock=N9;x.assertStringLiteral=v7;x.assertStringLiteralTypeAnnotation=bq;x.assertStringTypeAnnotation=Eq;x.assertSuper=y9;x.assertSwitchCase=R7;x.assertSwitchStatement=q7;x.assertSymbolTypeAnnotation=Tq;x.assertTSAnyKeyword=OU;x.assertTSArrayType=zU;x.assertTSAsExpression=gV;x.assertTSBaseType=I$;x.assertTSBigIntKeyword=BU;x.assertTSBooleanKeyword=NU;x.assertTSCallSignatureDeclaration=CU;x.assertTSConditionalType=sV;x.assertTSConstructSignatureDeclaration=DU;x.assertTSConstructorType=GU;x.assertTSDeclareFunction=vU;x.assertTSDeclareMethod=PU;x.assertTSEntityName=r$;x.assertTSEnumBody=TV;x.assertTSEnumDeclaration=SV;x.assertTSEnumMember=xV;x.assertTSExportAssignment=IV;x.assertTSExpressionWithTypeArguments=fV;x.assertTSExternalModuleReference=DV;x.assertTSFunctionType=KU;x.assertTSImportEqualsDeclaration=CV;x.assertTSImportType=AV;x.assertTSIndexSignature=_U;x.assertTSIndexedAccessType=lV;x.assertTSInferType=iV;x.assertTSInstantiationExpression=yV;x.assertTSInterfaceBody=hV;x.assertTSInterfaceDeclaration=dV;x.assertTSIntersectionType=nV;x.assertTSIntrinsicKeyword=kU;x.assertTSLiteralType=pV;x.assertTSMappedType=uV;x.assertTSMethodSignature=IU;x.assertTSModuleBlock=PV;x.assertTSModuleDeclaration=vV;x.assertTSNamedTupleMember=tV;x.assertTSNamespaceExportDeclaration=_V;x.assertTSNeverKeyword=FU;x.assertTSNonNullExpression=wV;x.assertTSNullKeyword=LU;x.assertTSNumberKeyword=jU;x.assertTSObjectKeyword=MU;x.assertTSOptionalType=ZU;x.assertTSParameterProperty=xU;x.assertTSParenthesizedType=aV;x.assertTSPropertySignature=wU;x.assertTSQualifiedName=AU;x.assertTSRestType=eV;x.assertTSSatisfiesExpression=bV;x.assertTSStringKeyword=RU;x.assertTSSymbolKeyword=qU;x.assertTSTemplateLiteralType=cV;x.assertTSThisType=WU;x.assertTSTupleType=QU;x.assertTSType=w$;x.assertTSTypeAliasDeclaration=mV;x.assertTSTypeAnnotation=OV;x.assertTSTypeAssertion=EV;x.assertTSTypeElement=D$;x.assertTSTypeLiteral=JU;x.assertTSTypeOperator=oV;x.assertTSTypeParameter=kV;x.assertTSTypeParameterDeclaration=BV;x.assertTSTypeParameterInstantiation=NV;x.assertTSTypePredicate=YU;x.assertTSTypeQuery=XU;x.assertTSTypeReference=HU;x.assertTSUndefinedKeyword=UU;x.assertTSUnionType=rV;x.assertTSUnknownKeyword=VU;x.assertTSVoidKeyword=$U;x.assertTaggedTemplateExpression=g9;x.assertTemplateElement=b9;x.assertTemplateLiteral=E9;x.assertTerminatorless=VV;x.assertThisExpression=U7;x.assertThisTypeAnnotation=Sq;x.assertThrowStatement=V7;x.assertTopicReference=bU;x.assertTryStatement=$7;x.assertTupleExpression=mU;x.assertTupleTypeAnnotation=xq;x.assertTypeAlias=Pq;x.assertTypeAnnotation=Aq;x.assertTypeCastExpression=Cq;x.assertTypeParameter=Dq;x.assertTypeParameterDeclaration=wq;x.assertTypeParameterInstantiation=Iq;x.assertTypeScript=C$;x.assertTypeofTypeAnnotation=vq;x.assertUnaryExpression=W7;x.assertUnaryLike=u$;x.assertUnionTypeAnnotation=_q;x.assertUpdateExpression=K7;x.assertUserWhitespacable=i$;x.assertV8IntrinsicIdentifier=oU;x.assertVariableDeclaration=G7;x.assertVariableDeclarator=H7;x.assertVariance=Oq;x.assertVoidTypeAnnotation=Nq;x.assertWhile=GV;x.assertWhileStatement=Y7;x.assertWithStatement=X7;x.assertYieldExpression=T9;var Q5=Ui(),no=Ya();function v(e,t,r){if(!(0,Q5.default)(e,t,r))throw new Error(`Expected type "${e}" with option ${JSON.stringify(r)}, but instead got "${t.type}".`)}function Z5(e,t){v("ArrayExpression",e,t)}function e7(e,t){v("AssignmentExpression",e,t)}function t7(e,t){v("BinaryExpression",e,t)}function r7(e,t){v("InterpreterDirective",e,t)}function n7(e,t){v("Directive",e,t)}function s7(e,t){v("DirectiveLiteral",e,t)}function i7(e,t){v("BlockStatement",e,t)}function a7(e,t){v("BreakStatement",e,t)}function o7(e,t){v("CallExpression",e,t)}function l7(e,t){v("CatchClause",e,t)}function u7(e,t){v("ConditionalExpression",e,t)}function c7(e,t){v("ContinueStatement",e,t)}function p7(e,t){v("DebuggerStatement",e,t)}function f7(e,t){v("DoWhileStatement",e,t)}function d7(e,t){v("EmptyStatement",e,t)}function h7(e,t){v("ExpressionStatement",e,t)}function m7(e,t){v("File",e,t)}function y7(e,t){v("ForInStatement",e,t)}function g7(e,t){v("ForStatement",e,t)}function b7(e,t){v("FunctionDeclaration",e,t)}function E7(e,t){v("FunctionExpression",e,t)}function T7(e,t){v("Identifier",e,t)}function S7(e,t){v("IfStatement",e,t)}function x7(e,t){v("LabeledStatement",e,t)}function v7(e,t){v("StringLiteral",e,t)}function P7(e,t){v("NumericLiteral",e,t)}function A7(e,t){v("NullLiteral",e,t)}function C7(e,t){v("BooleanLiteral",e,t)}function D7(e,t){v("RegExpLiteral",e,t)}function w7(e,t){v("LogicalExpression",e,t)}function I7(e,t){v("MemberExpression",e,t)}function _7(e,t){v("NewExpression",e,t)}function O7(e,t){v("Program",e,t)}function N7(e,t){v("ObjectExpression",e,t)}function B7(e,t){v("ObjectMethod",e,t)}function k7(e,t){v("ObjectProperty",e,t)}function F7(e,t){v("RestElement",e,t)}function L7(e,t){v("ReturnStatement",e,t)}function j7(e,t){v("SequenceExpression",e,t)}function M7(e,t){v("ParenthesizedExpression",e,t)}function R7(e,t){v("SwitchCase",e,t)}function q7(e,t){v("SwitchStatement",e,t)}function U7(e,t){v("ThisExpression",e,t)}function V7(e,t){v("ThrowStatement",e,t)}function $7(e,t){v("TryStatement",e,t)}function W7(e,t){v("UnaryExpression",e,t)}function K7(e,t){v("UpdateExpression",e,t)}function G7(e,t){v("VariableDeclaration",e,t)}function H7(e,t){v("VariableDeclarator",e,t)}function Y7(e,t){v("WhileStatement",e,t)}function X7(e,t){v("WithStatement",e,t)}function J7(e,t){v("AssignmentPattern",e,t)}function z7(e,t){v("ArrayPattern",e,t)}function Q7(e,t){v("ArrowFunctionExpression",e,t)}function Z7(e,t){v("ClassBody",e,t)}function e9(e,t){v("ClassExpression",e,t)}function t9(e,t){v("ClassDeclaration",e,t)}function r9(e,t){v("ExportAllDeclaration",e,t)}function n9(e,t){v("ExportDefaultDeclaration",e,t)}function s9(e,t){v("ExportNamedDeclaration",e,t)}function i9(e,t){v("ExportSpecifier",e,t)}function a9(e,t){v("ForOfStatement",e,t)}function o9(e,t){v("ImportDeclaration",e,t)}function l9(e,t){v("ImportDefaultSpecifier",e,t)}function u9(e,t){v("ImportNamespaceSpecifier",e,t)}function c9(e,t){v("ImportSpecifier",e,t)}function p9(e,t){v("ImportExpression",e,t)}function f9(e,t){v("MetaProperty",e,t)}function d9(e,t){v("ClassMethod",e,t)}function h9(e,t){v("ObjectPattern",e,t)}function m9(e,t){v("SpreadElement",e,t)}function y9(e,t){v("Super",e,t)}function g9(e,t){v("TaggedTemplateExpression",e,t)}function b9(e,t){v("TemplateElement",e,t)}function E9(e,t){v("TemplateLiteral",e,t)}function T9(e,t){v("YieldExpression",e,t)}function S9(e,t){v("AwaitExpression",e,t)}function x9(e,t){v("Import",e,t)}function v9(e,t){v("BigIntLiteral",e,t)}function P9(e,t){v("ExportNamespaceSpecifier",e,t)}function A9(e,t){v("OptionalMemberExpression",e,t)}function C9(e,t){v("OptionalCallExpression",e,t)}function D9(e,t){v("ClassProperty",e,t)}function w9(e,t){v("ClassAccessorProperty",e,t)}function I9(e,t){v("ClassPrivateProperty",e,t)}function _9(e,t){v("ClassPrivateMethod",e,t)}function O9(e,t){v("PrivateName",e,t)}function N9(e,t){v("StaticBlock",e,t)}function B9(e,t){v("AnyTypeAnnotation",e,t)}function k9(e,t){v("ArrayTypeAnnotation",e,t)}function F9(e,t){v("BooleanTypeAnnotation",e,t)}function L9(e,t){v("BooleanLiteralTypeAnnotation",e,t)}function j9(e,t){v("NullLiteralTypeAnnotation",e,t)}function M9(e,t){v("ClassImplements",e,t)}function R9(e,t){v("DeclareClass",e,t)}function q9(e,t){v("DeclareFunction",e,t)}function U9(e,t){v("DeclareInterface",e,t)}function V9(e,t){v("DeclareModule",e,t)}function $9(e,t){v("DeclareModuleExports",e,t)}function W9(e,t){v("DeclareTypeAlias",e,t)}function K9(e,t){v("DeclareOpaqueType",e,t)}function G9(e,t){v("DeclareVariable",e,t)}function H9(e,t){v("DeclareExportDeclaration",e,t)}function Y9(e,t){v("DeclareExportAllDeclaration",e,t)}function X9(e,t){v("DeclaredPredicate",e,t)}function J9(e,t){v("ExistsTypeAnnotation",e,t)}function z9(e,t){v("FunctionTypeAnnotation",e,t)}function Q9(e,t){v("FunctionTypeParam",e,t)}function Z9(e,t){v("GenericTypeAnnotation",e,t)}function eq(e,t){v("InferredPredicate",e,t)}function tq(e,t){v("InterfaceExtends",e,t)}function rq(e,t){v("InterfaceDeclaration",e,t)}function nq(e,t){v("InterfaceTypeAnnotation",e,t)}function sq(e,t){v("IntersectionTypeAnnotation",e,t)}function iq(e,t){v("MixedTypeAnnotation",e,t)}function aq(e,t){v("EmptyTypeAnnotation",e,t)}function oq(e,t){v("NullableTypeAnnotation",e,t)}function lq(e,t){v("NumberLiteralTypeAnnotation",e,t)}function uq(e,t){v("NumberTypeAnnotation",e,t)}function cq(e,t){v("ObjectTypeAnnotation",e,t)}function pq(e,t){v("ObjectTypeInternalSlot",e,t)}function fq(e,t){v("ObjectTypeCallProperty",e,t)}function dq(e,t){v("ObjectTypeIndexer",e,t)}function hq(e,t){v("ObjectTypeProperty",e,t)}function mq(e,t){v("ObjectTypeSpreadProperty",e,t)}function yq(e,t){v("OpaqueType",e,t)}function gq(e,t){v("QualifiedTypeIdentifier",e,t)}function bq(e,t){v("StringLiteralTypeAnnotation",e,t)}function Eq(e,t){v("StringTypeAnnotation",e,t)}function Tq(e,t){v("SymbolTypeAnnotation",e,t)}function Sq(e,t){v("ThisTypeAnnotation",e,t)}function xq(e,t){v("TupleTypeAnnotation",e,t)}function vq(e,t){v("TypeofTypeAnnotation",e,t)}function Pq(e,t){v("TypeAlias",e,t)}function Aq(e,t){v("TypeAnnotation",e,t)}function Cq(e,t){v("TypeCastExpression",e,t)}function Dq(e,t){v("TypeParameter",e,t)}function wq(e,t){v("TypeParameterDeclaration",e,t)}function Iq(e,t){v("TypeParameterInstantiation",e,t)}function _q(e,t){v("UnionTypeAnnotation",e,t)}function Oq(e,t){v("Variance",e,t)}function Nq(e,t){v("VoidTypeAnnotation",e,t)}function Bq(e,t){v("EnumDeclaration",e,t)}function kq(e,t){v("EnumBooleanBody",e,t)}function Fq(e,t){v("EnumNumberBody",e,t)}function Lq(e,t){v("EnumStringBody",e,t)}function jq(e,t){v("EnumSymbolBody",e,t)}function Mq(e,t){v("EnumBooleanMember",e,t)}function Rq(e,t){v("EnumNumberMember",e,t)}function qq(e,t){v("EnumStringMember",e,t)}function Uq(e,t){v("EnumDefaultedMember",e,t)}function Vq(e,t){v("IndexedAccessType",e,t)}function $q(e,t){v("OptionalIndexedAccessType",e,t)}function Wq(e,t){v("JSXAttribute",e,t)}function Kq(e,t){v("JSXClosingElement",e,t)}function Gq(e,t){v("JSXElement",e,t)}function Hq(e,t){v("JSXEmptyExpression",e,t)}function Yq(e,t){v("JSXExpressionContainer",e,t)}function Xq(e,t){v("JSXSpreadChild",e,t)}function Jq(e,t){v("JSXIdentifier",e,t)}function zq(e,t){v("JSXMemberExpression",e,t)}function Qq(e,t){v("JSXNamespacedName",e,t)}function Zq(e,t){v("JSXOpeningElement",e,t)}function eU(e,t){v("JSXSpreadAttribute",e,t)}function tU(e,t){v("JSXText",e,t)}function rU(e,t){v("JSXFragment",e,t)}function nU(e,t){v("JSXOpeningFragment",e,t)}function sU(e,t){v("JSXClosingFragment",e,t)}function iU(e,t){v("Noop",e,t)}function aU(e,t){v("Placeholder",e,t)}function oU(e,t){v("V8IntrinsicIdentifier",e,t)}function lU(e,t){v("ArgumentPlaceholder",e,t)}function uU(e,t){v("BindExpression",e,t)}function cU(e,t){v("ImportAttribute",e,t)}function pU(e,t){v("Decorator",e,t)}function fU(e,t){v("DoExpression",e,t)}function dU(e,t){v("ExportDefaultSpecifier",e,t)}function hU(e,t){v("RecordExpression",e,t)}function mU(e,t){v("TupleExpression",e,t)}function yU(e,t){v("DecimalLiteral",e,t)}function gU(e,t){v("ModuleExpression",e,t)}function bU(e,t){v("TopicReference",e,t)}function EU(e,t){v("PipelineTopicExpression",e,t)}function TU(e,t){v("PipelineBareFunction",e,t)}function SU(e,t){v("PipelinePrimaryTopicReference",e,t)}function xU(e,t){v("TSParameterProperty",e,t)}function vU(e,t){v("TSDeclareFunction",e,t)}function PU(e,t){v("TSDeclareMethod",e,t)}function AU(e,t){v("TSQualifiedName",e,t)}function CU(e,t){v("TSCallSignatureDeclaration",e,t)}function DU(e,t){v("TSConstructSignatureDeclaration",e,t)}function wU(e,t){v("TSPropertySignature",e,t)}function IU(e,t){v("TSMethodSignature",e,t)}function _U(e,t){v("TSIndexSignature",e,t)}function OU(e,t){v("TSAnyKeyword",e,t)}function NU(e,t){v("TSBooleanKeyword",e,t)}function BU(e,t){v("TSBigIntKeyword",e,t)}function kU(e,t){v("TSIntrinsicKeyword",e,t)}function FU(e,t){v("TSNeverKeyword",e,t)}function LU(e,t){v("TSNullKeyword",e,t)}function jU(e,t){v("TSNumberKeyword",e,t)}function MU(e,t){v("TSObjectKeyword",e,t)}function RU(e,t){v("TSStringKeyword",e,t)}function qU(e,t){v("TSSymbolKeyword",e,t)}function UU(e,t){v("TSUndefinedKeyword",e,t)}function VU(e,t){v("TSUnknownKeyword",e,t)}function $U(e,t){v("TSVoidKeyword",e,t)}function WU(e,t){v("TSThisType",e,t)}function KU(e,t){v("TSFunctionType",e,t)}function GU(e,t){v("TSConstructorType",e,t)}function HU(e,t){v("TSTypeReference",e,t)}function YU(e,t){v("TSTypePredicate",e,t)}function XU(e,t){v("TSTypeQuery",e,t)}function JU(e,t){v("TSTypeLiteral",e,t)}function zU(e,t){v("TSArrayType",e,t)}function QU(e,t){v("TSTupleType",e,t)}function ZU(e,t){v("TSOptionalType",e,t)}function eV(e,t){v("TSRestType",e,t)}function tV(e,t){v("TSNamedTupleMember",e,t)}function rV(e,t){v("TSUnionType",e,t)}function nV(e,t){v("TSIntersectionType",e,t)}function sV(e,t){v("TSConditionalType",e,t)}function iV(e,t){v("TSInferType",e,t)}function aV(e,t){v("TSParenthesizedType",e,t)}function oV(e,t){v("TSTypeOperator",e,t)}function lV(e,t){v("TSIndexedAccessType",e,t)}function uV(e,t){v("TSMappedType",e,t)}function cV(e,t){v("TSTemplateLiteralType",e,t)}function pV(e,t){v("TSLiteralType",e,t)}function fV(e,t){v("TSExpressionWithTypeArguments",e,t)}function dV(e,t){v("TSInterfaceDeclaration",e,t)}function hV(e,t){v("TSInterfaceBody",e,t)}function mV(e,t){v("TSTypeAliasDeclaration",e,t)}function yV(e,t){v("TSInstantiationExpression",e,t)}function gV(e,t){v("TSAsExpression",e,t)}function bV(e,t){v("TSSatisfiesExpression",e,t)}function EV(e,t){v("TSTypeAssertion",e,t)}function TV(e,t){v("TSEnumBody",e,t)}function SV(e,t){v("TSEnumDeclaration",e,t)}function xV(e,t){v("TSEnumMember",e,t)}function vV(e,t){v("TSModuleDeclaration",e,t)}function PV(e,t){v("TSModuleBlock",e,t)}function AV(e,t){v("TSImportType",e,t)}function CV(e,t){v("TSImportEqualsDeclaration",e,t)}function DV(e,t){v("TSExternalModuleReference",e,t)}function wV(e,t){v("TSNonNullExpression",e,t)}function IV(e,t){v("TSExportAssignment",e,t)}function _V(e,t){v("TSNamespaceExportDeclaration",e,t)}function OV(e,t){v("TSTypeAnnotation",e,t)}function NV(e,t){v("TSTypeParameterInstantiation",e,t)}function BV(e,t){v("TSTypeParameterDeclaration",e,t)}function kV(e,t){v("TSTypeParameter",e,t)}function FV(e,t){v("Standardized",e,t)}function LV(e,t){v("Expression",e,t)}function jV(e,t){v("Binary",e,t)}function MV(e,t){v("Scopable",e,t)}function RV(e,t){v("BlockParent",e,t)}function qV(e,t){v("Block",e,t)}function UV(e,t){v("Statement",e,t)}function VV(e,t){v("Terminatorless",e,t)}function $V(e,t){v("CompletionStatement",e,t)}function WV(e,t){v("Conditional",e,t)}function KV(e,t){v("Loop",e,t)}function GV(e,t){v("While",e,t)}function HV(e,t){v("ExpressionWrapper",e,t)}function YV(e,t){v("For",e,t)}function XV(e,t){v("ForXStatement",e,t)}function JV(e,t){v("Function",e,t)}function zV(e,t){v("FunctionParent",e,t)}function QV(e,t){v("Pureish",e,t)}function ZV(e,t){v("Declaration",e,t)}function e$(e,t){v("PatternLike",e,t)}function t$(e,t){v("LVal",e,t)}function r$(e,t){v("TSEntityName",e,t)}function n$(e,t){v("Literal",e,t)}function s$(e,t){v("Immutable",e,t)}function i$(e,t){v("UserWhitespacable",e,t)}function a$(e,t){v("Method",e,t)}function o$(e,t){v("ObjectMember",e,t)}function l$(e,t){v("Property",e,t)}function u$(e,t){v("UnaryLike",e,t)}function c$(e,t){v("Pattern",e,t)}function p$(e,t){v("Class",e,t)}function f$(e,t){v("ImportOrExportDeclaration",e,t)}function d$(e,t){v("ExportDeclaration",e,t)}function h$(e,t){v("ModuleSpecifier",e,t)}function m$(e,t){v("Accessor",e,t)}function y$(e,t){v("Private",e,t)}function g$(e,t){v("Flow",e,t)}function b$(e,t){v("FlowType",e,t)}function E$(e,t){v("FlowBaseAnnotation",e,t)}function T$(e,t){v("FlowDeclaration",e,t)}function S$(e,t){v("FlowPredicate",e,t)}function x$(e,t){v("EnumBody",e,t)}function v$(e,t){v("EnumMember",e,t)}function P$(e,t){v("JSX",e,t)}function A$(e,t){v("Miscellaneous",e,t)}function C$(e,t){v("TypeScript",e,t)}function D$(e,t){v("TSTypeElement",e,t)}function w$(e,t){v("TSType",e,t)}function I$(e,t){v("TSBaseType",e,t)}function _$(e,t){(0,no.default)("assertNumberLiteral","assertNumericLiteral"),v("NumberLiteral",e,t)}function O$(e,t){(0,no.default)("assertRegexLiteral","assertRegExpLiteral"),v("RegexLiteral",e,t)}function N$(e,t){(0,no.default)("assertRestProperty","assertRestElement"),v("RestProperty",e,t)}function B$(e,t){(0,no.default)("assertSpreadProperty","assertSpreadElement"),v("SpreadProperty",e,t)}function k$(e,t){(0,no.default)("assertModuleDeclaration","assertImportOrExportDeclaration"),v("ModuleDeclaration",e,t)}});var FT=T(Yl=>{"use strict";Object.defineProperty(Yl,"__esModule",{value:!0});Yl.default=void 0;var Zr=Pr(),qSe=Yl.default=F$;function F$(e){switch(e){case"string":return(0,Zr.stringTypeAnnotation)();case"number":return(0,Zr.numberTypeAnnotation)();case"undefined":return(0,Zr.voidTypeAnnotation)();case"boolean":return(0,Zr.booleanTypeAnnotation)();case"function":return(0,Zr.genericTypeAnnotation)((0,Zr.identifier)("Function"));case"object":return(0,Zr.genericTypeAnnotation)((0,Zr.identifier)("Object"));case"symbol":return(0,Zr.genericTypeAnnotation)((0,Zr.identifier)("Symbol"));case"bigint":return(0,Zr.anyTypeAnnotation)()}throw new Error("Invalid typeof value: "+e)}});var Od=T(_d=>{"use strict";Object.defineProperty(_d,"__esModule",{value:!0});_d.default=jT;var so=Nt();function LT(e){return(0,so.isIdentifier)(e)?e.name:`${e.id.name}.${LT(e.qualification)}`}function jT(e){let t=Array.from(e),r=new Map,n=new Map,s=new Set,i=[];for(let a=0;a<t.length;a++){let o=t[a];if(o&&!i.includes(o)){if((0,so.isAnyTypeAnnotation)(o))return[o];if((0,so.isFlowBaseAnnotation)(o)){n.set(o.type,o);continue}if((0,so.isUnionTypeAnnotation)(o)){s.has(o.types)||(t.push(...o.types),s.add(o.types));continue}if((0,so.isGenericTypeAnnotation)(o)){let l=LT(o.id);if(r.has(l)){let u=r.get(l);u.typeParameters?o.typeParameters&&(u.typeParameters.params.push(...o.typeParameters.params),u.typeParameters.params=jT(u.typeParameters.params)):u=o.typeParameters}else r.set(l,o);continue}i.push(o)}}for(let[,a]of n)i.push(a);for(let[,a]of r)i.push(a);return i}});var MT=T(Nd=>{"use strict";Object.defineProperty(Nd,"__esModule",{value:!0});Nd.default=M$;var L$=Pr(),j$=Od();function M$(e){let t=(0,j$.default)(e);return t.length===1?t[0]:(0,L$.unionTypeAnnotation)(t)}});var UT=T(Bd=>{"use strict";Object.defineProperty(Bd,"__esModule",{value:!0});Bd.default=qT;var Hi=Nt();function RT(e){return(0,Hi.isIdentifier)(e)?e.name:(0,Hi.isThisExpression)(e)?"this":`${e.right.name}.${RT(e.left)}`}function qT(e){let t=Array.from(e),r=new Map,n=new Map,s=new Set,i=[];for(let a=0;a<t.length;a++){let o=t[a];if(!o||i.includes(o))continue;if((0,Hi.isTSAnyKeyword)(o))return[o];if((0,Hi.isTSBaseType)(o)){n.set(o.type,o);continue}if((0,Hi.isTSUnionType)(o)){s.has(o.types)||(t.push(...o.types),s.add(o.types));continue}let l="typeParameters";if((0,Hi.isTSTypeReference)(o)&&o[l]){let u=o[l],c=RT(o.typeName);if(r.has(c)){let p=r.get(c),d=p[l];d?(d.params.push(...u.params),d.params=qT(d.params)):p=u}else r.set(c,o);continue}i.push(o)}for(let[,a]of n)i.push(a);for(let[,a]of r)i.push(a);return i}});var VT=T(kd=>{"use strict";Object.defineProperty(kd,"__esModule",{value:!0});kd.default=V$;var R$=Pr(),q$=UT(),U$=Nt();function V$(e){let t=e.map(n=>(0,U$.isTSTypeAnnotation)(n)?n.typeAnnotation:n),r=(0,q$.default)(t);return r.length===1?r[0]:(0,R$.tsUnionType)(r)}});var Ld=T(Fd=>{"use strict";Object.defineProperty(Fd,"__esModule",{value:!0});Fd.buildUndefinedNode=$$;var $T=Pr();function $$(){return(0,$T.unaryExpression)("void",(0,$T.numericLiteral)(0),!0)}});var gs=T(Md=>{"use strict";Object.defineProperty(Md,"__esModule",{value:!0});Md.default=W$;var WT=Fr(),KT=Nt(),{hasOwn:bn}={hasOwn:Function.call.bind(Object.prototype.hasOwnProperty)};function GT(e,t,r,n){return e&&typeof e.type=="string"?HT(e,t,r,n):e}function jd(e,t,r,n){return Array.isArray(e)?e.map(s=>GT(s,t,r,n)):GT(e,t,r,n)}function W$(e,t=!0,r=!1){return HT(e,t,r,new Map)}function HT(e,t=!0,r=!1,n){if(!e)return e;let{type:s}=e,i={type:e.type};if((0,KT.isIdentifier)(e))i.name=e.name,bn(e,"optional")&&typeof e.optional=="boolean"&&(i.optional=e.optional),bn(e,"typeAnnotation")&&(i.typeAnnotation=t?jd(e.typeAnnotation,!0,r,n):e.typeAnnotation),bn(e,"decorators")&&(i.decorators=t?jd(e.decorators,!0,r,n):e.decorators);else if(bn(WT.NODE_FIELDS,s))for(let a of Object.keys(WT.NODE_FIELDS[s]))bn(e,a)&&(t?i[a]=(0,KT.isFile)(e)&&a==="comments"?Xl(e.comments,t,r,n):jd(e[a],!0,r,n):i[a]=e[a]);else throw new Error(`Unknown node type: "${s}"`);return bn(e,"loc")&&(r?i.loc=null:i.loc=e.loc),bn(e,"leadingComments")&&(i.leadingComments=Xl(e.leadingComments,t,r,n)),bn(e,"innerComments")&&(i.innerComments=Xl(e.innerComments,t,r,n)),bn(e,"trailingComments")&&(i.trailingComments=Xl(e.trailingComments,t,r,n)),bn(e,"extra")&&(i.extra=Object.assign({},e.extra)),i}function Xl(e,t,r,n){return!e||!t?e:e.map(s=>{let i=n.get(s);if(i)return i;let{type:a,value:o,loc:l}=s,u={type:a,value:o,loc:l};return r&&(u.loc=null),n.set(s,u),u})}});var YT=T(Rd=>{"use strict";Object.defineProperty(Rd,"__esModule",{value:!0});Rd.default=G$;var K$=gs();function G$(e){return(0,K$.default)(e,!1)}});var XT=T(qd=>{"use strict";Object.defineProperty(qd,"__esModule",{value:!0});qd.default=Y$;var H$=gs();function Y$(e){return(0,H$.default)(e)}});var JT=T(Ud=>{"use strict";Object.defineProperty(Ud,"__esModule",{value:!0});Ud.default=J$;var X$=gs();function J$(e){return(0,X$.default)(e,!0,!0)}});var zT=T(Vd=>{"use strict";Object.defineProperty(Vd,"__esModule",{value:!0});Vd.default=Q$;var z$=gs();function Q$(e){return(0,z$.default)(e,!1,!0)}});var Wd=T($d=>{"use strict";Object.defineProperty($d,"__esModule",{value:!0});$d.default=Z$;function Z$(e,t,r){if(!r||!e)return e;let n=`${t}Comments`;return e[n]?t==="leading"?e[n]=r.concat(e[n]):e[n].push(...r):e[n]=r,e}});var QT=T(Kd=>{"use strict";Object.defineProperty(Kd,"__esModule",{value:!0});Kd.default=tW;var eW=Wd();function tW(e,t,r,n){return(0,eW.default)(e,t,[{type:n?"CommentLine":"CommentBlock",value:r}])}});var Jl=T(Gd=>{"use strict";Object.defineProperty(Gd,"__esModule",{value:!0});Gd.default=rW;function rW(e,t,r){t&&r&&(t[e]=Array.from(new Set([].concat(t[e],r[e]).filter(Boolean))))}});var Yd=T(Hd=>{"use strict";Object.defineProperty(Hd,"__esModule",{value:!0});Hd.default=sW;var nW=Jl();function sW(e,t){(0,nW.default)("innerComments",e,t)}});var Jd=T(Xd=>{"use strict";Object.defineProperty(Xd,"__esModule",{value:!0});Xd.default=aW;var iW=Jl();function aW(e,t){(0,iW.default)("leadingComments",e,t)}});var Qd=T(zd=>{"use strict";Object.defineProperty(zd,"__esModule",{value:!0});zd.default=lW;var oW=Jl();function lW(e,t){(0,oW.default)("trailingComments",e,t)}});var eh=T(Zd=>{"use strict";Object.defineProperty(Zd,"__esModule",{value:!0});Zd.default=fW;var uW=Qd(),cW=Jd(),pW=Yd();function fW(e,t){return(0,uW.default)(e,t),(0,cW.default)(e,t),(0,pW.default)(e,t),e}});var ZT=T(th=>{"use strict";Object.defineProperty(th,"__esModule",{value:!0});th.default=hW;var dW=hs();function hW(e){return dW.COMMENT_KEYS.forEach(t=>{e[t]=null}),e}});var eS=T(z=>{"use strict";Object.defineProperty(z,"__esModule",{value:!0});z.WHILE_TYPES=z.USERWHITESPACABLE_TYPES=z.UNARYLIKE_TYPES=z.TYPESCRIPT_TYPES=z.TSTYPE_TYPES=z.TSTYPEELEMENT_TYPES=z.TSENTITYNAME_TYPES=z.TSBASETYPE_TYPES=z.TERMINATORLESS_TYPES=z.STATEMENT_TYPES=z.STANDARDIZED_TYPES=z.SCOPABLE_TYPES=z.PUREISH_TYPES=z.PROPERTY_TYPES=z.PRIVATE_TYPES=z.PATTERN_TYPES=z.PATTERNLIKE_TYPES=z.OBJECTMEMBER_TYPES=z.MODULESPECIFIER_TYPES=z.MODULEDECLARATION_TYPES=z.MISCELLANEOUS_TYPES=z.METHOD_TYPES=z.LVAL_TYPES=z.LOOP_TYPES=z.LITERAL_TYPES=z.JSX_TYPES=z.IMPORTOREXPORTDECLARATION_TYPES=z.IMMUTABLE_TYPES=z.FUNCTION_TYPES=z.FUNCTIONPARENT_TYPES=z.FOR_TYPES=z.FORXSTATEMENT_TYPES=z.FLOW_TYPES=z.FLOWTYPE_TYPES=z.FLOWPREDICATE_TYPES=z.FLOWDECLARATION_TYPES=z.FLOWBASEANNOTATION_TYPES=z.EXPRESSION_TYPES=z.EXPRESSIONWRAPPER_TYPES=z.EXPORTDECLARATION_TYPES=z.ENUMMEMBER_TYPES=z.ENUMBODY_TYPES=z.DECLARATION_TYPES=z.CONDITIONAL_TYPES=z.COMPLETIONSTATEMENT_TYPES=z.CLASS_TYPES=z.BLOCK_TYPES=z.BLOCKPARENT_TYPES=z.BINARY_TYPES=z.ACCESSOR_TYPES=void 0;var Be=Fr(),axe=z.STANDARDIZED_TYPES=Be.FLIPPED_ALIAS_KEYS.Standardized,oxe=z.EXPRESSION_TYPES=Be.FLIPPED_ALIAS_KEYS.Expression,lxe=z.BINARY_TYPES=Be.FLIPPED_ALIAS_KEYS.Binary,uxe=z.SCOPABLE_TYPES=Be.FLIPPED_ALIAS_KEYS.Scopable,cxe=z.BLOCKPARENT_TYPES=Be.FLIPPED_ALIAS_KEYS.BlockParent,pxe=z.BLOCK_TYPES=Be.FLIPPED_ALIAS_KEYS.Block,fxe=z.STATEMENT_TYPES=Be.FLIPPED_ALIAS_KEYS.Statement,dxe=z.TERMINATORLESS_TYPES=Be.FLIPPED_ALIAS_KEYS.Terminatorless,hxe=z.COMPLETIONSTATEMENT_TYPES=Be.FLIPPED_ALIAS_KEYS.CompletionStatement,mxe=z.CONDITIONAL_TYPES=Be.FLIPPED_ALIAS_KEYS.Conditional,yxe=z.LOOP_TYPES=Be.FLIPPED_ALIAS_KEYS.Loop,gxe=z.WHILE_TYPES=Be.FLIPPED_ALIAS_KEYS.While,bxe=z.EXPRESSIONWRAPPER_TYPES=Be.FLIPPED_ALIAS_KEYS.ExpressionWrapper,Exe=z.FOR_TYPES=Be.FLIPPED_ALIAS_KEYS.For,Txe=z.FORXSTATEMENT_TYPES=Be.FLIPPED_ALIAS_KEYS.ForXStatement,Sxe=z.FUNCTION_TYPES=Be.FLIPPED_ALIAS_KEYS.Function,xxe=z.FUNCTIONPARENT_TYPES=Be.FLIPPED_ALIAS_KEYS.FunctionParent,vxe=z.PUREISH_TYPES=Be.FLIPPED_ALIAS_KEYS.Pureish,Pxe=z.DECLARATION_TYPES=Be.FLIPPED_ALIAS_KEYS.Declaration,Axe=z.PATTERNLIKE_TYPES=Be.FLIPPED_ALIAS_KEYS.PatternLike,Cxe=z.LVAL_TYPES=Be.FLIPPED_ALIAS_KEYS.LVal,Dxe=z.TSENTITYNAME_TYPES=Be.FLIPPED_ALIAS_KEYS.TSEntityName,wxe=z.LITERAL_TYPES=Be.FLIPPED_ALIAS_KEYS.Literal,Ixe=z.IMMUTABLE_TYPES=Be.FLIPPED_ALIAS_KEYS.Immutable,_xe=z.USERWHITESPACABLE_TYPES=Be.FLIPPED_ALIAS_KEYS.UserWhitespacable,Oxe=z.METHOD_TYPES=Be.FLIPPED_ALIAS_KEYS.Method,Nxe=z.OBJECTMEMBER_TYPES=Be.FLIPPED_ALIAS_KEYS.ObjectMember,Bxe=z.PROPERTY_TYPES=Be.FLIPPED_ALIAS_KEYS.Property,kxe=z.UNARYLIKE_TYPES=Be.FLIPPED_ALIAS_KEYS.UnaryLike,Fxe=z.PATTERN_TYPES=Be.FLIPPED_ALIAS_KEYS.Pattern,Lxe=z.CLASS_TYPES=Be.FLIPPED_ALIAS_KEYS.Class,mW=z.IMPORTOREXPORTDECLARATION_TYPES=Be.FLIPPED_ALIAS_KEYS.ImportOrExportDeclaration,jxe=z.EXPORTDECLARATION_TYPES=Be.FLIPPED_ALIAS_KEYS.ExportDeclaration,Mxe=z.MODULESPECIFIER_TYPES=Be.FLIPPED_ALIAS_KEYS.ModuleSpecifier,Rxe=z.ACCESSOR_TYPES=Be.FLIPPED_ALIAS_KEYS.Accessor,qxe=z.PRIVATE_TYPES=Be.FLIPPED_ALIAS_KEYS.Private,Uxe=z.FLOW_TYPES=Be.FLIPPED_ALIAS_KEYS.Flow,Vxe=z.FLOWTYPE_TYPES=Be.FLIPPED_ALIAS_KEYS.FlowType,$xe=z.FLOWBASEANNOTATION_TYPES=Be.FLIPPED_ALIAS_KEYS.FlowBaseAnnotation,Wxe=z.FLOWDECLARATION_TYPES=Be.FLIPPED_ALIAS_KEYS.FlowDeclaration,Kxe=z.FLOWPREDICATE_TYPES=Be.FLIPPED_ALIAS_KEYS.FlowPredicate,Gxe=z.ENUMBODY_TYPES=Be.FLIPPED_ALIAS_KEYS.EnumBody,Hxe=z.ENUMMEMBER_TYPES=Be.FLIPPED_ALIAS_KEYS.EnumMember,Yxe=z.JSX_TYPES=Be.FLIPPED_ALIAS_KEYS.JSX,Xxe=z.MISCELLANEOUS_TYPES=Be.FLIPPED_ALIAS_KEYS.Miscellaneous,Jxe=z.TYPESCRIPT_TYPES=Be.FLIPPED_ALIAS_KEYS.TypeScript,zxe=z.TSTYPEELEMENT_TYPES=Be.FLIPPED_ALIAS_KEYS.TSTypeElement,Qxe=z.TSTYPE_TYPES=Be.FLIPPED_ALIAS_KEYS.TSType,Zxe=z.TSBASETYPE_TYPES=Be.FLIPPED_ALIAS_KEYS.TSBaseType,eve=z.MODULEDECLARATION_TYPES=mW});var sh=T(nh=>{"use strict";Object.defineProperty(nh,"__esModule",{value:!0});nh.default=yW;var zl=Nt(),rh=Pr();function yW(e,t){if((0,zl.isBlockStatement)(e))return e;let r=[];return(0,zl.isEmptyStatement)(e)?r=[]:((0,zl.isStatement)(e)||((0,zl.isFunction)(t)?e=(0,rh.returnStatement)(e):e=(0,rh.expressionStatement)(e)),r=[e]),(0,rh.blockStatement)(r)}});var tS=T(ih=>{"use strict";Object.defineProperty(ih,"__esModule",{value:!0});ih.default=bW;var gW=sh();function bW(e,t="body"){let r=(0,gW.default)(e[t],e);return e[t]=r,r}});var oh=T(ah=>{"use strict";Object.defineProperty(ah,"__esModule",{value:!0});ah.default=SW;var EW=$i(),TW=Vi();function SW(e){e=e+"";let t="";for(let r of e)t+=(0,TW.isIdentifierChar)(r.codePointAt(0))?r:"-";return t=t.replace(/^[-0-9]+/,""),t=t.replace(/[-\s]+(.)?/g,function(r,n){return n?n.toUpperCase():""}),(0,EW.default)(t)||(t=`_${t}`),t||"_"}});var rS=T(lh=>{"use strict";Object.defineProperty(lh,"__esModule",{value:!0});lh.default=vW;var xW=oh();function vW(e){return e=(0,xW.default)(e),(e==="eval"||e==="arguments")&&(e="_"+e),e}});var nS=T(uh=>{"use strict";Object.defineProperty(uh,"__esModule",{value:!0});uh.default=CW;var PW=Nt(),AW=Pr();function CW(e,t=e.key||e.property){return!e.computed&&(0,PW.isIdentifier)(t)&&(t=(0,AW.stringLiteral)(t.name)),t}});var sS=T(Ql=>{"use strict";Object.defineProperty(Ql,"__esModule",{value:!0});Ql.default=void 0;var io=Nt(),ove=Ql.default=DW;function DW(e){if((0,io.isExpressionStatement)(e)&&(e=e.expression),(0,io.isExpression)(e))return e;if((0,io.isClass)(e)?e.type="ClassExpression":(0,io.isFunction)(e)&&(e.type="FunctionExpression"),!(0,io.isExpression)(e))throw new Error(`cannot turn ${e.type} to an expression`);return e}});var fh=T(ph=>{"use strict";Object.defineProperty(ph,"__esModule",{value:!0});ph.default=ch;var wW=Fr();function ch(e,t,r){if(!e)return;let n=wW.VISITOR_KEYS[e.type];if(n){r=r||{},t(e,r);for(let s of n){let i=e[s];if(Array.isArray(i))for(let a of i)ch(a,t,r);else ch(i,t,r)}}}});var hh=T(dh=>{"use strict";Object.defineProperty(dh,"__esModule",{value:!0});dh.default=OW;var IW=hs(),iS=["tokens","start","end","loc","raw","rawValue"],_W=[...IW.COMMENT_KEYS,"comments",...iS];function OW(e,t={}){let r=t.preserveComments?iS:_W;for(let s of r)e[s]!=null&&(e[s]=void 0);for(let s of Object.keys(e))s[0]==="_"&&e[s]!=null&&(e[s]=void 0);let n=Object.getOwnPropertySymbols(e);for(let s of n)e[s]=null}});var yh=T(mh=>{"use strict";Object.defineProperty(mh,"__esModule",{value:!0});mh.default=kW;var NW=fh(),BW=hh();function kW(e,t){return(0,NW.default)(e,BW.default,t),e}});var oS=T(gh=>{"use strict";Object.defineProperty(gh,"__esModule",{value:!0});gh.default=ei;var aS=Nt(),FW=gs(),LW=yh();function ei(e,t=e.key){let r;return e.kind==="method"?ei.increment()+"":((0,aS.isIdentifier)(t)?r=t.name:(0,aS.isStringLiteral)(t)?r=JSON.stringify(t.value):r=JSON.stringify((0,LW.default)((0,FW.default)(t))),e.computed&&(r=`[${r}]`),e.static&&(r=`static:${r}`),r)}ei.uid=0;ei.increment=function(){return ei.uid>=Number.MAX_SAFE_INTEGER?ei.uid=0:ei.uid++}});var lS=T(eu=>{"use strict";Object.defineProperty(eu,"__esModule",{value:!0});eu.default=void 0;var Zl=Nt(),jW=Pr(),dve=eu.default=MW;function MW(e,t){if((0,Zl.isStatement)(e))return e;let r=!1,n;if((0,Zl.isClass)(e))r=!0,n="ClassDeclaration";else if((0,Zl.isFunction)(e))r=!0,n="FunctionDeclaration";else if((0,Zl.isAssignmentExpression)(e))return(0,jW.expressionStatement)(e);if(r&&!e.id&&(n=!1),!n){if(t)return!1;throw new Error(`cannot turn ${e.type} to a statement`)}return e.type=n,e}});var uS=T(tu=>{"use strict";Object.defineProperty(tu,"__esModule",{value:!0});tu.default=void 0;var RW=$i(),ar=Pr(),mve=tu.default=bh,qW=Function.call.bind(Object.prototype.toString);function UW(e){return qW(e)==="[object RegExp]"}function VW(e){if(typeof e!="object"||e===null||Object.prototype.toString.call(e)!=="[object Object]")return!1;let t=Object.getPrototypeOf(e);return t===null||Object.getPrototypeOf(t)===null}function bh(e){if(e===void 0)return(0,ar.identifier)("undefined");if(e===!0||e===!1)return(0,ar.booleanLiteral)(e);if(e===null)return(0,ar.nullLiteral)();if(typeof e=="string")return(0,ar.stringLiteral)(e);if(typeof e=="number"){let t;if(Number.isFinite(e))t=(0,ar.numericLiteral)(Math.abs(e));else{let r;Number.isNaN(e)?r=(0,ar.numericLiteral)(0):r=(0,ar.numericLiteral)(1),t=(0,ar.binaryExpression)("/",r,(0,ar.numericLiteral)(0))}return(e<0||Object.is(e,-0))&&(t=(0,ar.unaryExpression)("-",t)),t}if(UW(e)){let t=e.source,r=/\/([a-z]*)$/.exec(e.toString())[1];return(0,ar.regExpLiteral)(t,r)}if(Array.isArray(e))return(0,ar.arrayExpression)(e.map(bh));if(VW(e)){let t=[];for(let r of Object.keys(e)){let n;(0,RW.default)(r)?n=(0,ar.identifier)(r):n=(0,ar.stringLiteral)(r),t.push((0,ar.objectProperty)(n,bh(e[r])))}return(0,ar.objectExpression)(t)}throw new Error("don't know how to turn this value into a node")}});var cS=T(Eh=>{"use strict";Object.defineProperty(Eh,"__esModule",{value:!0});Eh.default=WW;var $W=Pr();function WW(e,t,r=!1){return e.object=(0,$W.memberExpression)(e.object,e.property,e.computed),e.property=t,e.computed=!!r,e}});var fS=T(Th=>{"use strict";Object.defineProperty(Th,"__esModule",{value:!0});Th.default=GW;var pS=hs(),KW=eh();function GW(e,t){if(!e||!t)return e;for(let r of pS.INHERIT_KEYS.optional)e[r]==null&&(e[r]=t[r]);for(let r of Object.keys(t))r[0]==="_"&&r!=="__clone"&&(e[r]=t[r]);for(let r of pS.INHERIT_KEYS.force)e[r]=t[r];return(0,KW.default)(e,t),e}});var dS=T(Sh=>{"use strict";Object.defineProperty(Sh,"__esModule",{value:!0});Sh.default=XW;var HW=Pr(),YW=Ne();function XW(e,t){if((0,YW.isSuper)(e.object))throw new Error("Cannot prepend node to super property access (`super.foo`).");return e.object=(0,HW.memberExpression)(t,e.object),e}});var hS=T(xh=>{"use strict";Object.defineProperty(xh,"__esModule",{value:!0});xh.default=JW;function JW(e){let t=[].concat(e),r=Object.create(null);for(;t.length;){let n=t.pop();if(n)switch(n.type){case"ArrayPattern":t.push(...n.elements);break;case"AssignmentExpression":case"AssignmentPattern":case"ForInStatement":case"ForOfStatement":t.push(n.left);break;case"ObjectPattern":t.push(...n.properties);break;case"ObjectProperty":t.push(n.value);break;case"RestElement":case"UpdateExpression":t.push(n.argument);break;case"UnaryExpression":n.operator==="delete"&&t.push(n.argument);break;case"Identifier":r[n.name]=n;break;default:break}}return r}});var ao=T(Ph=>{"use strict";Object.defineProperty(Ph,"__esModule",{value:!0});Ph.default=vh;var kn=Nt();function vh(e,t,r,n){let s=[].concat(e),i=Object.create(null);for(;s.length;){let a=s.shift();if(!a||n&&((0,kn.isAssignmentExpression)(a)||(0,kn.isUnaryExpression)(a)||(0,kn.isUpdateExpression)(a)))continue;if((0,kn.isIdentifier)(a)){t?(i[a.name]=i[a.name]||[]).push(a):i[a.name]=a;continue}if((0,kn.isExportDeclaration)(a)&&!(0,kn.isExportAllDeclaration)(a)){(0,kn.isDeclaration)(a.declaration)&&s.push(a.declaration);continue}if(r){if((0,kn.isFunctionDeclaration)(a)){s.push(a.id);continue}if((0,kn.isFunctionExpression)(a))continue}let o=vh.keys[a.type];if(o)for(let l=0;l<o.length;l++){let u=o[l],c=a[u];c&&(Array.isArray(c)?s.push(...c):s.push(c))}}return i}var zW={DeclareClass:["id"],DeclareFunction:["id"],DeclareModule:["id"],DeclareVariable:["id"],DeclareInterface:["id"],DeclareTypeAlias:["id"],DeclareOpaqueType:["id"],InterfaceDeclaration:["id"],TypeAlias:["id"],OpaqueType:["id"],CatchClause:["param"],LabeledStatement:["label"],UnaryExpression:["argument"],AssignmentExpression:["left"],ImportSpecifier:["local"],ImportNamespaceSpecifier:["local"],ImportDefaultSpecifier:["local"],ImportDeclaration:["specifiers"],TSImportEqualsDeclaration:["id"],ExportSpecifier:["exported"],ExportNamespaceSpecifier:["exported"],ExportDefaultSpecifier:["exported"],FunctionDeclaration:["id","params"],FunctionExpression:["id","params"],ArrowFunctionExpression:["params"],ObjectMethod:["params"],ClassMethod:["params"],ClassPrivateMethod:["params"],ForInStatement:["left"],ForOfStatement:["left"],ClassDeclaration:["id"],ClassExpression:["id"],RestElement:["argument"],UpdateExpression:["argument"],ObjectProperty:["value"],AssignmentPattern:["left"],ArrayPattern:["elements"],ObjectPattern:["properties"],VariableDeclaration:["declarations"],VariableDeclarator:["id"]};vh.keys=zW});var mS=T(ru=>{"use strict";Object.defineProperty(ru,"__esModule",{value:!0});ru.default=void 0;var QW=ao(),xve=ru.default=ZW;function ZW(e,t){return(0,QW.default)(e,t,!0)}});var gS=T(Ah=>{"use strict";Object.defineProperty(Ah,"__esModule",{value:!0});Ah.default=tK;var jr=Nt();function eK(e){return(0,jr.isNullLiteral)(e)?"null":(0,jr.isRegExpLiteral)(e)?`/${e.pattern}/${e.flags}`:(0,jr.isTemplateLiteral)(e)?e.quasis.map(t=>t.value.raw).join(""):e.value!==void 0?String(e.value):null}function yS(e){if(!e.computed||(0,jr.isLiteral)(e.key))return e.key}function tK(e,t){if("id"in e&&e.id)return{name:e.id.name,originalNode:e.id};let r="",n;if((0,jr.isObjectProperty)(t,{value:e})?n=yS(t):(0,jr.isObjectMethod)(e)||(0,jr.isClassMethod)(e)?(n=yS(e),e.kind==="get"?r="get ":e.kind==="set"&&(r="set ")):(0,jr.isVariableDeclarator)(t,{init:e})?n=t.id:(0,jr.isAssignmentExpression)(t,{operator:"=",right:e})&&(n=t.left),!n)return null;let s=(0,jr.isLiteral)(n)?eK(n):(0,jr.isIdentifier)(n)?n.name:(0,jr.isPrivateName)(n)?n.id.name:null;return s==null?null:{name:r+s,originalNode:n}}});var bS=T(Dh=>{"use strict";Object.defineProperty(Dh,"__esModule",{value:!0});Dh.default=nK;var rK=Fr();function nK(e,t,r){typeof t=="function"&&(t={enter:t});let{enter:n,exit:s}=t;Ch(e,n,s,r,[])}function Ch(e,t,r,n,s){let i=rK.VISITOR_KEYS[e.type];if(i){t&&t(e,s,n);for(let a of i){let o=e[a];if(Array.isArray(o))for(let l=0;l<o.length;l++){let u=o[l];u&&(s.push({node:e,key:a,index:l}),Ch(u,t,r,n,s),s.pop())}else o&&(s.push({node:e,key:a}),Ch(o,t,r,n,s),s.pop())}r&&r(e,s,n)}}});var ES=T(wh=>{"use strict";Object.defineProperty(wh,"__esModule",{value:!0});wh.default=iK;var sK=ao();function iK(e,t,r){if(r&&e.type==="Identifier"&&t.type==="ObjectProperty"&&r.type==="ObjectExpression")return!1;let n=sK.default.keys[t.type];if(n)for(let s=0;s<n.length;s++){let i=n[s],a=t[i];if(Array.isArray(a)){if(a.includes(e))return!0}else if(a===e)return!0}return!1}});var _h=T(Ih=>{"use strict";Object.defineProperty(Ih,"__esModule",{value:!0});Ih.default=lK;var aK=Nt(),oK=hs();function lK(e){return(0,aK.isVariableDeclaration)(e)&&(e.kind!=="var"||e[oK.BLOCK_SCOPED_SYMBOL])}});var SS=T(Oh=>{"use strict";Object.defineProperty(Oh,"__esModule",{value:!0});Oh.default=cK;var TS=Nt(),uK=_h();function cK(e){return(0,TS.isFunctionDeclaration)(e)||(0,TS.isClassDeclaration)(e)||(0,uK.default)(e)}});var xS=T(Nh=>{"use strict";Object.defineProperty(Nh,"__esModule",{value:!0});Nh.default=dK;var pK=jl(),fK=Nt();function dK(e){return(0,pK.default)(e.type,"Immutable")?!0:(0,fK.isIdentifier)(e)?e.name==="undefined":!1}});var PS=T(kh=>{"use strict";Object.defineProperty(kh,"__esModule",{value:!0});kh.default=Bh;var vS=Fr();function Bh(e,t){if(typeof e!="object"||typeof t!="object"||e==null||t==null)return e===t;if(e.type!==t.type)return!1;let r=Object.keys(vS.NODE_FIELDS[e.type]||e.type),n=vS.VISITOR_KEYS[e.type];for(let s of r){let i=e[s],a=t[s];if(typeof i!=typeof a)return!1;if(!(i==null&&a==null)){if(i==null||a==null)return!1;if(Array.isArray(i)){if(!Array.isArray(a)||i.length!==a.length)return!1;for(let o=0;o<i.length;o++)if(!Bh(i[o],a[o]))return!1;continue}if(typeof i=="object"&&!(n!=null&&n.includes(s))){for(let o of Object.keys(i))if(i[o]!==a[o])return!1;continue}if(!Bh(i,a))return!1}}return!0}});var AS=T(Fh=>{"use strict";Object.defineProperty(Fh,"__esModule",{value:!0});Fh.default=hK;function hK(e,t,r){switch(t.type){case"MemberExpression":case"OptionalMemberExpression":return t.property===e?!!t.computed:t.object===e;case"JSXMemberExpression":return t.object===e;case"VariableDeclarator":return t.init===e;case"ArrowFunctionExpression":return t.body===e;case"PrivateName":return!1;case"ClassMethod":case"ClassPrivateMethod":case"ObjectMethod":return t.key===e?!!t.computed:!1;case"ObjectProperty":return t.key===e?!!t.computed:!r||r.type!=="ObjectPattern";case"ClassProperty":case"ClassAccessorProperty":return t.key===e?!!t.computed:!0;case"ClassPrivateProperty":return t.key!==e;case"ClassDeclaration":case"ClassExpression":return t.superClass===e;case"AssignmentExpression":return t.right===e;case"AssignmentPattern":return t.right===e;case"LabeledStatement":return!1;case"CatchClause":return!1;case"RestElement":return!1;case"BreakStatement":case"ContinueStatement":return!1;case"FunctionDeclaration":case"FunctionExpression":return!1;case"ExportNamespaceSpecifier":case"ExportDefaultSpecifier":return!1;case"ExportSpecifier":return r!=null&&r.source?!1:t.local===e;case"ImportDefaultSpecifier":case"ImportNamespaceSpecifier":case"ImportSpecifier":return!1;case"ImportAttribute":return!1;case"JSXAttribute":return!1;case"ObjectPattern":case"ArrayPattern":return!1;case"MetaProperty":return!1;case"ObjectTypeProperty":return t.key!==e;case"TSEnumMember":return t.id!==e;case"TSPropertySignature":return t.key===e?!!t.computed:!0}return!0}});var CS=T(Lh=>{"use strict";Object.defineProperty(Lh,"__esModule",{value:!0});Lh.default=mK;var ti=Nt();function mK(e,t){return(0,ti.isBlockStatement)(e)&&((0,ti.isFunction)(t)||(0,ti.isCatchClause)(t))?!1:(0,ti.isPattern)(e)&&((0,ti.isFunction)(t)||(0,ti.isCatchClause)(t))?!0:(0,ti.isScopable)(e)}});var wS=T(jh=>{"use strict";Object.defineProperty(jh,"__esModule",{value:!0});jh.default=yK;var DS=Nt();function yK(e){return(0,DS.isImportDefaultSpecifier)(e)||(0,DS.isIdentifier)(e.imported||e.exported,{name:"default"})}});var IS=T(Mh=>{"use strict";Object.defineProperty(Mh,"__esModule",{value:!0});Mh.default=EK;var gK=$i(),bK=new Set(["abstract","boolean","byte","char","double","enum","final","float","goto","implements","int","interface","long","native","package","private","protected","public","short","static","synchronized","throws","transient","volatile"]);function EK(e){return(0,gK.default)(e)&&!bK.has(e)}});var _S=T(Rh=>{"use strict";Object.defineProperty(Rh,"__esModule",{value:!0});Rh.default=xK;var TK=Nt(),SK=hs();function xK(e){return(0,TK.isVariableDeclaration)(e,{kind:"var"})&&!e[SK.BLOCK_SCOPED_SYMBOL]}});var OS=T(Vh=>{"use strict";Object.defineProperty(Vh,"__esModule",{value:!0});Vh.default=nu;var vK=ao(),ri=Nt(),qh=Pr(),Uh=Ld(),PK=gs();function nu(e,t){let r=[],n=!0;for(let s of e)if((0,ri.isEmptyStatement)(s)||(n=!1),(0,ri.isExpression)(s))r.push(s);else if((0,ri.isExpressionStatement)(s))r.push(s.expression);else if((0,ri.isVariableDeclaration)(s)){if(s.kind!=="var")return;for(let i of s.declarations){let a=(0,vK.default)(i);for(let o of Object.keys(a))t.push({kind:s.kind,id:(0,PK.default)(a[o])});i.init&&r.push((0,qh.assignmentExpression)("=",i.id,i.init))}n=!0}else if((0,ri.isIfStatement)(s)){let i=s.consequent?nu([s.consequent],t):(0,Uh.buildUndefinedNode)(),a=s.alternate?nu([s.alternate],t):(0,Uh.buildUndefinedNode)();if(!i||!a)return;r.push((0,qh.conditionalExpression)(s.test,i,a))}else if((0,ri.isBlockStatement)(s)){let i=nu(s.body,t);if(!i)return;r.push(i)}else if((0,ri.isEmptyStatement)(s))e.indexOf(s)===0&&(n=!0);else return;return n&&r.push((0,Uh.buildUndefinedNode)()),r.length===1?r[0]:(0,qh.sequenceExpression)(r)}});var NS=T($h=>{"use strict";Object.defineProperty($h,"__esModule",{value:!0});$h.default=CK;var AK=OS();function CK(e,t){if(!(e!=null&&e.length))return;let r=[],n=(0,AK.default)(e,r);if(n){for(let s of r)t.push(s);return n}}});var Ne=T(re=>{"use strict";Object.defineProperty(re,"__esModule",{value:!0});var bs={react:!0,assertNode:!0,createTypeAnnotationBasedOnTypeof:!0,createUnionTypeAnnotation:!0,createFlowUnionType:!0,createTSUnionType:!0,cloneNode:!0,clone:!0,cloneDeep:!0,cloneDeepWithoutLoc:!0,cloneWithoutLoc:!0,addComment:!0,addComments:!0,inheritInnerComments:!0,inheritLeadingComments:!0,inheritsComments:!0,inheritTrailingComments:!0,removeComments:!0,ensureBlock:!0,toBindingIdentifierName:!0,toBlock:!0,toComputedKey:!0,toExpression:!0,toIdentifier:!0,toKeyAlias:!0,toStatement:!0,valueToNode:!0,appendToMemberExpression:!0,inherits:!0,prependToMemberExpression:!0,removeProperties:!0,removePropertiesDeep:!0,removeTypeDuplicates:!0,getAssignmentIdentifiers:!0,getBindingIdentifiers:!0,getOuterBindingIdentifiers:!0,getFunctionName:!0,traverse:!0,traverseFast:!0,shallowEqual:!0,is:!0,isBinding:!0,isBlockScoped:!0,isImmutable:!0,isLet:!0,isNode:!0,isNodesEquivalent:!0,isPlaceholderType:!0,isReferenced:!0,isScope:!0,isSpecifierDefault:!0,isType:!0,isValidES3Identifier:!0,isValidIdentifier:!0,isVar:!0,matchesPattern:!0,validate:!0,buildMatchMemberExpression:!0,__internal__deprecationWarning:!0};Object.defineProperty(re,"__internal__deprecationWarning",{enumerable:!0,get:function(){return _G.default}});Object.defineProperty(re,"addComment",{enumerable:!0,get:function(){return MK.default}});Object.defineProperty(re,"addComments",{enumerable:!0,get:function(){return RK.default}});Object.defineProperty(re,"appendToMemberExpression",{enumerable:!0,get:function(){return eG.default}});Object.defineProperty(re,"assertNode",{enumerable:!0,get:function(){return _K.default}});Object.defineProperty(re,"buildMatchMemberExpression",{enumerable:!0,get:function(){return IG.default}});Object.defineProperty(re,"clone",{enumerable:!0,get:function(){return kK.default}});Object.defineProperty(re,"cloneDeep",{enumerable:!0,get:function(){return FK.default}});Object.defineProperty(re,"cloneDeepWithoutLoc",{enumerable:!0,get:function(){return LK.default}});Object.defineProperty(re,"cloneNode",{enumerable:!0,get:function(){return BK.default}});Object.defineProperty(re,"cloneWithoutLoc",{enumerable:!0,get:function(){return jK.default}});Object.defineProperty(re,"createFlowUnionType",{enumerable:!0,get:function(){return BS.default}});Object.defineProperty(re,"createTSUnionType",{enumerable:!0,get:function(){return NK.default}});Object.defineProperty(re,"createTypeAnnotationBasedOnTypeof",{enumerable:!0,get:function(){return OK.default}});Object.defineProperty(re,"createUnionTypeAnnotation",{enumerable:!0,get:function(){return BS.default}});Object.defineProperty(re,"ensureBlock",{enumerable:!0,get:function(){return KK.default}});Object.defineProperty(re,"getAssignmentIdentifiers",{enumerable:!0,get:function(){return aG.default}});Object.defineProperty(re,"getBindingIdentifiers",{enumerable:!0,get:function(){return oG.default}});Object.defineProperty(re,"getFunctionName",{enumerable:!0,get:function(){return uG.default}});Object.defineProperty(re,"getOuterBindingIdentifiers",{enumerable:!0,get:function(){return lG.default}});Object.defineProperty(re,"inheritInnerComments",{enumerable:!0,get:function(){return qK.default}});Object.defineProperty(re,"inheritLeadingComments",{enumerable:!0,get:function(){return UK.default}});Object.defineProperty(re,"inheritTrailingComments",{enumerable:!0,get:function(){return $K.default}});Object.defineProperty(re,"inherits",{enumerable:!0,get:function(){return tG.default}});Object.defineProperty(re,"inheritsComments",{enumerable:!0,get:function(){return VK.default}});Object.defineProperty(re,"is",{enumerable:!0,get:function(){return fG.default}});Object.defineProperty(re,"isBinding",{enumerable:!0,get:function(){return dG.default}});Object.defineProperty(re,"isBlockScoped",{enumerable:!0,get:function(){return hG.default}});Object.defineProperty(re,"isImmutable",{enumerable:!0,get:function(){return mG.default}});Object.defineProperty(re,"isLet",{enumerable:!0,get:function(){return yG.default}});Object.defineProperty(re,"isNode",{enumerable:!0,get:function(){return gG.default}});Object.defineProperty(re,"isNodesEquivalent",{enumerable:!0,get:function(){return bG.default}});Object.defineProperty(re,"isPlaceholderType",{enumerable:!0,get:function(){return EG.default}});Object.defineProperty(re,"isReferenced",{enumerable:!0,get:function(){return TG.default}});Object.defineProperty(re,"isScope",{enumerable:!0,get:function(){return SG.default}});Object.defineProperty(re,"isSpecifierDefault",{enumerable:!0,get:function(){return xG.default}});Object.defineProperty(re,"isType",{enumerable:!0,get:function(){return vG.default}});Object.defineProperty(re,"isValidES3Identifier",{enumerable:!0,get:function(){return PG.default}});Object.defineProperty(re,"isValidIdentifier",{enumerable:!0,get:function(){return AG.default}});Object.defineProperty(re,"isVar",{enumerable:!0,get:function(){return CG.default}});Object.defineProperty(re,"matchesPattern",{enumerable:!0,get:function(){return DG.default}});Object.defineProperty(re,"prependToMemberExpression",{enumerable:!0,get:function(){return rG.default}});re.react=void 0;Object.defineProperty(re,"removeComments",{enumerable:!0,get:function(){return WK.default}});Object.defineProperty(re,"removeProperties",{enumerable:!0,get:function(){return nG.default}});Object.defineProperty(re,"removePropertiesDeep",{enumerable:!0,get:function(){return sG.default}});Object.defineProperty(re,"removeTypeDuplicates",{enumerable:!0,get:function(){return iG.default}});Object.defineProperty(re,"shallowEqual",{enumerable:!0,get:function(){return pG.default}});Object.defineProperty(re,"toBindingIdentifierName",{enumerable:!0,get:function(){return GK.default}});Object.defineProperty(re,"toBlock",{enumerable:!0,get:function(){return HK.default}});Object.defineProperty(re,"toComputedKey",{enumerable:!0,get:function(){return YK.default}});Object.defineProperty(re,"toExpression",{enumerable:!0,get:function(){return XK.default}});Object.defineProperty(re,"toIdentifier",{enumerable:!0,get:function(){return JK.default}});Object.defineProperty(re,"toKeyAlias",{enumerable:!0,get:function(){return zK.default}});Object.defineProperty(re,"toStatement",{enumerable:!0,get:function(){return QK.default}});Object.defineProperty(re,"traverse",{enumerable:!0,get:function(){return su.default}});Object.defineProperty(re,"traverseFast",{enumerable:!0,get:function(){return cG.default}});Object.defineProperty(re,"validate",{enumerable:!0,get:function(){return wG.default}});Object.defineProperty(re,"valueToNode",{enumerable:!0,get:function(){return ZK.default}});var DK=RE(),wK=qE(),IK=NT(),_K=BT(),Wh=kT();Object.keys(Wh).forEach(function(e){e==="default"||e==="__esModule"||Object.prototype.hasOwnProperty.call(bs,e)||e in re&&re[e]===Wh[e]||Object.defineProperty(re,e,{enumerable:!0,get:function(){return Wh[e]}})});var OK=FT(),BS=MT(),NK=VT(),Kh=Ld();Object.keys(Kh).forEach(function(e){e==="default"||e==="__esModule"||Object.prototype.hasOwnProperty.call(bs,e)||e in re&&re[e]===Kh[e]||Object.defineProperty(re,e,{enumerable:!0,get:function(){return Kh[e]}})});var Gh=Pr();Object.keys(Gh).forEach(function(e){e==="default"||e==="__esModule"||Object.prototype.hasOwnProperty.call(bs,e)||e in re&&re[e]===Gh[e]||Object.defineProperty(re,e,{enumerable:!0,get:function(){return Gh[e]}})});var BK=gs(),kK=YT(),FK=XT(),LK=JT(),jK=zT(),MK=QT(),RK=Wd(),qK=Yd(),UK=Jd(),VK=eh(),$K=Qd(),WK=ZT(),Hh=eS();Object.keys(Hh).forEach(function(e){e==="default"||e==="__esModule"||Object.prototype.hasOwnProperty.call(bs,e)||e in re&&re[e]===Hh[e]||Object.defineProperty(re,e,{enumerable:!0,get:function(){return Hh[e]}})});var Yh=hs();Object.keys(Yh).forEach(function(e){e==="default"||e==="__esModule"||Object.prototype.hasOwnProperty.call(bs,e)||e in re&&re[e]===Yh[e]||Object.defineProperty(re,e,{enumerable:!0,get:function(){return Yh[e]}})});var KK=tS(),GK=rS(),HK=sh(),YK=nS(),XK=sS(),JK=oh(),zK=oS(),QK=lS(),ZK=uS(),Xh=Fr();Object.keys(Xh).forEach(function(e){e==="default"||e==="__esModule"||Object.prototype.hasOwnProperty.call(bs,e)||e in re&&re[e]===Xh[e]||Object.defineProperty(re,e,{enumerable:!0,get:function(){return Xh[e]}})});var eG=cS(),tG=fS(),rG=dS(),nG=hh(),sG=yh(),iG=Od(),aG=hS(),oG=ao(),lG=mS(),uG=gS(),su=bS();Object.keys(su).forEach(function(e){e==="default"||e==="__esModule"||Object.prototype.hasOwnProperty.call(bs,e)||e in re&&re[e]===su[e]||Object.defineProperty(re,e,{enumerable:!0,get:function(){return su[e]}})});var cG=fh(),pG=Fl(),fG=Ui(),dG=ES(),hG=SS(),mG=xS(),yG=_h(),gG=wd(),bG=PS(),EG=Zf(),TG=AS(),SG=CS(),xG=wS(),vG=jl(),PG=IS(),AG=$i(),CG=_S(),DG=Hf(),wG=Vl(),IG=Xf(),Jh=Nt();Object.keys(Jh).forEach(function(e){e==="default"||e==="__esModule"||Object.prototype.hasOwnProperty.call(bs,e)||e in re&&re[e]===Jh[e]||Object.defineProperty(re,e,{enumerable:!0,get:function(){return Jh[e]}})});var _G=Ya(),OG=NS(),Mve=re.react={isReactComponent:DK.default,isCompatTag:wK.default,buildChildren:IK.default};re.toSequenceExpression=OG.default;process.env.BABEL_TYPES_8_BREAKING&&console.warn("BABEL_TYPES_8_BREAKING is not supported anymore. Use the latest Babel 8.0.0 pre-release instead!")});var kS=T(Mr=>{"use strict";Object.defineProperty(Mr,"__esModule",{value:!0});Mr.statements=Mr.statement=Mr.smart=Mr.program=Mr.expression=void 0;var NG=Ne(),{assertExpressionStatement:BG}=NG;function zh(e){return{code:t=>`/* @babel/template */;
${t}`,validate:()=>{},unwrap:t=>e(t.program.body.slice(1))}}var qve=Mr.smart=zh(e=>e.length>1?e:e[0]),Uve=Mr.statements=zh(e=>e),Vve=Mr.statement=zh(e=>{if(e.length===0)throw new Error("Found nothing to return.");if(e.length>1)throw new Error("Found multiple statements but wanted one");return e[0]}),kG=Mr.expression={code:e=>`(
${e}
)`,validate:e=>{if(e.program.body.length>1)throw new Error("Found multiple statements but wanted one");if(kG.unwrap(e).start===0)throw new Error("Parse result included parens.")},unwrap:({program:e})=>{let[t]=e.body;return BG(t),t.expression}},$ve=Mr.program={code:e=>e,validate:()=>{},unwrap:e=>e.program}});var iu=T(oo=>{"use strict";Object.defineProperty(oo,"__esModule",{value:!0});oo.merge=jG;oo.normalizeReplacements=RG;oo.validate=MG;var FG=["placeholderWhitelist","placeholderPattern","preserveComments","syntacticPlaceholders"];function LG(e,t){if(e==null)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.includes(n))continue;r[n]=e[n]}return r}function jG(e,t){let{placeholderWhitelist:r=e.placeholderWhitelist,placeholderPattern:n=e.placeholderPattern,preserveComments:s=e.preserveComments,syntacticPlaceholders:i=e.syntacticPlaceholders}=t;return{parser:Object.assign({},e.parser,t.parser),placeholderWhitelist:r,placeholderPattern:n,preserveComments:s,syntacticPlaceholders:i}}function MG(e){if(e!=null&&typeof e!="object")throw new Error("Unknown template options.");let t=e||{},{placeholderWhitelist:r,placeholderPattern:n,preserveComments:s,syntacticPlaceholders:i}=t,a=LG(t,FG);if(r!=null&&!(r instanceof Set))throw new Error("'.placeholderWhitelist' must be a Set, null, or undefined");if(n!=null&&!(n instanceof RegExp)&&n!==!1)throw new Error("'.placeholderPattern' must be a RegExp, false, null, or undefined");if(s!=null&&typeof s!="boolean")throw new Error("'.preserveComments' must be a boolean, null, or undefined");if(i!=null&&typeof i!="boolean")throw new Error("'.syntacticPlaceholders' must be a boolean, null, or undefined");if(i===!0&&(r!=null||n!=null))throw new Error("'.placeholderWhitelist' and '.placeholderPattern' aren't compatible with '.syntacticPlaceholders: true'");return{parser:a,placeholderWhitelist:r||void 0,placeholderPattern:n==null?void 0:n,preserveComments:s==null?void 0:s,syntacticPlaceholders:i==null?void 0:i}}function RG(e){if(Array.isArray(e))return e.reduce((t,r,n)=>(t["$"+n]=r,t),{});if(typeof e=="object"||e==null)return e||void 0;throw new Error("Template replacements must be an array, object, null, or undefined")}});var bo=T(go=>{"use strict";Object.defineProperty(go,"__esModule",{value:!0});function qG(e,t){if(e==null)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.indexOf(n)!==-1)continue;r[n]=e[n]}return r}var Tn=class{constructor(t,r,n){this.line=void 0,this.column=void 0,this.index=void 0,this.line=t,this.column=r,this.index=n}},Qi=class{constructor(t,r){this.start=void 0,this.end=void 0,this.filename=void 0,this.identifierName=void 0,this.start=t,this.end=r}};function gr(e,t){let{line:r,column:n,index:s}=e;return new Tn(r,n+t,s+t)}var FS="BABEL_PARSER_SOURCETYPE_MODULE_REQUIRED",UG={ImportMetaOutsideModule:{message:`import.meta may appear only with 'sourceType: "module"'`,code:FS},ImportOutsideModule:{message:`'import' and 'export' may appear only with 'sourceType: "module"'`,code:FS}},LS={ArrayPattern:"array destructuring pattern",AssignmentExpression:"assignment expression",AssignmentPattern:"assignment expression",ArrowFunctionExpression:"arrow function expression",ConditionalExpression:"conditional expression",CatchClause:"catch clause",ForOfStatement:"for-of statement",ForInStatement:"for-in statement",ForStatement:"for-loop",FormalParameters:"function parameter list",Identifier:"identifier",ImportSpecifier:"import specifier",ImportDefaultSpecifier:"import default specifier",ImportNamespaceSpecifier:"import namespace specifier",ObjectPattern:"object destructuring pattern",ParenthesizedExpression:"parenthesized expression",RestElement:"rest element",UpdateExpression:{true:"prefix operation",false:"postfix operation"},VariableDeclarator:"variable declaration",YieldExpression:"yield expression"},lu=e=>e.type==="UpdateExpression"?LS.UpdateExpression[`${e.prefix}`]:LS[e.type],VG={AccessorIsGenerator:({kind:e})=>`A ${e}ter cannot be a generator.`,ArgumentsInClass:"'arguments' is only allowed in functions and class methods.",AsyncFunctionInSingleStatementContext:"Async functions can only be declared at the top level or inside a block.",AwaitBindingIdentifier:"Can not use 'await' as identifier inside an async function.",AwaitBindingIdentifierInStaticBlock:"Can not use 'await' as identifier inside a static block.",AwaitExpressionFormalParameter:"'await' is not allowed in async function parameters.",AwaitUsingNotInAsyncContext:"'await using' is only allowed within async functions and at the top levels of modules.",AwaitNotInAsyncContext:"'await' is only allowed within async functions and at the top levels of modules.",AwaitNotInAsyncFunction:"'await' is only allowed within async functions.",BadGetterArity:"A 'get' accessor must not have any formal parameters.",BadSetterArity:"A 'set' accessor must have exactly one formal parameter.",BadSetterRestParameter:"A 'set' accessor function argument must not be a rest parameter.",ConstructorClassField:"Classes may not have a field named 'constructor'.",ConstructorClassPrivateField:"Classes may not have a private field named '#constructor'.",ConstructorIsAccessor:"Class constructor may not be an accessor.",ConstructorIsAsync:"Constructor can't be an async function.",ConstructorIsGenerator:"Constructor can't be a generator.",DeclarationMissingInitializer:({kind:e})=>`Missing initializer in ${e} declaration.`,DecoratorArgumentsOutsideParentheses:"Decorator arguments must be moved inside parentheses: use '@(decorator(args))' instead of '@(decorator)(args)'.",DecoratorBeforeExport:"Decorators must be placed *before* the 'export' keyword. Remove the 'decoratorsBeforeExport: true' option to use the 'export @decorator class {}' syntax.",DecoratorsBeforeAfterExport:"Decorators can be placed *either* before or after the 'export' keyword, but not in both locations at the same time.",DecoratorConstructor:"Decorators can't be used with a constructor. Did you mean '@dec class { ... }'?",DecoratorExportClass:"Decorators must be placed *after* the 'export' keyword. Remove the 'decoratorsBeforeExport: false' option to use the '@decorator export class {}' syntax.",DecoratorSemicolon:"Decorators must not be followed by a semicolon.",DecoratorStaticBlock:"Decorators can't be used with a static block.",DeferImportRequiresNamespace:'Only `import defer * as x from "./module"` is valid.',DeletePrivateField:"Deleting a private field is not allowed.",DestructureNamedImport:"ES2015 named imports do not destructure. Use another statement for destructuring after the import.",DuplicateConstructor:"Duplicate constructor in the same class.",DuplicateDefaultExport:"Only one default export allowed per module.",DuplicateExport:({exportName:e})=>`\`${e}\` has already been exported. Exported identifiers must be unique.`,DuplicateProto:"Redefinition of __proto__ property.",DuplicateRegExpFlags:"Duplicate regular expression flag.",DynamicImportPhaseRequiresImportExpressions:({phase:e})=>`'import.${e}(...)' can only be parsed when using the 'createImportExpressions' option.`,ElementAfterRest:"Rest element must be last element.",EscapedCharNotAnIdentifier:"Invalid Unicode escape.",ExportBindingIsString:({localName:e,exportName:t})=>`A string literal cannot be used as an exported binding without \`from\`.
- Did you mean \`export { '${e}' as '${t}' } from 'some-module'\`?`,ExportDefaultFromAsIdentifier:"'from' is not allowed as an identifier after 'export default'.",ForInOfLoopInitializer:({type:e})=>`'${e==="ForInStatement"?"for-in":"for-of"}' loop variable declaration may not have an initializer.`,ForInUsing:"For-in loop may not start with 'using' declaration.",ForOfAsync:"The left-hand side of a for-of loop may not be 'async'.",ForOfLet:"The left-hand side of a for-of loop may not start with 'let'.",GeneratorInSingleStatementContext:"Generators can only be declared at the top level or inside a block.",IllegalBreakContinue:({type:e})=>`Unsyntactic ${e==="BreakStatement"?"break":"continue"}.`,IllegalLanguageModeDirective:"Illegal 'use strict' directive in function with non-simple parameter list.",IllegalReturn:"'return' outside of function.",ImportAttributesUseAssert:"The `assert` keyword in import attributes is deprecated and it has been replaced by the `with` keyword. You can enable the `deprecatedImportAssert` parser plugin to suppress this error.",ImportBindingIsString:({importName:e})=>`A string literal cannot be used as an imported binding.
- Did you mean \`import { "${e}" as foo }\`?`,ImportCallArity:"`import()` requires exactly one or two arguments.",ImportCallNotNewExpression:"Cannot use new with import(...).",ImportCallSpreadArgument:"`...` is not allowed in `import()`.",ImportJSONBindingNotDefault:"A JSON module can only be imported with `default`.",ImportReflectionHasAssertion:"`import module x` cannot have assertions.",ImportReflectionNotBinding:'Only `import module x from "./module"` is valid.',IncompatibleRegExpUVFlags:"The 'u' and 'v' regular expression flags cannot be enabled at the same time.",InvalidBigIntLiteral:"Invalid BigIntLiteral.",InvalidCodePoint:"Code point out of bounds.",InvalidCoverInitializedName:"Invalid shorthand property initializer.",InvalidDecimal:"Invalid decimal.",InvalidDigit:({radix:e})=>`Expected number in radix ${e}.`,InvalidEscapeSequence:"Bad character escape sequence.",InvalidEscapeSequenceTemplate:"Invalid escape sequence in template.",InvalidEscapedReservedWord:({reservedWord:e})=>`Escape sequence in keyword ${e}.`,InvalidIdentifier:({identifierName:e})=>`Invalid identifier ${e}.`,InvalidLhs:({ancestor:e})=>`Invalid left-hand side in ${lu(e)}.`,InvalidLhsBinding:({ancestor:e})=>`Binding invalid left-hand side in ${lu(e)}.`,InvalidLhsOptionalChaining:({ancestor:e})=>`Invalid optional chaining in the left-hand side of ${lu(e)}.`,InvalidNumber:"Invalid number.",InvalidOrMissingExponent:"Floating-point numbers require a valid exponent after the 'e'.",InvalidOrUnexpectedToken:({unexpected:e})=>`Unexpected character '${e}'.`,InvalidParenthesizedAssignment:"Invalid parenthesized assignment pattern.",InvalidPrivateFieldResolution:({identifierName:e})=>`Private name #${e} is not defined.`,InvalidPropertyBindingPattern:"Binding member expression.",InvalidRecordProperty:"Only properties and spread elements are allowed in record definitions.",InvalidRestAssignmentPattern:"Invalid rest operator's argument.",LabelRedeclaration:({labelName:e})=>`Label '${e}' is already declared.`,LetInLexicalBinding:"'let' is disallowed as a lexically bound name.",LineTerminatorBeforeArrow:"No line break is allowed before '=>'.",MalformedRegExpFlags:"Invalid regular expression flag.",MissingClassName:"A class name is required.",MissingEqInAssignment:"Only '=' operator can be used for specifying default value.",MissingSemicolon:"Missing semicolon.",MissingPlugin:({missingPlugin:e})=>`This experimental syntax requires enabling the parser plugin: ${e.map(t=>JSON.stringify(t)).join(", ")}.`,MissingOneOfPlugins:({missingPlugin:e})=>`This experimental syntax requires enabling one of the following parser plugin(s): ${e.map(t=>JSON.stringify(t)).join(", ")}.`,MissingUnicodeEscape:"Expecting Unicode escape sequence \\uXXXX.",MixingCoalesceWithLogical:"Nullish coalescing operator(??) requires parens when mixing with logical operators.",ModuleAttributeDifferentFromType:"The only accepted module attribute is `type`.",ModuleAttributeInvalidValue:"Only string literals are allowed as module attribute values.",ModuleAttributesWithDuplicateKeys:({key:e})=>`Duplicate key "${e}" is not allowed in module attributes.`,ModuleExportNameHasLoneSurrogate:({surrogateCharCode:e})=>`An export name cannot include a lone surrogate, found '\\u${e.toString(16)}'.`,ModuleExportUndefined:({localName:e})=>`Export '${e}' is not defined.`,MultipleDefaultsInSwitch:"Multiple default clauses.",NewlineAfterThrow:"Illegal newline after throw.",NoCatchOrFinally:"Missing catch or finally clause.",NumberIdentifier:"Identifier directly after number.",NumericSeparatorInEscapeSequence:"Numeric separators are not allowed inside unicode escape sequences or hex escape sequences.",ObsoleteAwaitStar:"'await*' has been removed from the async functions proposal. Use Promise.all() instead.",OptionalChainingNoNew:"Constructors in/after an Optional Chain are not allowed.",OptionalChainingNoTemplate:"Tagged Template Literals are not allowed in optionalChain.",OverrideOnConstructor:"'override' modifier cannot appear on a constructor declaration.",ParamDupe:"Argument name clash.",PatternHasAccessor:"Object pattern can't contain getter or setter.",PatternHasMethod:"Object pattern can't contain methods.",PrivateInExpectedIn:({identifierName:e})=>`Private names are only allowed in property accesses (\`obj.#${e}\`) or in \`in\` expressions (\`#${e} in obj\`).`,PrivateNameRedeclaration:({identifierName:e})=>`Duplicate private name #${e}.`,RecordExpressionBarIncorrectEndSyntaxType:"Record expressions ending with '|}' are only allowed when the 'syntaxType' option of the 'recordAndTuple' plugin is set to 'bar'.",RecordExpressionBarIncorrectStartSyntaxType:"Record expressions starting with '{|' are only allowed when the 'syntaxType' option of the 'recordAndTuple' plugin is set to 'bar'.",RecordExpressionHashIncorrectStartSyntaxType:"Record expressions starting with '#{' are only allowed when the 'syntaxType' option of the 'recordAndTuple' plugin is set to 'hash'.",RecordNoProto:"'__proto__' is not allowed in Record expressions.",RestTrailingComma:"Unexpected trailing comma after rest element.",SloppyFunction:"In non-strict mode code, functions can only be declared at top level or inside a block.",SloppyFunctionAnnexB:"In non-strict mode code, functions can only be declared at top level, inside a block, or as the body of an if statement.",SourcePhaseImportRequiresDefault:'Only `import source x from "./module"` is valid.',StaticPrototype:"Classes may not have static property named prototype.",SuperNotAllowed:"`super()` is only valid inside a class constructor of a subclass. Maybe a typo in the method name ('constructor') or not extending another class?",SuperPrivateField:"Private fields can't be accessed on super.",TrailingDecorator:"Decorators must be attached to a class element.",TupleExpressionBarIncorrectEndSyntaxType:"Tuple expressions ending with '|]' are only allowed when the 'syntaxType' option of the 'recordAndTuple' plugin is set to 'bar'.",TupleExpressionBarIncorrectStartSyntaxType:"Tuple expressions starting with '[|' are only allowed when the 'syntaxType' option of the 'recordAndTuple' plugin is set to 'bar'.",TupleExpressionHashIncorrectStartSyntaxType:"Tuple expressions starting with '#[' are only allowed when the 'syntaxType' option of the 'recordAndTuple' plugin is set to 'hash'.",UnexpectedArgumentPlaceholder:"Unexpected argument placeholder.",UnexpectedAwaitAfterPipelineBody:'Unexpected "await" after pipeline body; await must have parentheses in minimal proposal.',UnexpectedDigitAfterHash:"Unexpected digit after hash token.",UnexpectedImportExport:"'import' and 'export' may only appear at the top level.",UnexpectedKeyword:({keyword:e})=>`Unexpected keyword '${e}'.`,UnexpectedLeadingDecorator:"Leading decorators must be attached to a class declaration.",UnexpectedLexicalDeclaration:"Lexical declaration cannot appear in a single-statement context.",UnexpectedNewTarget:"`new.target` can only be used in functions or class properties.",UnexpectedNumericSeparator:"A numeric separator is only allowed between two digits.",UnexpectedPrivateField:"Unexpected private name.",UnexpectedReservedWord:({reservedWord:e})=>`Unexpected reserved word '${e}'.`,UnexpectedSuper:"'super' is only allowed in object methods and classes.",UnexpectedToken:({expected:e,unexpected:t})=>`Unexpected token${t?` '${t}'.`:""}${e?`, expected "${e}"`:""}`,UnexpectedTokenUnaryExponentiation:"Illegal expression. Wrap left hand side or entire exponentiation in parentheses.",UnexpectedUsingDeclaration:"Using declaration cannot appear in the top level when source type is `script`.",UnsupportedBind:"Binding should be performed on object property.",UnsupportedDecoratorExport:"A decorated export must export a class declaration.",UnsupportedDefaultExport:"Only expressions, functions or classes are allowed as the `default` export.",UnsupportedImport:"`import` can only be used in `import()` or `import.meta`.",UnsupportedMetaProperty:({target:e,onlyValidPropertyName:t})=>`The only valid meta property for ${e} is ${e}.${t}.`,UnsupportedParameterDecorator:"Decorators cannot be used to decorate parameters.",UnsupportedPropertyDecorator:"Decorators cannot be used to decorate object literal properties.",UnsupportedSuper:"'super' can only be used with function calls (i.e. super()) or in property accesses (i.e. super.prop or super[prop]).",UnterminatedComment:"Unterminated comment.",UnterminatedRegExp:"Unterminated regular expression.",UnterminatedString:"Unterminated string constant.",UnterminatedTemplate:"Unterminated template.",UsingDeclarationExport:"Using declaration cannot be exported.",UsingDeclarationHasBindingPattern:"Using declaration cannot have destructuring patterns.",VarRedeclaration:({identifierName:e})=>`Identifier '${e}' has already been declared.`,YieldBindingIdentifier:"Can not use 'yield' as identifier inside a generator.",YieldInParameter:"Yield expression is not allowed in formal parameters.",ZeroDigitNumericSeparator:"Numeric separator can not be used after leading 0."},$G={StrictDelete:"Deleting local variable in strict mode.",StrictEvalArguments:({referenceName:e})=>`Assigning to '${e}' in strict mode.`,StrictEvalArgumentsBinding:({bindingName:e})=>`Binding '${e}' in strict mode.`,StrictFunction:"In strict mode code, functions can only be declared at top level or inside a block.",StrictNumericEscape:"The only valid numeric escape in strict mode is '\\0'.",StrictOctalLiteral:"Legacy octal literals are not allowed in strict mode.",StrictWith:"'with' in strict mode."},WG=new Set(["ArrowFunctionExpression","AssignmentExpression","ConditionalExpression","YieldExpression"]),KG=Object.assign({PipeBodyIsTighter:"Unexpected yield after pipeline body; any yield expression acting as Hack-style pipe body must be parenthesized due to its loose operator precedence.",PipeTopicRequiresHackPipes:'Topic reference is used, but the pipelineOperator plugin was not passed a "proposal": "hack" or "smart" option.',PipeTopicUnbound:"Topic reference is unbound; it must be inside a pipe body.",PipeTopicUnconfiguredToken:({token:e})=>`Invalid topic token ${e}. In order to use ${e} as a topic reference, the pipelineOperator plugin must be configured with { "proposal": "hack", "topicToken": "${e}" }.`,PipeTopicUnused:"Hack-style pipe body does not contain a topic reference; Hack-style pipes must use topic at least once.",PipeUnparenthesizedBody:({type:e})=>`Hack-style pipe body cannot be an unparenthesized ${lu({type:e})}; please wrap it in parentheses.`},{PipelineBodyNoArrow:'Unexpected arrow "=>" after pipeline body; arrow function in pipeline body must be parenthesized.',PipelineBodySequenceExpression:"Pipeline body may not be a comma-separated sequence expression.",PipelineHeadSequenceExpression:"Pipeline head should not be a comma-separated sequence expression.",PipelineTopicUnused:"Pipeline is in topic style but does not use topic reference.",PrimaryTopicNotAllowed:"Topic reference was used in a lexical context without topic binding.",PrimaryTopicRequiresSmartPipeline:'Topic reference is used, but the pipelineOperator plugin was not passed a "proposal": "hack" or "smart" option.'}),GG=["message"];function jS(e,t,r){Object.defineProperty(e,t,{enumerable:!1,configurable:!0,value:r})}function HG({toMessage:e,code:t,reasonCode:r,syntaxPlugin:n}){let s=r==="MissingPlugin"||r==="MissingOneOfPlugins";{let i={AccessorCannotDeclareThisParameter:"AccesorCannotDeclareThisParameter",AccessorCannotHaveTypeParameters:"AccesorCannotHaveTypeParameters",ConstInitializerMustBeStringOrNumericLiteralOrLiteralEnumReference:"ConstInitiailizerMustBeStringOrNumericLiteralOrLiteralEnumReference",SetAccessorCannotHaveOptionalParameter:"SetAccesorCannotHaveOptionalParameter",SetAccessorCannotHaveRestParameter:"SetAccesorCannotHaveRestParameter",SetAccessorCannotHaveReturnType:"SetAccesorCannotHaveReturnType"};i[r]&&(r=i[r])}return function i(a,o){let l=new SyntaxError;return l.code=t,l.reasonCode=r,l.loc=a,l.pos=a.index,l.syntaxPlugin=n,s&&(l.missingPlugin=o.missingPlugin),jS(l,"clone",function(c={}){var p;let{line:d,column:y,index:E}=(p=c.loc)!=null?p:a;return i(new Tn(d,y,E),Object.assign({},o,c.details))}),jS(l,"details",o),Object.defineProperty(l,"message",{configurable:!0,get(){let u=`${e(o)} (${a.line}:${a.column})`;return this.message=u,u},set(u){Object.defineProperty(this,"message",{value:u,writable:!0})}}),l}}function Mn(e,t){if(Array.isArray(e))return n=>Mn(n,e[0]);let r={};for(let n of Object.keys(e)){let s=e[n],i=typeof s=="string"?{message:()=>s}:typeof s=="function"?{message:s}:s,{message:a}=i,o=qG(i,GG),l=typeof a=="string"?()=>a:a;r[n]=HG(Object.assign({code:"BABEL_PARSER_SYNTAX_ERROR",reasonCode:n,toMessage:l},t?{syntaxPlugin:t}:{},o))}return r}var B=Object.assign({},Mn(UG),Mn(VG),Mn($G),Mn`pipelineOperator`(KG));function YG(){return{sourceType:"script",sourceFilename:void 0,startIndex:0,startColumn:0,startLine:1,allowAwaitOutsideFunction:!1,allowReturnOutsideFunction:!1,allowNewTargetOutsideFunction:!1,allowImportExportEverywhere:!1,allowSuperOutsideMethod:!1,allowUndeclaredExports:!1,plugins:[],strictMode:null,ranges:!1,tokens:!1,createImportExpressions:!1,createParenthesizedExpressions:!1,errorRecovery:!1,attachComment:!0,annexB:!0}}function XG(e){let t=YG();if(e==null)return t;if(e.annexB!=null&&e.annexB!==!1)throw new Error("The `annexB` option can only be set to `false`.");for(let r of Object.keys(t))e[r]!=null&&(t[r]=e[r]);if(t.startLine===1)e.startIndex==null&&t.startColumn>0?t.startIndex=t.startColumn:e.startColumn==null&&t.startIndex>0&&(t.startColumn=t.startIndex);else if((e.startColumn==null||e.startIndex==null)&&e.startIndex!=null)throw new Error("With a `startLine > 1` you must also specify `startIndex` and `startColumn`.");return t}var{defineProperty:JG}=Object,MS=(e,t)=>{e&&JG(e,t,{enumerable:!1,value:e[t]})};function lo(e){return MS(e.loc.start,"index"),MS(e.loc.end,"index"),e}var zG=e=>class extends e{parse(){let r=lo(super.parse());return this.optionFlags&128&&(r.tokens=r.tokens.map(lo)),r}parseRegExpLiteral({pattern:r,flags:n}){let s=null;try{s=new RegExp(r,n)}catch{}let i=this.estreeParseLiteral(s);return i.regex={pattern:r,flags:n},i}parseBigIntLiteral(r){let n;try{n=BigInt(r)}catch{n=null}let s=this.estreeParseLiteral(n);return s.bigint=String(s.value||r),s}parseDecimalLiteral(r){let s=this.estreeParseLiteral(null);return s.decimal=String(s.value||r),s}estreeParseLiteral(r){return this.parseLiteral(r,"Literal")}parseStringLiteral(r){return this.estreeParseLiteral(r)}parseNumericLiteral(r){return this.estreeParseLiteral(r)}parseNullLiteral(){return this.estreeParseLiteral(null)}parseBooleanLiteral(r){return this.estreeParseLiteral(r)}directiveToStmt(r){let n=r.value;delete r.value,n.type="Literal",n.raw=n.extra.raw,n.value=n.extra.expressionValue;let s=r;return s.type="ExpressionStatement",s.expression=n,s.directive=n.extra.rawValue,delete n.extra,s}initFunction(r,n){super.initFunction(r,n),r.expression=!1}checkDeclaration(r){r!=null&&this.isObjectProperty(r)?this.checkDeclaration(r.value):super.checkDeclaration(r)}getObjectOrClassMethodParams(r){return r.value.params}isValidDirective(r){var n;return r.type==="ExpressionStatement"&&r.expression.type==="Literal"&&typeof r.expression.value=="string"&&!((n=r.expression.extra)!=null&&n.parenthesized)}parseBlockBody(r,n,s,i,a){super.parseBlockBody(r,n,s,i,a);let o=r.directives.map(l=>this.directiveToStmt(l));r.body=o.concat(r.body),delete r.directives}parsePrivateName(){let r=super.parsePrivateName();return this.getPluginOption("estree","classFeatures")?this.convertPrivateNameToPrivateIdentifier(r):r}convertPrivateNameToPrivateIdentifier(r){let n=super.getPrivateNameSV(r);return r=r,delete r.id,r.name=n,r.type="PrivateIdentifier",r}isPrivateName(r){return this.getPluginOption("estree","classFeatures")?r.type==="PrivateIdentifier":super.isPrivateName(r)}getPrivateNameSV(r){return this.getPluginOption("estree","classFeatures")?r.name:super.getPrivateNameSV(r)}parseLiteral(r,n){let s=super.parseLiteral(r,n);return s.raw=s.extra.raw,delete s.extra,s}parseFunctionBody(r,n,s=!1){super.parseFunctionBody(r,n,s),r.expression=r.body.type!=="BlockStatement"}parseMethod(r,n,s,i,a,o,l=!1){let u=this.startNode();u.kind=r.kind,u=super.parseMethod(u,n,s,i,a,o,l),u.type="FunctionExpression",delete u.kind,r.value=u;let{typeParameters:c}=r;return c&&(delete r.typeParameters,u.typeParameters=c,this.resetStartLocationFromNode(u,c)),o==="ClassPrivateMethod"&&(r.computed=!1),this.finishNode(r,"MethodDefinition")}nameIsConstructor(r){return r.type==="Literal"?r.value==="constructor":super.nameIsConstructor(r)}parseClassProperty(...r){let n=super.parseClassProperty(...r);return this.getPluginOption("estree","classFeatures")&&(n.type="PropertyDefinition"),n}parseClassPrivateProperty(...r){let n=super.parseClassPrivateProperty(...r);return this.getPluginOption("estree","classFeatures")&&(n.type="PropertyDefinition",n.computed=!1),n}parseObjectMethod(r,n,s,i,a){let o=super.parseObjectMethod(r,n,s,i,a);return o&&(o.type="Property",o.kind==="method"&&(o.kind="init"),o.shorthand=!1),o}parseObjectProperty(r,n,s,i){let a=super.parseObjectProperty(r,n,s,i);return a&&(a.kind="init",a.type="Property"),a}isValidLVal(r,n,s){return r==="Property"?"value":super.isValidLVal(r,n,s)}isAssignable(r,n){return r!=null&&this.isObjectProperty(r)?this.isAssignable(r.value,n):super.isAssignable(r,n)}toAssignable(r,n=!1){if(r!=null&&this.isObjectProperty(r)){let{key:s,value:i}=r;this.isPrivateName(s)&&this.classScope.usePrivateName(this.getPrivateNameSV(s),s.loc.start),this.toAssignable(i,n)}else super.toAssignable(r,n)}toAssignableObjectExpressionProp(r,n,s){r.type==="Property"&&(r.kind==="get"||r.kind==="set")?this.raise(B.PatternHasAccessor,r.key):r.type==="Property"&&r.method?this.raise(B.PatternHasMethod,r.key):super.toAssignableObjectExpressionProp(r,n,s)}finishCallExpression(r,n){let s=super.finishCallExpression(r,n);if(s.callee.type==="Import"){var i,a;s.type="ImportExpression",s.source=s.arguments[0],s.options=(i=s.arguments[1])!=null?i:null,s.attributes=(a=s.arguments[1])!=null?a:null,delete s.arguments,delete s.callee}return s}toReferencedArguments(r){r.type!=="ImportExpression"&&super.toReferencedArguments(r)}parseExport(r,n){let s=this.state.lastTokStartLoc,i=super.parseExport(r,n);switch(i.type){case"ExportAllDeclaration":i.exported=null;break;case"ExportNamedDeclaration":i.specifiers.length===1&&i.specifiers[0].type==="ExportNamespaceSpecifier"&&(i.type="ExportAllDeclaration",i.exported=i.specifiers[0].exported,delete i.specifiers);case"ExportDefaultDeclaration":{var a;let{declaration:o}=i;(o==null?void 0:o.type)==="ClassDeclaration"&&((a=o.decorators)==null?void 0:a.length)>0&&o.start===i.start&&this.resetStartLocation(i,s)}break}return i}parseSubscript(r,n,s,i){let a=super.parseSubscript(r,n,s,i);if(i.optionalChainMember){if((a.type==="OptionalMemberExpression"||a.type==="OptionalCallExpression")&&(a.type=a.type.substring(8)),i.stop){let o=this.startNodeAtNode(a);return o.expression=a,this.finishNode(o,"ChainExpression")}}else(a.type==="MemberExpression"||a.type==="CallExpression")&&(a.optional=!1);return a}isOptionalMemberExpression(r){return r.type==="ChainExpression"?r.expression.type==="MemberExpression":super.isOptionalMemberExpression(r)}hasPropertyAsPrivateName(r){return r.type==="ChainExpression"&&(r=r.expression),super.hasPropertyAsPrivateName(r)}isObjectProperty(r){return r.type==="Property"&&r.kind==="init"&&!r.method}isObjectMethod(r){return r.type==="Property"&&(r.method||r.kind==="get"||r.kind==="set")}finishNodeAt(r,n,s){return lo(super.finishNodeAt(r,n,s))}resetStartLocation(r,n){super.resetStartLocation(r,n),lo(r)}resetEndLocation(r,n=this.state.lastTokEndLoc){super.resetEndLocation(r,n),lo(r)}},ii=class{constructor(t,r){this.token=void 0,this.preserveSpace=void 0,this.token=t,this.preserveSpace=!!r}},at={brace:new ii("{"),j_oTag:new ii("<tag"),j_cTag:new ii("</tag"),j_expr:new ii("<tag>...</tag>",!0)};at.template=new ii("`",!0);var Ke=!0,ae=!0,Qh=!0,uo=!0,Es=!0,QG=!0,pu=class{constructor(t,r={}){this.label=void 0,this.keyword=void 0,this.beforeExpr=void 0,this.startsExpr=void 0,this.rightAssociative=void 0,this.isLoop=void 0,this.isAssign=void 0,this.prefix=void 0,this.postfix=void 0,this.binop=void 0,this.label=t,this.keyword=r.keyword,this.beforeExpr=!!r.beforeExpr,this.startsExpr=!!r.startsExpr,this.rightAssociative=!!r.rightAssociative,this.isLoop=!!r.isLoop,this.isAssign=!!r.isAssign,this.prefix=!!r.prefix,this.postfix=!!r.postfix,this.binop=r.binop!=null?r.binop:null,this.updateContext=null}},vm=new Map;function rt(e,t={}){t.keyword=e;let r=Pe(e,t);return vm.set(e,r),r}function yr(e,t){return Pe(e,{beforeExpr:Ke,binop:t})}var fo=-1,Ln=[],Pm=[],Am=[],Cm=[],Dm=[],wm=[];function Pe(e,t={}){var r,n,s,i;return++fo,Pm.push(e),Am.push((r=t.binop)!=null?r:-1),Cm.push((n=t.beforeExpr)!=null?n:!1),Dm.push((s=t.startsExpr)!=null?s:!1),wm.push((i=t.prefix)!=null?i:!1),Ln.push(new pu(e,t)),fo}function We(e,t={}){var r,n,s,i;return++fo,vm.set(e,fo),Pm.push(e),Am.push((r=t.binop)!=null?r:-1),Cm.push((n=t.beforeExpr)!=null?n:!1),Dm.push((s=t.startsExpr)!=null?s:!1),wm.push((i=t.prefix)!=null?i:!1),Ln.push(new pu("name",t)),fo}var ZG={bracketL:Pe("[",{beforeExpr:Ke,startsExpr:ae}),bracketHashL:Pe("#[",{beforeExpr:Ke,startsExpr:ae}),bracketBarL:Pe("[|",{beforeExpr:Ke,startsExpr:ae}),bracketR:Pe("]"),bracketBarR:Pe("|]"),braceL:Pe("{",{beforeExpr:Ke,startsExpr:ae}),braceBarL:Pe("{|",{beforeExpr:Ke,startsExpr:ae}),braceHashL:Pe("#{",{beforeExpr:Ke,startsExpr:ae}),braceR:Pe("}"),braceBarR:Pe("|}"),parenL:Pe("(",{beforeExpr:Ke,startsExpr:ae}),parenR:Pe(")"),comma:Pe(",",{beforeExpr:Ke}),semi:Pe(";",{beforeExpr:Ke}),colon:Pe(":",{beforeExpr:Ke}),doubleColon:Pe("::",{beforeExpr:Ke}),dot:Pe("."),question:Pe("?",{beforeExpr:Ke}),questionDot:Pe("?."),arrow:Pe("=>",{beforeExpr:Ke}),template:Pe("template"),ellipsis:Pe("...",{beforeExpr:Ke}),backQuote:Pe("`",{startsExpr:ae}),dollarBraceL:Pe("${",{beforeExpr:Ke,startsExpr:ae}),templateTail:Pe("...`",{startsExpr:ae}),templateNonTail:Pe("...${",{beforeExpr:Ke,startsExpr:ae}),at:Pe("@"),hash:Pe("#",{startsExpr:ae}),interpreterDirective:Pe("#!..."),eq:Pe("=",{beforeExpr:Ke,isAssign:uo}),assign:Pe("_=",{beforeExpr:Ke,isAssign:uo}),slashAssign:Pe("_=",{beforeExpr:Ke,isAssign:uo}),xorAssign:Pe("_=",{beforeExpr:Ke,isAssign:uo}),moduloAssign:Pe("_=",{beforeExpr:Ke,isAssign:uo}),incDec:Pe("++/--",{prefix:Es,postfix:QG,startsExpr:ae}),bang:Pe("!",{beforeExpr:Ke,prefix:Es,startsExpr:ae}),tilde:Pe("~",{beforeExpr:Ke,prefix:Es,startsExpr:ae}),doubleCaret:Pe("^^",{startsExpr:ae}),doubleAt:Pe("@@",{startsExpr:ae}),pipeline:yr("|>",0),nullishCoalescing:yr("??",1),logicalOR:yr("||",1),logicalAND:yr("&&",2),bitwiseOR:yr("|",3),bitwiseXOR:yr("^",4),bitwiseAND:yr("&",5),equality:yr("==/!=/===/!==",6),lt:yr("</>/<=/>=",7),gt:yr("</>/<=/>=",7),relational:yr("</>/<=/>=",7),bitShift:yr("<</>>/>>>",8),bitShiftL:yr("<</>>/>>>",8),bitShiftR:yr("<</>>/>>>",8),plusMin:Pe("+/-",{beforeExpr:Ke,binop:9,prefix:Es,startsExpr:ae}),modulo:Pe("%",{binop:10,startsExpr:ae}),star:Pe("*",{binop:10}),slash:yr("/",10),exponent:Pe("**",{beforeExpr:Ke,binop:11,rightAssociative:!0}),_in:rt("in",{beforeExpr:Ke,binop:7}),_instanceof:rt("instanceof",{beforeExpr:Ke,binop:7}),_break:rt("break"),_case:rt("case",{beforeExpr:Ke}),_catch:rt("catch"),_continue:rt("continue"),_debugger:rt("debugger"),_default:rt("default",{beforeExpr:Ke}),_else:rt("else",{beforeExpr:Ke}),_finally:rt("finally"),_function:rt("function",{startsExpr:ae}),_if:rt("if"),_return:rt("return",{beforeExpr:Ke}),_switch:rt("switch"),_throw:rt("throw",{beforeExpr:Ke,prefix:Es,startsExpr:ae}),_try:rt("try"),_var:rt("var"),_const:rt("const"),_with:rt("with"),_new:rt("new",{beforeExpr:Ke,startsExpr:ae}),_this:rt("this",{startsExpr:ae}),_super:rt("super",{startsExpr:ae}),_class:rt("class",{startsExpr:ae}),_extends:rt("extends",{beforeExpr:Ke}),_export:rt("export"),_import:rt("import",{startsExpr:ae}),_null:rt("null",{startsExpr:ae}),_true:rt("true",{startsExpr:ae}),_false:rt("false",{startsExpr:ae}),_typeof:rt("typeof",{beforeExpr:Ke,prefix:Es,startsExpr:ae}),_void:rt("void",{beforeExpr:Ke,prefix:Es,startsExpr:ae}),_delete:rt("delete",{beforeExpr:Ke,prefix:Es,startsExpr:ae}),_do:rt("do",{isLoop:Qh,beforeExpr:Ke}),_for:rt("for",{isLoop:Qh}),_while:rt("while",{isLoop:Qh}),_as:We("as",{startsExpr:ae}),_assert:We("assert",{startsExpr:ae}),_async:We("async",{startsExpr:ae}),_await:We("await",{startsExpr:ae}),_defer:We("defer",{startsExpr:ae}),_from:We("from",{startsExpr:ae}),_get:We("get",{startsExpr:ae}),_let:We("let",{startsExpr:ae}),_meta:We("meta",{startsExpr:ae}),_of:We("of",{startsExpr:ae}),_sent:We("sent",{startsExpr:ae}),_set:We("set",{startsExpr:ae}),_source:We("source",{startsExpr:ae}),_static:We("static",{startsExpr:ae}),_using:We("using",{startsExpr:ae}),_yield:We("yield",{startsExpr:ae}),_asserts:We("asserts",{startsExpr:ae}),_checks:We("checks",{startsExpr:ae}),_exports:We("exports",{startsExpr:ae}),_global:We("global",{startsExpr:ae}),_implements:We("implements",{startsExpr:ae}),_intrinsic:We("intrinsic",{startsExpr:ae}),_infer:We("infer",{startsExpr:ae}),_is:We("is",{startsExpr:ae}),_mixins:We("mixins",{startsExpr:ae}),_proto:We("proto",{startsExpr:ae}),_require:We("require",{startsExpr:ae}),_satisfies:We("satisfies",{startsExpr:ae}),_keyof:We("keyof",{startsExpr:ae}),_readonly:We("readonly",{startsExpr:ae}),_unique:We("unique",{startsExpr:ae}),_abstract:We("abstract",{startsExpr:ae}),_declare:We("declare",{startsExpr:ae}),_enum:We("enum",{startsExpr:ae}),_module:We("module",{startsExpr:ae}),_namespace:We("namespace",{startsExpr:ae}),_interface:We("interface",{startsExpr:ae}),_type:We("type",{startsExpr:ae}),_opaque:We("opaque",{startsExpr:ae}),name:Pe("name",{startsExpr:ae}),placeholder:Pe("%%",{startsExpr:!0}),string:Pe("string",{startsExpr:ae}),num:Pe("num",{startsExpr:ae}),bigint:Pe("bigint",{startsExpr:ae}),decimal:Pe("decimal",{startsExpr:ae}),regexp:Pe("regexp",{startsExpr:ae}),privateName:Pe("#name",{startsExpr:ae}),eof:Pe("eof"),jsxName:Pe("jsxName"),jsxText:Pe("jsxText",{beforeExpr:!0}),jsxTagStart:Pe("jsxTagStart",{startsExpr:!0}),jsxTagEnd:Pe("jsxTagEnd")};function ot(e){return e>=93&&e<=133}function eH(e){return e<=92}function en(e){return e>=58&&e<=133}function JS(e){return e>=58&&e<=137}function tH(e){return Cm[e]}function nm(e){return Dm[e]}function rH(e){return e>=29&&e<=33}function RS(e){return e>=129&&e<=131}function nH(e){return e>=90&&e<=92}function Im(e){return e>=58&&e<=92}function sH(e){return e>=39&&e<=59}function iH(e){return e===34}function aH(e){return wm[e]}function oH(e){return e>=121&&e<=123}function lH(e){return e>=124&&e<=130}function Ss(e){return Pm[e]}function uu(e){return Am[e]}function uH(e){return e===57}function fu(e){return e>=24&&e<=25}function Fn(e){return Ln[e]}Ln[8].updateContext=e=>{e.pop()},Ln[5].updateContext=Ln[7].updateContext=Ln[23].updateContext=e=>{e.push(at.brace)},Ln[22].updateContext=e=>{e[e.length-1]===at.template?e.pop():e.push(at.template)},Ln[143].updateContext=e=>{e.push(at.j_expr,at.j_oTag)};var _m="\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0560-\u0588\u05D0-\u05EA\u05EF-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u0860-\u086A\u0870-\u0887\u0889-\u088E\u08A0-\u08C9\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u09FC\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C5D\u0C60\u0C61\u0C80\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D04-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D54-\u0D56\u0D5F-\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E86-\u0E8A\u0E8C-\u0EA3\u0EA5\u0EA7-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u1711\u171F-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1878\u1880-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4C\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1C80-\u1C8A\u1C90-\u1CBA\u1CBD-\u1CBF\u1CE9-\u1CEC\u1CEE-\u1CF3\u1CF5\u1CF6\u1CFA\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2118-\u211D\u2124\u2126\u2128\u212A-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u3005-\u3007\u3021-\u3029\u3031-\u3035\u3038-\u303C\u3041-\u3096\u309B-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312F\u3131-\u318E\u31A0-\u31BF\u31F0-\u31FF\u3400-\u4DBF\u4E00-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6EF\uA717-\uA71F\uA722-\uA788\uA78B-\uA7CD\uA7D0\uA7D1\uA7D3\uA7D5-\uA7DC\uA7F2-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA8FD\uA8FE\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uA9E0-\uA9E4\uA9E6-\uA9EF\uA9FA-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB69\uAB70-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC",zS="\xB7\u0300-\u036F\u0387\u0483-\u0487\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u0610-\u061A\u064B-\u0669\u0670\u06D6-\u06DC\u06DF-\u06E4\u06E7\u06E8\u06EA-\u06ED\u06F0-\u06F9\u0711\u0730-\u074A\u07A6-\u07B0\u07C0-\u07C9\u07EB-\u07F3\u07FD\u0816-\u0819\u081B-\u0823\u0825-\u0827\u0829-\u082D\u0859-\u085B\u0897-\u089F\u08CA-\u08E1\u08E3-\u0903\u093A-\u093C\u093E-\u094F\u0951-\u0957\u0962\u0963\u0966-\u096F\u0981-\u0983\u09BC\u09BE-\u09C4\u09C7\u09C8\u09CB-\u09CD\u09D7\u09E2\u09E3\u09E6-\u09EF\u09FE\u0A01-\u0A03\u0A3C\u0A3E-\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A66-\u0A71\u0A75\u0A81-\u0A83\u0ABC\u0ABE-\u0AC5\u0AC7-\u0AC9\u0ACB-\u0ACD\u0AE2\u0AE3\u0AE6-\u0AEF\u0AFA-\u0AFF\u0B01-\u0B03\u0B3C\u0B3E-\u0B44\u0B47\u0B48\u0B4B-\u0B4D\u0B55-\u0B57\u0B62\u0B63\u0B66-\u0B6F\u0B82\u0BBE-\u0BC2\u0BC6-\u0BC8\u0BCA-\u0BCD\u0BD7\u0BE6-\u0BEF\u0C00-\u0C04\u0C3C\u0C3E-\u0C44\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C62\u0C63\u0C66-\u0C6F\u0C81-\u0C83\u0CBC\u0CBE-\u0CC4\u0CC6-\u0CC8\u0CCA-\u0CCD\u0CD5\u0CD6\u0CE2\u0CE3\u0CE6-\u0CEF\u0CF3\u0D00-\u0D03\u0D3B\u0D3C\u0D3E-\u0D44\u0D46-\u0D48\u0D4A-\u0D4D\u0D57\u0D62\u0D63\u0D66-\u0D6F\u0D81-\u0D83\u0DCA\u0DCF-\u0DD4\u0DD6\u0DD8-\u0DDF\u0DE6-\u0DEF\u0DF2\u0DF3\u0E31\u0E34-\u0E3A\u0E47-\u0E4E\u0E50-\u0E59\u0EB1\u0EB4-\u0EBC\u0EC8-\u0ECE\u0ED0-\u0ED9\u0F18\u0F19\u0F20-\u0F29\u0F35\u0F37\u0F39\u0F3E\u0F3F\u0F71-\u0F84\u0F86\u0F87\u0F8D-\u0F97\u0F99-\u0FBC\u0FC6\u102B-\u103E\u1040-\u1049\u1056-\u1059\u105E-\u1060\u1062-\u1064\u1067-\u106D\u1071-\u1074\u1082-\u108D\u108F-\u109D\u135D-\u135F\u1369-\u1371\u1712-\u1715\u1732-\u1734\u1752\u1753\u1772\u1773\u17B4-\u17D3\u17DD\u17E0-\u17E9\u180B-\u180D\u180F-\u1819\u18A9\u1920-\u192B\u1930-\u193B\u1946-\u194F\u19D0-\u19DA\u1A17-\u1A1B\u1A55-\u1A5E\u1A60-\u1A7C\u1A7F-\u1A89\u1A90-\u1A99\u1AB0-\u1ABD\u1ABF-\u1ACE\u1B00-\u1B04\u1B34-\u1B44\u1B50-\u1B59\u1B6B-\u1B73\u1B80-\u1B82\u1BA1-\u1BAD\u1BB0-\u1BB9\u1BE6-\u1BF3\u1C24-\u1C37\u1C40-\u1C49\u1C50-\u1C59\u1CD0-\u1CD2\u1CD4-\u1CE8\u1CED\u1CF4\u1CF7-\u1CF9\u1DC0-\u1DFF\u200C\u200D\u203F\u2040\u2054\u20D0-\u20DC\u20E1\u20E5-\u20F0\u2CEF-\u2CF1\u2D7F\u2DE0-\u2DFF\u302A-\u302F\u3099\u309A\u30FB\uA620-\uA629\uA66F\uA674-\uA67D\uA69E\uA69F\uA6F0\uA6F1\uA802\uA806\uA80B\uA823-\uA827\uA82C\uA880\uA881\uA8B4-\uA8C5\uA8D0-\uA8D9\uA8E0-\uA8F1\uA8FF-\uA909\uA926-\uA92D\uA947-\uA953\uA980-\uA983\uA9B3-\uA9C0\uA9D0-\uA9D9\uA9E5\uA9F0-\uA9F9\uAA29-\uAA36\uAA43\uAA4C\uAA4D\uAA50-\uAA59\uAA7B-\uAA7D\uAAB0\uAAB2-\uAAB4\uAAB7\uAAB8\uAABE\uAABF\uAAC1\uAAEB-\uAAEF\uAAF5\uAAF6\uABE3-\uABEA\uABEC\uABED\uABF0-\uABF9\uFB1E\uFE00-\uFE0F\uFE20-\uFE2F\uFE33\uFE34\uFE4D-\uFE4F\uFF10-\uFF19\uFF3F\uFF65",cH=new RegExp("["+_m+"]"),pH=new RegExp("["+_m+zS+"]");_m=zS=null;var QS=[0,11,2,25,2,18,2,1,2,14,3,13,35,122,70,52,268,28,4,48,48,31,14,29,6,37,11,29,3,35,5,7,2,4,43,157,19,35,5,35,5,39,9,51,13,10,2,14,2,6,2,1,2,10,2,14,2,6,2,1,4,51,13,310,10,21,11,7,25,5,2,41,2,8,70,5,3,0,2,43,2,1,4,0,3,22,11,22,10,30,66,18,2,1,11,21,11,25,71,55,7,1,65,0,16,3,2,2,2,28,43,28,4,28,36,7,2,27,28,53,11,21,11,18,14,17,111,72,56,50,14,50,14,35,39,27,10,22,251,41,7,1,17,2,60,28,11,0,9,21,43,17,47,20,28,22,13,52,58,1,3,0,14,44,33,24,27,35,30,0,3,0,9,34,4,0,13,47,15,3,22,0,2,0,36,17,2,24,20,1,64,6,2,0,2,3,2,14,2,9,8,46,39,7,3,1,3,21,2,6,2,1,2,4,4,0,19,0,13,4,31,9,2,0,3,0,2,37,2,0,26,0,2,0,45,52,19,3,21,2,31,47,21,1,2,0,185,46,42,3,37,47,21,0,60,42,14,0,72,26,38,6,186,43,117,63,32,7,3,0,3,7,2,1,2,23,16,0,2,0,95,7,3,38,17,0,2,0,29,0,11,39,8,0,22,0,12,45,20,0,19,72,200,32,32,8,2,36,18,0,50,29,113,6,2,1,2,37,22,0,26,5,2,1,2,31,15,0,328,18,16,0,2,12,2,33,125,0,80,921,103,110,18,195,2637,96,16,1071,18,5,26,3994,6,582,6842,29,1763,568,8,30,18,78,18,29,19,47,17,3,32,20,6,18,433,44,212,63,129,74,6,0,67,12,65,1,2,0,29,6135,9,1237,42,9,8936,3,2,6,2,1,2,290,16,0,30,2,3,0,15,3,9,395,2309,106,6,12,4,8,8,9,5991,84,2,70,2,1,3,0,3,1,3,3,2,11,2,0,2,6,2,64,2,3,3,7,2,6,2,27,2,3,2,4,2,0,4,6,2,339,3,24,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,7,1845,30,7,5,262,61,147,44,11,6,17,0,322,29,19,43,485,27,229,29,3,0,496,6,2,3,2,1,2,14,2,196,60,67,8,0,1205,3,2,26,2,1,2,0,3,0,2,9,2,3,2,0,2,0,7,0,5,0,2,0,2,0,2,2,2,1,2,0,3,0,2,0,2,0,2,0,2,0,2,1,2,0,3,3,2,6,2,3,2,3,2,0,2,9,2,16,6,2,2,4,2,16,4421,42719,33,4153,7,221,3,5761,15,7472,16,621,2467,541,1507,4938,6,4191],fH=[509,0,227,0,150,4,294,9,1368,2,2,1,6,3,41,2,5,0,166,1,574,3,9,9,7,9,32,4,318,1,80,3,71,10,50,3,123,2,54,14,32,10,3,1,11,3,46,10,8,0,46,9,7,2,37,13,2,9,6,1,45,0,13,2,49,13,9,3,2,11,83,11,7,0,3,0,158,11,6,9,7,3,56,1,2,6,3,1,3,2,10,0,11,1,3,6,4,4,68,8,2,0,3,0,2,3,2,4,2,0,15,1,83,17,10,9,5,0,82,19,13,9,214,6,3,8,28,1,83,16,16,9,82,12,9,9,7,19,58,14,5,9,243,14,166,9,71,5,2,1,3,3,2,0,2,1,13,9,120,6,3,6,4,0,29,9,41,6,2,3,9,0,10,10,47,15,343,9,54,7,2,7,17,9,57,21,2,13,123,5,4,0,2,1,2,6,2,0,9,9,49,4,2,1,2,4,9,9,330,3,10,1,2,0,49,6,4,4,14,10,5350,0,7,14,11465,27,2343,9,87,9,39,4,60,6,26,9,535,9,470,0,2,54,8,3,82,0,12,1,19628,1,4178,9,519,45,3,22,543,4,4,5,9,7,3,6,31,3,149,2,1418,49,513,54,5,49,9,0,15,0,23,4,2,14,1361,6,2,16,3,6,2,1,2,4,101,0,161,6,10,9,357,0,62,13,499,13,245,1,2,9,726,6,110,6,6,9,4759,9,787719,239];function sm(e,t){let r=65536;for(let n=0,s=t.length;n<s;n+=2){if(r+=t[n],r>e)return!1;if(r+=t[n+1],r>=e)return!0}return!1}function jn(e){return e<65?e===36:e<=90?!0:e<97?e===95:e<=122?!0:e<=65535?e>=170&&cH.test(String.fromCharCode(e)):sm(e,QS)}function Xi(e){return e<48?e===36:e<58?!0:e<65?!1:e<=90?!0:e<97?e===95:e<=122?!0:e<=65535?e>=170&&pH.test(String.fromCharCode(e)):sm(e,QS)||sm(e,fH)}var Om={keyword:["break","case","catch","continue","debugger","default","do","else","finally","for","function","if","return","switch","throw","try","var","const","while","with","new","this","super","class","extends","export","import","null","true","false","in","instanceof","typeof","void","delete"],strict:["implements","interface","let","package","private","protected","public","static","yield"],strictBind:["eval","arguments"]},dH=new Set(Om.keyword),hH=new Set(Om.strict),mH=new Set(Om.strictBind);function ZS(e,t){return t&&e==="await"||e==="enum"}function ex(e,t){return ZS(e,t)||hH.has(e)}function tx(e){return mH.has(e)}function rx(e,t){return ex(e,t)||tx(e)}function yH(e){return dH.has(e)}function gH(e,t,r){return e===64&&t===64&&jn(r)}var bH=new Set(["break","case","catch","continue","debugger","default","do","else","finally","for","function","if","return","switch","throw","try","var","const","while","with","new","this","super","class","extends","export","import","null","true","false","in","instanceof","typeof","void","delete","implements","interface","let","package","private","protected","public","static","yield","eval","arguments","enum","await"]);function EH(e){return bH.has(e)}var ho=class{constructor(t){this.flags=0,this.names=new Map,this.firstLexicalName="",this.flags=t}},mo=class{constructor(t,r){this.parser=void 0,this.scopeStack=[],this.inModule=void 0,this.undefinedExports=new Map,this.parser=t,this.inModule=r}get inTopLevel(){return(this.currentScope().flags&1)>0}get inFunction(){return(this.currentVarScopeFlags()&2)>0}get allowSuper(){return(this.currentThisScopeFlags()&16)>0}get allowDirectSuper(){return(this.currentThisScopeFlags()&32)>0}get inClass(){return(this.currentThisScopeFlags()&64)>0}get inClassAndNotInNonArrowFunction(){let t=this.currentThisScopeFlags();return(t&64)>0&&(t&2)===0}get inStaticBlock(){for(let t=this.scopeStack.length-1;;t--){let{flags:r}=this.scopeStack[t];if(r&128)return!0;if(r&451)return!1}}get inNonArrowFunction(){return(this.currentThisScopeFlags()&2)>0}get treatFunctionsAsVar(){return this.treatFunctionsAsVarInScope(this.currentScope())}createScope(t){return new ho(t)}enter(t){this.scopeStack.push(this.createScope(t))}exit(){return this.scopeStack.pop().flags}treatFunctionsAsVarInScope(t){return!!(t.flags&130||!this.parser.inModule&&t.flags&1)}declareName(t,r,n){let s=this.currentScope();if(r&8||r&16){this.checkRedeclarationInScope(s,t,r,n);let i=s.names.get(t)||0;r&16?i=i|4:(s.firstLexicalName||(s.firstLexicalName=t),i=i|2),s.names.set(t,i),r&8&&this.maybeExportDefined(s,t)}else if(r&4)for(let i=this.scopeStack.length-1;i>=0&&(s=this.scopeStack[i],this.checkRedeclarationInScope(s,t,r,n),s.names.set(t,(s.names.get(t)||0)|1),this.maybeExportDefined(s,t),!(s.flags&387));--i);this.parser.inModule&&s.flags&1&&this.undefinedExports.delete(t)}maybeExportDefined(t,r){this.parser.inModule&&t.flags&1&&this.undefinedExports.delete(r)}checkRedeclarationInScope(t,r,n,s){this.isRedeclaredInScope(t,r,n)&&this.parser.raise(B.VarRedeclaration,s,{identifierName:r})}isRedeclaredInScope(t,r,n){if(!(n&1))return!1;if(n&8)return t.names.has(r);let s=t.names.get(r);return n&16?(s&2)>0||!this.treatFunctionsAsVarInScope(t)&&(s&1)>0:(s&2)>0&&!(t.flags&8&&t.firstLexicalName===r)||!this.treatFunctionsAsVarInScope(t)&&(s&4)>0}checkLocalExport(t){let{name:r}=t;this.scopeStack[0].names.has(r)||this.undefinedExports.set(r,t.loc.start)}currentScope(){return this.scopeStack[this.scopeStack.length-1]}currentVarScopeFlags(){for(let t=this.scopeStack.length-1;;t--){let{flags:r}=this.scopeStack[t];if(r&387)return r}}currentThisScopeFlags(){for(let t=this.scopeStack.length-1;;t--){let{flags:r}=this.scopeStack[t];if(r&451&&!(r&4))return r}}},im=class extends ho{constructor(...t){super(...t),this.declareFunctions=new Set}},am=class extends mo{createScope(t){return new im(t)}declareName(t,r,n){let s=this.currentScope();if(r&2048){this.checkRedeclarationInScope(s,t,r,n),this.maybeExportDefined(s,t),s.declareFunctions.add(t);return}super.declareName(t,r,n)}isRedeclaredInScope(t,r,n){if(super.isRedeclaredInScope(t,r,n))return!0;if(n&2048&&!t.declareFunctions.has(r)){let s=t.names.get(r);return(s&4)>0||(s&2)>0}return!1}checkLocalExport(t){this.scopeStack[0].declareFunctions.has(t.name)||super.checkLocalExport(t)}},om=class{constructor(){this.sawUnambiguousESM=!1,this.ambiguousScriptDifferentAst=!1}sourceToOffsetPos(t){return t+this.startIndex}offsetToSourcePos(t){return t-this.startIndex}hasPlugin(t){if(typeof t=="string")return this.plugins.has(t);{let[r,n]=t;if(!this.hasPlugin(r))return!1;let s=this.plugins.get(r);for(let i of Object.keys(n))if((s==null?void 0:s[i])!==n[i])return!1;return!0}}getPluginOption(t,r){var n;return(n=this.plugins.get(t))==null?void 0:n[r]}};function nx(e,t){e.trailingComments===void 0?e.trailingComments=t:e.trailingComments.unshift(...t)}function TH(e,t){e.leadingComments===void 0?e.leadingComments=t:e.leadingComments.unshift(...t)}function yo(e,t){e.innerComments===void 0?e.innerComments=t:e.innerComments.unshift(...t)}function ni(e,t,r){let n=null,s=t.length;for(;n===null&&s>0;)n=t[--s];n===null||n.start>r.start?yo(e,r.comments):nx(n,r.comments)}var lm=class extends om{addComment(t){this.filename&&(t.loc.filename=this.filename);let{commentsLen:r}=this.state;this.comments.length!==r&&(this.comments.length=r),this.comments.push(t),this.state.commentsLen++}processComment(t){let{commentStack:r}=this.state,n=r.length;if(n===0)return;let s=n-1,i=r[s];i.start===t.end&&(i.leadingNode=t,s--);let{start:a}=t;for(;s>=0;s--){let o=r[s],l=o.end;if(l>a)o.containingNode=t,this.finalizeComment(o),r.splice(s,1);else{l===a&&(o.trailingNode=t);break}}}finalizeComment(t){let{comments:r}=t;if(t.leadingNode!==null||t.trailingNode!==null)t.leadingNode!==null&&nx(t.leadingNode,r),t.trailingNode!==null&&TH(t.trailingNode,r);else{let{containingNode:n,start:s}=t;if(this.input.charCodeAt(this.offsetToSourcePos(s)-1)===44)switch(n.type){case"ObjectExpression":case"ObjectPattern":case"RecordExpression":ni(n,n.properties,t);break;case"CallExpression":case"OptionalCallExpression":ni(n,n.arguments,t);break;case"FunctionDeclaration":case"FunctionExpression":case"ArrowFunctionExpression":case"ObjectMethod":case"ClassMethod":case"ClassPrivateMethod":ni(n,n.params,t);break;case"ArrayExpression":case"ArrayPattern":case"TupleExpression":ni(n,n.elements,t);break;case"ExportNamedDeclaration":case"ImportDeclaration":ni(n,n.specifiers,t);break;case"TSEnumDeclaration":ni(n,n.members,t);break;case"TSEnumBody":ni(n,n.members,t);break;default:yo(n,r)}else yo(n,r)}}finalizeRemainingComments(){let{commentStack:t}=this.state;for(let r=t.length-1;r>=0;r--)this.finalizeComment(t[r]);this.state.commentStack=[]}resetPreviousNodeTrailingComments(t){let{commentStack:r}=this.state,{length:n}=r;if(n===0)return;let s=r[n-1];s.leadingNode===t&&(s.leadingNode=null)}resetPreviousIdentifierLeadingComments(t){let{commentStack:r}=this.state,{length:n}=r;n!==0&&(r[n-1].trailingNode===t?r[n-1].trailingNode=null:n>=2&&r[n-2].trailingNode===t&&(r[n-2].trailingNode=null))}takeSurroundingComments(t,r,n){let{commentStack:s}=this.state,i=s.length;if(i===0)return;let a=i-1;for(;a>=0;a--){let o=s[a],l=o.end;if(o.start===n)o.leadingNode=t;else if(l===r)o.trailingNode=t;else if(l<r)break}}},SH=/\r\n|[\r\n\u2028\u2029]/,au=new RegExp(SH.source,"g");function Ji(e){switch(e){case 10:case 13:case 8232:case 8233:return!0;default:return!1}}function qS(e,t,r){for(let n=t;n<r;n++)if(Ji(e.charCodeAt(n)))return!0;return!1}var Zh=/(?:\s|\/\/.*|\/\*[^]*?\*\/)*/g,em=/(?:[^\S\n\r\u2028\u2029]|\/\/.*|\/\*.*?\*\/)*/g;function xH(e){switch(e){case 9:case 11:case 12:case 32:case 160:case 5760:case 8192:case 8193:case 8194:case 8195:case 8196:case 8197:case 8198:case 8199:case 8200:case 8201:case 8202:case 8239:case 8287:case 12288:case 65279:return!0;default:return!1}}var um=class e{constructor(){this.flags=1024,this.startIndex=void 0,this.curLine=void 0,this.lineStart=void 0,this.startLoc=void 0,this.endLoc=void 0,this.errors=[],this.potentialArrowAt=-1,this.noArrowAt=[],this.noArrowParamsConversionAt=[],this.topicContext={maxNumOfResolvableTopics:0,maxTopicIndex:null},this.labels=[],this.commentsLen=0,this.commentStack=[],this.pos=0,this.type=140,this.value=null,this.start=0,this.end=0,this.lastTokEndLoc=null,this.lastTokStartLoc=null,this.context=[at.brace],this.firstInvalidTemplateEscapePos=null,this.strictErrors=new Map,this.tokensLength=0}get strict(){return(this.flags&1)>0}set strict(t){t?this.flags|=1:this.flags&=-2}init({strictMode:t,sourceType:r,startIndex:n,startLine:s,startColumn:i}){this.strict=t===!1?!1:t===!0?!0:r==="module",this.startIndex=n,this.curLine=s,this.lineStart=-i,this.startLoc=this.endLoc=new Tn(s,i,n)}get maybeInArrowParameters(){return(this.flags&2)>0}set maybeInArrowParameters(t){t?this.flags|=2:this.flags&=-3}get inType(){return(this.flags&4)>0}set inType(t){t?this.flags|=4:this.flags&=-5}get noAnonFunctionType(){return(this.flags&8)>0}set noAnonFunctionType(t){t?this.flags|=8:this.flags&=-9}get hasFlowComment(){return(this.flags&16)>0}set hasFlowComment(t){t?this.flags|=16:this.flags&=-17}get isAmbientContext(){return(this.flags&32)>0}set isAmbientContext(t){t?this.flags|=32:this.flags&=-33}get inAbstractClass(){return(this.flags&64)>0}set inAbstractClass(t){t?this.flags|=64:this.flags&=-65}get inDisallowConditionalTypesContext(){return(this.flags&128)>0}set inDisallowConditionalTypesContext(t){t?this.flags|=128:this.flags&=-129}get soloAwait(){return(this.flags&256)>0}set soloAwait(t){t?this.flags|=256:this.flags&=-257}get inFSharpPipelineDirectBody(){return(this.flags&512)>0}set inFSharpPipelineDirectBody(t){t?this.flags|=512:this.flags&=-513}get canStartJSXElement(){return(this.flags&1024)>0}set canStartJSXElement(t){t?this.flags|=1024:this.flags&=-1025}get containsEsc(){return(this.flags&2048)>0}set containsEsc(t){t?this.flags|=2048:this.flags&=-2049}get hasTopLevelAwait(){return(this.flags&4096)>0}set hasTopLevelAwait(t){t?this.flags|=4096:this.flags&=-4097}curPosition(){return new Tn(this.curLine,this.pos-this.lineStart,this.pos+this.startIndex)}clone(){let t=new e;return t.flags=this.flags,t.startIndex=this.startIndex,t.curLine=this.curLine,t.lineStart=this.lineStart,t.startLoc=this.startLoc,t.endLoc=this.endLoc,t.errors=this.errors.slice(),t.potentialArrowAt=this.potentialArrowAt,t.noArrowAt=this.noArrowAt.slice(),t.noArrowParamsConversionAt=this.noArrowParamsConversionAt.slice(),t.topicContext=this.topicContext,t.labels=this.labels.slice(),t.commentsLen=this.commentsLen,t.commentStack=this.commentStack.slice(),t.pos=this.pos,t.type=this.type,t.value=this.value,t.start=this.start,t.end=this.end,t.lastTokEndLoc=this.lastTokEndLoc,t.lastTokStartLoc=this.lastTokStartLoc,t.context=this.context.slice(),t.firstInvalidTemplateEscapePos=this.firstInvalidTemplateEscapePos,t.strictErrors=this.strictErrors,t.tokensLength=this.tokensLength,t}},vH=function(t){return t>=48&&t<=57},US={decBinOct:new Set([46,66,69,79,95,98,101,111]),hex:new Set([46,88,95,120])},ou={bin:e=>e===48||e===49,oct:e=>e>=48&&e<=55,dec:e=>e>=48&&e<=57,hex:e=>e>=48&&e<=57||e>=65&&e<=70||e>=97&&e<=102};function VS(e,t,r,n,s,i){let a=r,o=n,l=s,u="",c=null,p=r,{length:d}=t;for(;;){if(r>=d){i.unterminated(a,o,l),u+=t.slice(p,r);break}let y=t.charCodeAt(r);if(PH(e,y,t,r)){u+=t.slice(p,r);break}if(y===92){u+=t.slice(p,r);let E=AH(t,r,n,s,e==="template",i);E.ch===null&&!c?c={pos:r,lineStart:n,curLine:s}:u+=E.ch,{pos:r,lineStart:n,curLine:s}=E,p=r}else y===8232||y===8233?(++r,++s,n=r):y===10||y===13?e==="template"?(u+=t.slice(p,r)+`
`,++r,y===13&&t.charCodeAt(r)===10&&++r,++s,p=n=r):i.unterminated(a,o,l):++r}return{pos:r,str:u,firstInvalidLoc:c,lineStart:n,curLine:s,containsInvalid:!!c}}function PH(e,t,r,n){return e==="template"?t===96||t===36&&r.charCodeAt(n+1)===123:t===(e==="double"?34:39)}function AH(e,t,r,n,s,i){let a=!s;t++;let o=u=>({pos:t,ch:u,lineStart:r,curLine:n}),l=e.charCodeAt(t++);switch(l){case 110:return o(`
`);case 114:return o("\r");case 120:{let u;return{code:u,pos:t}=cm(e,t,r,n,2,!1,a,i),o(u===null?null:String.fromCharCode(u))}case 117:{let u;return{code:u,pos:t}=ix(e,t,r,n,a,i),o(u===null?null:String.fromCodePoint(u))}case 116:return o("	");case 98:return o("\b");case 118:return o("\v");case 102:return o("\f");case 13:e.charCodeAt(t)===10&&++t;case 10:r=t,++n;case 8232:case 8233:return o("");case 56:case 57:if(s)return o(null);i.strictNumericEscape(t-1,r,n);default:if(l>=48&&l<=55){let u=t-1,p=/^[0-7]+/.exec(e.slice(u,t+2))[0],d=parseInt(p,8);d>255&&(p=p.slice(0,-1),d=parseInt(p,8)),t+=p.length-1;let y=e.charCodeAt(t);if(p!=="0"||y===56||y===57){if(s)return o(null);i.strictNumericEscape(u,r,n)}return o(String.fromCharCode(d))}return o(String.fromCharCode(l))}}function cm(e,t,r,n,s,i,a,o){let l=t,u;return{n:u,pos:t}=sx(e,t,r,n,16,s,i,!1,o,!a),u===null&&(a?o.invalidEscapeSequence(l,r,n):t=l-1),{code:u,pos:t}}function sx(e,t,r,n,s,i,a,o,l,u){let c=t,p=s===16?US.hex:US.decBinOct,d=s===16?ou.hex:s===10?ou.dec:s===8?ou.oct:ou.bin,y=!1,E=0;for(let f=0,m=i==null?1/0:i;f<m;++f){let g=e.charCodeAt(t),D;if(g===95&&o!=="bail"){let _=e.charCodeAt(t-1),O=e.charCodeAt(t+1);if(o){if(Number.isNaN(O)||!d(O)||p.has(_)||p.has(O)){if(u)return{n:null,pos:t};l.unexpectedNumericSeparator(t,r,n)}}else{if(u)return{n:null,pos:t};l.numericSeparatorInEscapeSequence(t,r,n)}++t;continue}if(g>=97?D=g-97+10:g>=65?D=g-65+10:vH(g)?D=g-48:D=1/0,D>=s){if(D<=9&&u)return{n:null,pos:t};if(D<=9&&l.invalidDigit(t,r,n,s))D=0;else if(a)D=0,y=!0;else break}++t,E=E*s+D}return t===c||i!=null&&t-c!==i||y?{n:null,pos:t}:{n:E,pos:t}}function ix(e,t,r,n,s,i){let a=e.charCodeAt(t),o;if(a===123){if(++t,{code:o,pos:t}=cm(e,t,r,n,e.indexOf("}",t)-t,!0,s,i),++t,o!==null&&o>1114111)if(s)i.invalidCodePoint(t,r,n);else return{code:null,pos:t}}else({code:o,pos:t}=cm(e,t,r,n,4,!1,s,i));return{code:o,pos:t}}function co(e,t,r){return new Tn(r,e-t,e)}var CH=new Set([103,109,115,105,121,117,100,118]),En=class{constructor(t){let r=t.startIndex||0;this.type=t.type,this.value=t.value,this.start=r+t.start,this.end=r+t.end,this.loc=new Qi(t.startLoc,t.endLoc)}},pm=class extends lm{constructor(t,r){super(),this.isLookahead=void 0,this.tokens=[],this.errorHandlers_readInt={invalidDigit:(n,s,i,a)=>this.optionFlags&1024?(this.raise(B.InvalidDigit,co(n,s,i),{radix:a}),!0):!1,numericSeparatorInEscapeSequence:this.errorBuilder(B.NumericSeparatorInEscapeSequence),unexpectedNumericSeparator:this.errorBuilder(B.UnexpectedNumericSeparator)},this.errorHandlers_readCodePoint=Object.assign({},this.errorHandlers_readInt,{invalidEscapeSequence:this.errorBuilder(B.InvalidEscapeSequence),invalidCodePoint:this.errorBuilder(B.InvalidCodePoint)}),this.errorHandlers_readStringContents_string=Object.assign({},this.errorHandlers_readCodePoint,{strictNumericEscape:(n,s,i)=>{this.recordStrictModeErrors(B.StrictNumericEscape,co(n,s,i))},unterminated:(n,s,i)=>{throw this.raise(B.UnterminatedString,co(n-1,s,i))}}),this.errorHandlers_readStringContents_template=Object.assign({},this.errorHandlers_readCodePoint,{strictNumericEscape:this.errorBuilder(B.StrictNumericEscape),unterminated:(n,s,i)=>{throw this.raise(B.UnterminatedTemplate,co(n,s,i))}}),this.state=new um,this.state.init(t),this.input=r,this.length=r.length,this.comments=[],this.isLookahead=!1}pushToken(t){this.tokens.length=this.state.tokensLength,this.tokens.push(t),++this.state.tokensLength}next(){this.checkKeywordEscapes(),this.optionFlags&128&&this.pushToken(new En(this.state)),this.state.lastTokEndLoc=this.state.endLoc,this.state.lastTokStartLoc=this.state.startLoc,this.nextToken()}eat(t){return this.match(t)?(this.next(),!0):!1}match(t){return this.state.type===t}createLookaheadState(t){return{pos:t.pos,value:null,type:t.type,start:t.start,end:t.end,context:[this.curContext()],inType:t.inType,startLoc:t.startLoc,lastTokEndLoc:t.lastTokEndLoc,curLine:t.curLine,lineStart:t.lineStart,curPosition:t.curPosition}}lookahead(){let t=this.state;this.state=this.createLookaheadState(t),this.isLookahead=!0,this.nextToken(),this.isLookahead=!1;let r=this.state;return this.state=t,r}nextTokenStart(){return this.nextTokenStartSince(this.state.pos)}nextTokenStartSince(t){return Zh.lastIndex=t,Zh.test(this.input)?Zh.lastIndex:t}lookaheadCharCode(){return this.input.charCodeAt(this.nextTokenStart())}nextTokenInLineStart(){return this.nextTokenInLineStartSince(this.state.pos)}nextTokenInLineStartSince(t){return em.lastIndex=t,em.test(this.input)?em.lastIndex:t}lookaheadInLineCharCode(){return this.input.charCodeAt(this.nextTokenInLineStart())}codePointAtPos(t){let r=this.input.charCodeAt(t);if((r&64512)===55296&&++t<this.input.length){let n=this.input.charCodeAt(t);(n&64512)===56320&&(r=65536+((r&1023)<<10)+(n&1023))}return r}setStrict(t){this.state.strict=t,t&&(this.state.strictErrors.forEach(([r,n])=>this.raise(r,n)),this.state.strictErrors.clear())}curContext(){return this.state.context[this.state.context.length-1]}nextToken(){if(this.skipSpace(),this.state.start=this.state.pos,this.isLookahead||(this.state.startLoc=this.state.curPosition()),this.state.pos>=this.length){this.finishToken(140);return}this.getTokenFromCode(this.codePointAtPos(this.state.pos))}skipBlockComment(t){let r;this.isLookahead||(r=this.state.curPosition());let n=this.state.pos,s=this.input.indexOf(t,n+2);if(s===-1)throw this.raise(B.UnterminatedComment,this.state.curPosition());for(this.state.pos=s+t.length,au.lastIndex=n+2;au.test(this.input)&&au.lastIndex<=s;)++this.state.curLine,this.state.lineStart=au.lastIndex;if(this.isLookahead)return;let i={type:"CommentBlock",value:this.input.slice(n+2,s),start:this.sourceToOffsetPos(n),end:this.sourceToOffsetPos(s+t.length),loc:new Qi(r,this.state.curPosition())};return this.optionFlags&128&&this.pushToken(i),i}skipLineComment(t){let r=this.state.pos,n;this.isLookahead||(n=this.state.curPosition());let s=this.input.charCodeAt(this.state.pos+=t);if(this.state.pos<this.length)for(;!Ji(s)&&++this.state.pos<this.length;)s=this.input.charCodeAt(this.state.pos);if(this.isLookahead)return;let i=this.state.pos,o={type:"CommentLine",value:this.input.slice(r+t,i),start:this.sourceToOffsetPos(r),end:this.sourceToOffsetPos(i),loc:new Qi(n,this.state.curPosition())};return this.optionFlags&128&&this.pushToken(o),o}skipSpace(){let t=this.state.pos,r=this.optionFlags&2048?[]:null;e:for(;this.state.pos<this.length;){let n=this.input.charCodeAt(this.state.pos);switch(n){case 32:case 160:case 9:++this.state.pos;break;case 13:this.input.charCodeAt(this.state.pos+1)===10&&++this.state.pos;case 10:case 8232:case 8233:++this.state.pos,++this.state.curLine,this.state.lineStart=this.state.pos;break;case 47:switch(this.input.charCodeAt(this.state.pos+1)){case 42:{let s=this.skipBlockComment("*/");s!==void 0&&(this.addComment(s),r==null||r.push(s));break}case 47:{let s=this.skipLineComment(2);s!==void 0&&(this.addComment(s),r==null||r.push(s));break}default:break e}break;default:if(xH(n))++this.state.pos;else if(n===45&&!this.inModule&&this.optionFlags&4096){let s=this.state.pos;if(this.input.charCodeAt(s+1)===45&&this.input.charCodeAt(s+2)===62&&(t===0||this.state.lineStart>t)){let i=this.skipLineComment(3);i!==void 0&&(this.addComment(i),r==null||r.push(i))}else break e}else if(n===60&&!this.inModule&&this.optionFlags&4096){let s=this.state.pos;if(this.input.charCodeAt(s+1)===33&&this.input.charCodeAt(s+2)===45&&this.input.charCodeAt(s+3)===45){let i=this.skipLineComment(4);i!==void 0&&(this.addComment(i),r==null||r.push(i))}else break e}else break e}}if((r==null?void 0:r.length)>0){let n=this.state.pos,s={start:this.sourceToOffsetPos(t),end:this.sourceToOffsetPos(n),comments:r,leadingNode:null,trailingNode:null,containingNode:null};this.state.commentStack.push(s)}}finishToken(t,r){this.state.end=this.state.pos,this.state.endLoc=this.state.curPosition();let n=this.state.type;this.state.type=t,this.state.value=r,this.isLookahead||this.updateContext(n)}replaceToken(t){this.state.type=t,this.updateContext()}readToken_numberSign(){if(this.state.pos===0&&this.readToken_interpreter())return;let t=this.state.pos+1,r=this.codePointAtPos(t);if(r>=48&&r<=57)throw this.raise(B.UnexpectedDigitAfterHash,this.state.curPosition());if(r===123||r===91&&this.hasPlugin("recordAndTuple")){if(this.expectPlugin("recordAndTuple"),this.getPluginOption("recordAndTuple","syntaxType")==="bar")throw this.raise(r===123?B.RecordExpressionHashIncorrectStartSyntaxType:B.TupleExpressionHashIncorrectStartSyntaxType,this.state.curPosition());this.state.pos+=2,r===123?this.finishToken(7):this.finishToken(1)}else jn(r)?(++this.state.pos,this.finishToken(139,this.readWord1(r))):r===92?(++this.state.pos,this.finishToken(139,this.readWord1())):this.finishOp(27,1)}readToken_dot(){let t=this.input.charCodeAt(this.state.pos+1);if(t>=48&&t<=57){this.readNumber(!0);return}t===46&&this.input.charCodeAt(this.state.pos+2)===46?(this.state.pos+=3,this.finishToken(21)):(++this.state.pos,this.finishToken(16))}readToken_slash(){this.input.charCodeAt(this.state.pos+1)===61?this.finishOp(31,2):this.finishOp(56,1)}readToken_interpreter(){if(this.state.pos!==0||this.length<2)return!1;let t=this.input.charCodeAt(this.state.pos+1);if(t!==33)return!1;let r=this.state.pos;for(this.state.pos+=1;!Ji(t)&&++this.state.pos<this.length;)t=this.input.charCodeAt(this.state.pos);let n=this.input.slice(r+2,this.state.pos);return this.finishToken(28,n),!0}readToken_mult_modulo(t){let r=t===42?55:54,n=1,s=this.input.charCodeAt(this.state.pos+1);t===42&&s===42&&(n++,s=this.input.charCodeAt(this.state.pos+2),r=57),s===61&&!this.state.inType&&(n++,r=t===37?33:30),this.finishOp(r,n)}readToken_pipe_amp(t){let r=this.input.charCodeAt(this.state.pos+1);if(r===t){this.input.charCodeAt(this.state.pos+2)===61?this.finishOp(30,3):this.finishOp(t===124?41:42,2);return}if(t===124){if(r===62){this.finishOp(39,2);return}if(this.hasPlugin("recordAndTuple")&&r===125){if(this.getPluginOption("recordAndTuple","syntaxType")!=="bar")throw this.raise(B.RecordExpressionBarIncorrectEndSyntaxType,this.state.curPosition());this.state.pos+=2,this.finishToken(9);return}if(this.hasPlugin("recordAndTuple")&&r===93){if(this.getPluginOption("recordAndTuple","syntaxType")!=="bar")throw this.raise(B.TupleExpressionBarIncorrectEndSyntaxType,this.state.curPosition());this.state.pos+=2,this.finishToken(4);return}}if(r===61){this.finishOp(30,2);return}this.finishOp(t===124?43:45,1)}readToken_caret(){let t=this.input.charCodeAt(this.state.pos+1);t===61&&!this.state.inType?this.finishOp(32,2):t===94&&this.hasPlugin(["pipelineOperator",{proposal:"hack",topicToken:"^^"}])?(this.finishOp(37,2),this.input.codePointAt(this.state.pos)===94&&this.unexpected()):this.finishOp(44,1)}readToken_atSign(){this.input.charCodeAt(this.state.pos+1)===64&&this.hasPlugin(["pipelineOperator",{proposal:"hack",topicToken:"@@"}])?this.finishOp(38,2):this.finishOp(26,1)}readToken_plus_min(t){let r=this.input.charCodeAt(this.state.pos+1);if(r===t){this.finishOp(34,2);return}r===61?this.finishOp(30,2):this.finishOp(53,1)}readToken_lt(){let{pos:t}=this.state,r=this.input.charCodeAt(t+1);if(r===60){if(this.input.charCodeAt(t+2)===61){this.finishOp(30,3);return}this.finishOp(51,2);return}if(r===61){this.finishOp(49,2);return}this.finishOp(47,1)}readToken_gt(){let{pos:t}=this.state,r=this.input.charCodeAt(t+1);if(r===62){let n=this.input.charCodeAt(t+2)===62?3:2;if(this.input.charCodeAt(t+n)===61){this.finishOp(30,n+1);return}this.finishOp(52,n);return}if(r===61){this.finishOp(49,2);return}this.finishOp(48,1)}readToken_eq_excl(t){let r=this.input.charCodeAt(this.state.pos+1);if(r===61){this.finishOp(46,this.input.charCodeAt(this.state.pos+2)===61?3:2);return}if(t===61&&r===62){this.state.pos+=2,this.finishToken(19);return}this.finishOp(t===61?29:35,1)}readToken_question(){let t=this.input.charCodeAt(this.state.pos+1),r=this.input.charCodeAt(this.state.pos+2);t===63?r===61?this.finishOp(30,3):this.finishOp(40,2):t===46&&!(r>=48&&r<=57)?(this.state.pos+=2,this.finishToken(18)):(++this.state.pos,this.finishToken(17))}getTokenFromCode(t){switch(t){case 46:this.readToken_dot();return;case 40:++this.state.pos,this.finishToken(10);return;case 41:++this.state.pos,this.finishToken(11);return;case 59:++this.state.pos,this.finishToken(13);return;case 44:++this.state.pos,this.finishToken(12);return;case 91:if(this.hasPlugin("recordAndTuple")&&this.input.charCodeAt(this.state.pos+1)===124){if(this.getPluginOption("recordAndTuple","syntaxType")!=="bar")throw this.raise(B.TupleExpressionBarIncorrectStartSyntaxType,this.state.curPosition());this.state.pos+=2,this.finishToken(2)}else++this.state.pos,this.finishToken(0);return;case 93:++this.state.pos,this.finishToken(3);return;case 123:if(this.hasPlugin("recordAndTuple")&&this.input.charCodeAt(this.state.pos+1)===124){if(this.getPluginOption("recordAndTuple","syntaxType")!=="bar")throw this.raise(B.RecordExpressionBarIncorrectStartSyntaxType,this.state.curPosition());this.state.pos+=2,this.finishToken(6)}else++this.state.pos,this.finishToken(5);return;case 125:++this.state.pos,this.finishToken(8);return;case 58:this.hasPlugin("functionBind")&&this.input.charCodeAt(this.state.pos+1)===58?this.finishOp(15,2):(++this.state.pos,this.finishToken(14));return;case 63:this.readToken_question();return;case 96:this.readTemplateToken();return;case 48:{let r=this.input.charCodeAt(this.state.pos+1);if(r===120||r===88){this.readRadixNumber(16);return}if(r===111||r===79){this.readRadixNumber(8);return}if(r===98||r===66){this.readRadixNumber(2);return}}case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:this.readNumber(!1);return;case 34:case 39:this.readString(t);return;case 47:this.readToken_slash();return;case 37:case 42:this.readToken_mult_modulo(t);return;case 124:case 38:this.readToken_pipe_amp(t);return;case 94:this.readToken_caret();return;case 43:case 45:this.readToken_plus_min(t);return;case 60:this.readToken_lt();return;case 62:this.readToken_gt();return;case 61:case 33:this.readToken_eq_excl(t);return;case 126:this.finishOp(36,1);return;case 64:this.readToken_atSign();return;case 35:this.readToken_numberSign();return;case 92:this.readWord();return;default:if(jn(t)){this.readWord(t);return}}throw this.raise(B.InvalidOrUnexpectedToken,this.state.curPosition(),{unexpected:String.fromCodePoint(t)})}finishOp(t,r){let n=this.input.slice(this.state.pos,this.state.pos+r);this.state.pos+=r,this.finishToken(t,n)}readRegexp(){let t=this.state.startLoc,r=this.state.start+1,n,s,{pos:i}=this.state;for(;;++i){if(i>=this.length)throw this.raise(B.UnterminatedRegExp,gr(t,1));let u=this.input.charCodeAt(i);if(Ji(u))throw this.raise(B.UnterminatedRegExp,gr(t,1));if(n)n=!1;else{if(u===91)s=!0;else if(u===93&&s)s=!1;else if(u===47&&!s)break;n=u===92}}let a=this.input.slice(r,i);++i;let o="",l=()=>gr(t,i+2-r);for(;i<this.length;){let u=this.codePointAtPos(i),c=String.fromCharCode(u);if(CH.has(u))u===118?o.includes("u")&&this.raise(B.IncompatibleRegExpUVFlags,l()):u===117&&o.includes("v")&&this.raise(B.IncompatibleRegExpUVFlags,l()),o.includes(c)&&this.raise(B.DuplicateRegExpFlags,l());else if(Xi(u)||u===92)this.raise(B.MalformedRegExpFlags,l());else break;++i,o+=c}this.state.pos=i,this.finishToken(138,{pattern:a,flags:o})}readInt(t,r,n=!1,s=!0){let{n:i,pos:a}=sx(this.input,this.state.pos,this.state.lineStart,this.state.curLine,t,r,n,s,this.errorHandlers_readInt,!1);return this.state.pos=a,i}readRadixNumber(t){let r=this.state.pos,n=this.state.curPosition(),s=!1;this.state.pos+=2;let i=this.readInt(t);i==null&&this.raise(B.InvalidDigit,gr(n,2),{radix:t});let a=this.input.charCodeAt(this.state.pos);if(a===110)++this.state.pos,s=!0;else if(a===109)throw this.raise(B.InvalidDecimal,n);if(jn(this.codePointAtPos(this.state.pos)))throw this.raise(B.NumberIdentifier,this.state.curPosition());if(s){let o=this.input.slice(r,this.state.pos).replace(/[_n]/g,"");this.finishToken(136,o);return}this.finishToken(135,i)}readNumber(t){let r=this.state.pos,n=this.state.curPosition(),s=!1,i=!1,a=!1,o=!1;!t&&this.readInt(10)===null&&this.raise(B.InvalidNumber,this.state.curPosition());let l=this.state.pos-r>=2&&this.input.charCodeAt(r)===48;if(l){let y=this.input.slice(r,this.state.pos);if(this.recordStrictModeErrors(B.StrictOctalLiteral,n),!this.state.strict){let E=y.indexOf("_");E>0&&this.raise(B.ZeroDigitNumericSeparator,gr(n,E))}o=l&&!/[89]/.test(y)}let u=this.input.charCodeAt(this.state.pos);if(u===46&&!o&&(++this.state.pos,this.readInt(10),s=!0,u=this.input.charCodeAt(this.state.pos)),(u===69||u===101)&&!o&&(u=this.input.charCodeAt(++this.state.pos),(u===43||u===45)&&++this.state.pos,this.readInt(10)===null&&this.raise(B.InvalidOrMissingExponent,n),s=!0,a=!0,u=this.input.charCodeAt(this.state.pos)),u===110&&((s||l)&&this.raise(B.InvalidBigIntLiteral,n),++this.state.pos,i=!0),u===109){this.expectPlugin("decimal",this.state.curPosition()),(a||l)&&this.raise(B.InvalidDecimal,n),++this.state.pos;var c=!0}if(jn(this.codePointAtPos(this.state.pos)))throw this.raise(B.NumberIdentifier,this.state.curPosition());let p=this.input.slice(r,this.state.pos).replace(/[_mn]/g,"");if(i){this.finishToken(136,p);return}if(c){this.finishToken(137,p);return}let d=o?parseInt(p,8):parseFloat(p);this.finishToken(135,d)}readCodePoint(t){let{code:r,pos:n}=ix(this.input,this.state.pos,this.state.lineStart,this.state.curLine,t,this.errorHandlers_readCodePoint);return this.state.pos=n,r}readString(t){let{str:r,pos:n,curLine:s,lineStart:i}=VS(t===34?"double":"single",this.input,this.state.pos+1,this.state.lineStart,this.state.curLine,this.errorHandlers_readStringContents_string);this.state.pos=n+1,this.state.lineStart=i,this.state.curLine=s,this.finishToken(134,r)}readTemplateContinuation(){this.match(8)||this.unexpected(null,8),this.state.pos--,this.readTemplateToken()}readTemplateToken(){let t=this.input[this.state.pos],{str:r,firstInvalidLoc:n,pos:s,curLine:i,lineStart:a}=VS("template",this.input,this.state.pos+1,this.state.lineStart,this.state.curLine,this.errorHandlers_readStringContents_template);this.state.pos=s+1,this.state.lineStart=a,this.state.curLine=i,n&&(this.state.firstInvalidTemplateEscapePos=new Tn(n.curLine,n.pos-n.lineStart,this.sourceToOffsetPos(n.pos))),this.input.codePointAt(s)===96?this.finishToken(24,n?null:t+r+"`"):(this.state.pos++,this.finishToken(25,n?null:t+r+"${"))}recordStrictModeErrors(t,r){let n=r.index;this.state.strict&&!this.state.strictErrors.has(n)?this.raise(t,r):this.state.strictErrors.set(n,[t,r])}readWord1(t){this.state.containsEsc=!1;let r="",n=this.state.pos,s=this.state.pos;for(t!==void 0&&(this.state.pos+=t<=65535?1:2);this.state.pos<this.length;){let i=this.codePointAtPos(this.state.pos);if(Xi(i))this.state.pos+=i<=65535?1:2;else if(i===92){this.state.containsEsc=!0,r+=this.input.slice(s,this.state.pos);let a=this.state.curPosition(),o=this.state.pos===n?jn:Xi;if(this.input.charCodeAt(++this.state.pos)!==117){this.raise(B.MissingUnicodeEscape,this.state.curPosition()),s=this.state.pos-1;continue}++this.state.pos;let l=this.readCodePoint(!0);l!==null&&(o(l)||this.raise(B.EscapedCharNotAnIdentifier,a),r+=String.fromCodePoint(l)),s=this.state.pos}else break}return r+this.input.slice(s,this.state.pos)}readWord(t){let r=this.readWord1(t),n=vm.get(r);n!==void 0?this.finishToken(n,Ss(n)):this.finishToken(132,r)}checkKeywordEscapes(){let{type:t}=this.state;Im(t)&&this.state.containsEsc&&this.raise(B.InvalidEscapedReservedWord,this.state.startLoc,{reservedWord:Ss(t)})}raise(t,r,n={}){let s=r instanceof Tn?r:r.loc.start,i=t(s,n);if(!(this.optionFlags&1024))throw i;return this.isLookahead||this.state.errors.push(i),i}raiseOverwrite(t,r,n={}){let s=r instanceof Tn?r:r.loc.start,i=s.index,a=this.state.errors;for(let o=a.length-1;o>=0;o--){let l=a[o];if(l.loc.index===i)return a[o]=t(s,n);if(l.loc.index<i)break}return this.raise(t,r,n)}updateContext(t){}unexpected(t,r){throw this.raise(B.UnexpectedToken,t!=null?t:this.state.startLoc,{expected:r?Ss(r):null})}expectPlugin(t,r){if(this.hasPlugin(t))return!0;throw this.raise(B.MissingPlugin,r!=null?r:this.state.startLoc,{missingPlugin:[t]})}expectOnePlugin(t){if(!t.some(r=>this.hasPlugin(r)))throw this.raise(B.MissingOneOfPlugins,this.state.startLoc,{missingPlugin:t})}errorBuilder(t){return(r,n,s)=>{this.raise(t,co(r,n,s))}}},fm=class{constructor(){this.privateNames=new Set,this.loneAccessors=new Map,this.undefinedPrivateNames=new Map}},dm=class{constructor(t){this.parser=void 0,this.stack=[],this.undefinedPrivateNames=new Map,this.parser=t}current(){return this.stack[this.stack.length-1]}enter(){this.stack.push(new fm)}exit(){let t=this.stack.pop(),r=this.current();for(let[n,s]of Array.from(t.undefinedPrivateNames))r?r.undefinedPrivateNames.has(n)||r.undefinedPrivateNames.set(n,s):this.parser.raise(B.InvalidPrivateFieldResolution,s,{identifierName:n})}declarePrivateName(t,r,n){let{privateNames:s,loneAccessors:i,undefinedPrivateNames:a}=this.current(),o=s.has(t);if(r&3){let l=o&&i.get(t);if(l){let u=l&4,c=r&4,p=l&3,d=r&3;o=p===d||u!==c,o||i.delete(t)}else o||i.set(t,r)}o&&this.parser.raise(B.PrivateNameRedeclaration,n,{identifierName:t}),s.add(t),a.delete(t)}usePrivateName(t,r){let n;for(n of this.stack)if(n.privateNames.has(t))return;n?n.undefinedPrivateNames.set(t,r):this.parser.raise(B.InvalidPrivateFieldResolution,r,{identifierName:t})}},Zi=class{constructor(t=0){this.type=t}canBeArrowParameterDeclaration(){return this.type===2||this.type===1}isCertainlyParameterDeclaration(){return this.type===3}},du=class extends Zi{constructor(t){super(t),this.declarationErrors=new Map}recordDeclarationError(t,r){let n=r.index;this.declarationErrors.set(n,[t,r])}clearDeclarationError(t){this.declarationErrors.delete(t)}iterateErrors(t){this.declarationErrors.forEach(t)}},hm=class{constructor(t){this.parser=void 0,this.stack=[new Zi],this.parser=t}enter(t){this.stack.push(t)}exit(){this.stack.pop()}recordParameterInitializerError(t,r){let n=r.loc.start,{stack:s}=this,i=s.length-1,a=s[i];for(;!a.isCertainlyParameterDeclaration();){if(a.canBeArrowParameterDeclaration())a.recordDeclarationError(t,n);else return;a=s[--i]}this.parser.raise(t,n)}recordArrowParameterBindingError(t,r){let{stack:n}=this,s=n[n.length-1],i=r.loc.start;if(s.isCertainlyParameterDeclaration())this.parser.raise(t,i);else if(s.canBeArrowParameterDeclaration())s.recordDeclarationError(t,i);else return}recordAsyncArrowParametersError(t){let{stack:r}=this,n=r.length-1,s=r[n];for(;s.canBeArrowParameterDeclaration();)s.type===2&&s.recordDeclarationError(B.AwaitBindingIdentifier,t),s=r[--n]}validateAsPattern(){let{stack:t}=this,r=t[t.length-1];r.canBeArrowParameterDeclaration()&&r.iterateErrors(([n,s])=>{this.parser.raise(n,s);let i=t.length-2,a=t[i];for(;a.canBeArrowParameterDeclaration();)a.clearDeclarationError(s.index),a=t[--i]})}};function DH(){return new Zi(3)}function wH(){return new du(1)}function IH(){return new du(2)}function ax(){return new Zi}var mm=class{constructor(){this.stacks=[]}enter(t){this.stacks.push(t)}exit(){this.stacks.pop()}currentFlags(){return this.stacks[this.stacks.length-1]}get hasAwait(){return(this.currentFlags()&2)>0}get hasYield(){return(this.currentFlags()&1)>0}get hasReturn(){return(this.currentFlags()&4)>0}get hasIn(){return(this.currentFlags()&8)>0}};function cu(e,t){return(e?2:0)|(t?1:0)}var ym=class extends pm{addExtra(t,r,n,s=!0){if(!t)return;let{extra:i}=t;i==null&&(i={},t.extra=i),s?i[r]=n:Object.defineProperty(i,r,{enumerable:s,value:n})}isContextual(t){return this.state.type===t&&!this.state.containsEsc}isUnparsedContextual(t,r){let n=t+r.length;if(this.input.slice(t,n)===r){let s=this.input.charCodeAt(n);return!(Xi(s)||(s&64512)===55296)}return!1}isLookaheadContextual(t){let r=this.nextTokenStart();return this.isUnparsedContextual(r,t)}eatContextual(t){return this.isContextual(t)?(this.next(),!0):!1}expectContextual(t,r){if(!this.eatContextual(t)){if(r!=null)throw this.raise(r,this.state.startLoc);this.unexpected(null,t)}}canInsertSemicolon(){return this.match(140)||this.match(8)||this.hasPrecedingLineBreak()}hasPrecedingLineBreak(){return qS(this.input,this.offsetToSourcePos(this.state.lastTokEndLoc.index),this.state.start)}hasFollowingLineBreak(){return qS(this.input,this.state.end,this.nextTokenStart())}isLineTerminator(){return this.eat(13)||this.canInsertSemicolon()}semicolon(t=!0){(t?this.isLineTerminator():this.eat(13))||this.raise(B.MissingSemicolon,this.state.lastTokEndLoc)}expect(t,r){this.eat(t)||this.unexpected(r,t)}tryParse(t,r=this.state.clone()){let n={node:null};try{let s=t((i=null)=>{throw n.node=i,n});if(this.state.errors.length>r.errors.length){let i=this.state;return this.state=r,this.state.tokensLength=i.tokensLength,{node:s,error:i.errors[r.errors.length],thrown:!1,aborted:!1,failState:i}}return{node:s,error:null,thrown:!1,aborted:!1,failState:null}}catch(s){let i=this.state;if(this.state=r,s instanceof SyntaxError)return{node:null,error:s,thrown:!0,aborted:!1,failState:i};if(s===n)return{node:n.node,error:null,thrown:!1,aborted:!0,failState:i};throw s}}checkExpressionErrors(t,r){if(!t)return!1;let{shorthandAssignLoc:n,doubleProtoLoc:s,privateKeyLoc:i,optionalParametersLoc:a}=t,o=!!n||!!s||!!a||!!i;if(!r)return o;n!=null&&this.raise(B.InvalidCoverInitializedName,n),s!=null&&this.raise(B.DuplicateProto,s),i!=null&&this.raise(B.UnexpectedPrivateField,i),a!=null&&this.unexpected(a)}isLiteralPropertyName(){return JS(this.state.type)}isPrivateName(t){return t.type==="PrivateName"}getPrivateNameSV(t){return t.id.name}hasPropertyAsPrivateName(t){return(t.type==="MemberExpression"||t.type==="OptionalMemberExpression")&&this.isPrivateName(t.property)}isObjectProperty(t){return t.type==="ObjectProperty"}isObjectMethod(t){return t.type==="ObjectMethod"}initializeScopes(t=this.options.sourceType==="module"){let r=this.state.labels;this.state.labels=[];let n=this.exportedIdentifiers;this.exportedIdentifiers=new Set;let s=this.inModule;this.inModule=t;let i=this.scope,a=this.getScopeHandler();this.scope=new a(this,t);let o=this.prodParam;this.prodParam=new mm;let l=this.classScope;this.classScope=new dm(this);let u=this.expressionScope;return this.expressionScope=new hm(this),()=>{this.state.labels=r,this.exportedIdentifiers=n,this.inModule=s,this.scope=i,this.prodParam=o,this.classScope=l,this.expressionScope=u}}enterInitialScopes(){let t=0;this.inModule&&(t|=2),this.scope.enter(1),this.prodParam.enter(t)}checkDestructuringPrivate(t){let{privateKeyLoc:r}=t;r!==null&&this.expectPlugin("destructuringPrivate",r)}},zi=class{constructor(){this.shorthandAssignLoc=null,this.doubleProtoLoc=null,this.privateKeyLoc=null,this.optionalParametersLoc=null}},ea=class{constructor(t,r,n){this.type="",this.start=r,this.end=0,this.loc=new Qi(n),(t==null?void 0:t.optionFlags)&64&&(this.range=[r,0]),t!=null&&t.filename&&(this.loc.filename=t.filename)}},Nm=ea.prototype;Nm.__clone=function(){let e=new ea(void 0,this.start,this.loc.start),t=Object.keys(this);for(let r=0,n=t.length;r<n;r++){let s=t[r];s!=="leadingComments"&&s!=="trailingComments"&&s!=="innerComments"&&(e[s]=this[s])}return e};function _H(e){return Rn(e)}function Rn(e){let{type:t,start:r,end:n,loc:s,range:i,extra:a,name:o}=e,l=Object.create(Nm);return l.type=t,l.start=r,l.end=n,l.loc=s,l.range=i,l.extra=a,l.name=o,t==="Placeholder"&&(l.expectedNode=e.expectedNode),l}function OH(e){let{type:t,start:r,end:n,loc:s,range:i,extra:a}=e;if(t==="Placeholder")return _H(e);let o=Object.create(Nm);return o.type=t,o.start=r,o.end=n,o.loc=s,o.range=i,e.raw!==void 0?o.raw=e.raw:o.extra=a,o.value=e.value,o}var gm=class extends ym{startNode(){let t=this.state.startLoc;return new ea(this,t.index,t)}startNodeAt(t){return new ea(this,t.index,t)}startNodeAtNode(t){return this.startNodeAt(t.loc.start)}finishNode(t,r){return this.finishNodeAt(t,r,this.state.lastTokEndLoc)}finishNodeAt(t,r,n){return t.type=r,t.end=n.index,t.loc.end=n,this.optionFlags&64&&(t.range[1]=n.index),this.optionFlags&2048&&this.processComment(t),t}resetStartLocation(t,r){t.start=r.index,t.loc.start=r,this.optionFlags&64&&(t.range[0]=r.index)}resetEndLocation(t,r=this.state.lastTokEndLoc){t.end=r.index,t.loc.end=r,this.optionFlags&64&&(t.range[1]=r.index)}resetStartLocationFromNode(t,r){this.resetStartLocation(t,r.loc.start)}},NH=new Set(["_","any","bool","boolean","empty","extends","false","interface","mixed","null","number","static","string","true","typeof","void"]),De=Mn`flow`({AmbiguousConditionalArrow:"Ambiguous expression: wrap the arrow functions in parentheses to disambiguate.",AmbiguousDeclareModuleKind:"Found both `declare module.exports` and `declare export` in the same module. Modules can only have 1 since they are either an ES module or they are a CommonJS module.",AssignReservedType:({reservedType:e})=>`Cannot overwrite reserved type ${e}.`,DeclareClassElement:"The `declare` modifier can only appear on class fields.",DeclareClassFieldInitializer:"Initializers are not allowed in fields with the `declare` modifier.",DuplicateDeclareModuleExports:"Duplicate `declare module.exports` statement.",EnumBooleanMemberNotInitialized:({memberName:e,enumName:t})=>`Boolean enum members need to be initialized. Use either \`${e} = true,\` or \`${e} = false,\` in enum \`${t}\`.`,EnumDuplicateMemberName:({memberName:e,enumName:t})=>`Enum member names need to be unique, but the name \`${e}\` has already been used before in enum \`${t}\`.`,EnumInconsistentMemberValues:({enumName:e})=>`Enum \`${e}\` has inconsistent member initializers. Either use no initializers, or consistently use literals (either booleans, numbers, or strings) for all member initializers.`,EnumInvalidExplicitType:({invalidEnumType:e,enumName:t})=>`Enum type \`${e}\` is not valid. Use one of \`boolean\`, \`number\`, \`string\`, or \`symbol\` in enum \`${t}\`.`,EnumInvalidExplicitTypeUnknownSupplied:({enumName:e})=>`Supplied enum type is not valid. Use one of \`boolean\`, \`number\`, \`string\`, or \`symbol\` in enum \`${e}\`.`,EnumInvalidMemberInitializerPrimaryType:({enumName:e,memberName:t,explicitType:r})=>`Enum \`${e}\` has type \`${r}\`, so the initializer of \`${t}\` needs to be a ${r} literal.`,EnumInvalidMemberInitializerSymbolType:({enumName:e,memberName:t})=>`Symbol enum members cannot be initialized. Use \`${t},\` in enum \`${e}\`.`,EnumInvalidMemberInitializerUnknownType:({enumName:e,memberName:t})=>`The enum member initializer for \`${t}\` needs to be a literal (either a boolean, number, or string) in enum \`${e}\`.`,EnumInvalidMemberName:({enumName:e,memberName:t,suggestion:r})=>`Enum member names cannot start with lowercase 'a' through 'z'. Instead of using \`${t}\`, consider using \`${r}\`, in enum \`${e}\`.`,EnumNumberMemberNotInitialized:({enumName:e,memberName:t})=>`Number enum members need to be initialized, e.g. \`${t} = 1\` in enum \`${e}\`.`,EnumStringMemberInconsistentlyInitialized:({enumName:e})=>`String enum members need to consistently either all use initializers, or use no initializers, in enum \`${e}\`.`,GetterMayNotHaveThisParam:"A getter cannot have a `this` parameter.",ImportReflectionHasImportType:"An `import module` declaration can not use `type` or `typeof` keyword.",ImportTypeShorthandOnlyInPureImport:"The `type` and `typeof` keywords on named imports can only be used on regular `import` statements. It cannot be used with `import type` or `import typeof` statements.",InexactInsideExact:"Explicit inexact syntax cannot appear inside an explicit exact object type.",InexactInsideNonObject:"Explicit inexact syntax cannot appear in class or interface definitions.",InexactVariance:"Explicit inexact syntax cannot have variance.",InvalidNonTypeImportInDeclareModule:"Imports within a `declare module` body must always be `import type` or `import typeof`.",MissingTypeParamDefault:"Type parameter declaration needs a default, since a preceding type parameter declaration has a default.",NestedDeclareModule:"`declare module` cannot be used inside another `declare module`.",NestedFlowComment:"Cannot have a flow comment inside another flow comment.",PatternIsOptional:Object.assign({message:"A binding pattern parameter cannot be optional in an implementation signature."},{reasonCode:"OptionalBindingPattern"}),SetterMayNotHaveThisParam:"A setter cannot have a `this` parameter.",SpreadVariance:"Spread properties cannot have variance.",ThisParamAnnotationRequired:"A type annotation is required for the `this` parameter.",ThisParamBannedInConstructor:"Constructors cannot have a `this` parameter; constructors don't bind `this` like other functions.",ThisParamMayNotBeOptional:"The `this` parameter cannot be optional.",ThisParamMustBeFirst:"The `this` parameter must be the first function parameter.",ThisParamNoDefault:"The `this` parameter may not have a default value.",TypeBeforeInitializer:"Type annotations must come before default assignments, e.g. instead of `age = 25: number` use `age: number = 25`.",TypeCastInPattern:"The type cast expression is expected to be wrapped with parenthesis.",UnexpectedExplicitInexactInObject:"Explicit inexact syntax must appear at the end of an inexact object.",UnexpectedReservedType:({reservedType:e})=>`Unexpected reserved type ${e}.`,UnexpectedReservedUnderscore:"`_` is only allowed as a type argument to call or new.",UnexpectedSpaceBetweenModuloChecks:"Spaces between `%` and `checks` are not allowed here.",UnexpectedSpreadType:"Spread operator cannot appear in class or interface definitions.",UnexpectedSubtractionOperand:'Unexpected token, expected "number" or "bigint".',UnexpectedTokenAfterTypeParameter:"Expected an arrow function after this type parameter declaration.",UnexpectedTypeParameterBeforeAsyncArrowFunction:"Type parameters must come after the async keyword, e.g. instead of `<T> async () => {}`, use `async <T>() => {}`.",UnsupportedDeclareExportKind:({unsupportedExportKind:e,suggestion:t})=>`\`declare export ${e}\` is not supported. Use \`${t}\` instead.`,UnsupportedStatementInDeclareModule:"Only declares and type imports are allowed inside declare module.",UnterminatedFlowComment:"Unterminated flow-comment."});function BH(e){return e.type==="DeclareExportAllDeclaration"||e.type==="DeclareExportDeclaration"&&(!e.declaration||e.declaration.type!=="TypeAlias"&&e.declaration.type!=="InterfaceDeclaration")}function $S(e){return e.importKind==="type"||e.importKind==="typeof"}var kH={const:"declare export var",let:"declare export var",type:"export type",interface:"export interface"};function FH(e,t){let r=[],n=[];for(let s=0;s<e.length;s++)(t(e[s],s,e)?r:n).push(e[s]);return[r,n]}var LH=/\*?\s*@((?:no)?flow)\b/,jH=e=>class extends e{constructor(...r){super(...r),this.flowPragma=void 0}getScopeHandler(){return am}shouldParseTypes(){return this.getPluginOption("flow","all")||this.flowPragma==="flow"}finishToken(r,n){r!==134&&r!==13&&r!==28&&this.flowPragma===void 0&&(this.flowPragma=null),super.finishToken(r,n)}addComment(r){if(this.flowPragma===void 0){let n=LH.exec(r.value);if(n)if(n[1]==="flow")this.flowPragma="flow";else if(n[1]==="noflow")this.flowPragma="noflow";else throw new Error("Unexpected flow pragma")}super.addComment(r)}flowParseTypeInitialiser(r){let n=this.state.inType;this.state.inType=!0,this.expect(r||14);let s=this.flowParseType();return this.state.inType=n,s}flowParsePredicate(){let r=this.startNode(),n=this.state.startLoc;return this.next(),this.expectContextual(110),this.state.lastTokStartLoc.index>n.index+1&&this.raise(De.UnexpectedSpaceBetweenModuloChecks,n),this.eat(10)?(r.value=super.parseExpression(),this.expect(11),this.finishNode(r,"DeclaredPredicate")):this.finishNode(r,"InferredPredicate")}flowParseTypeAndPredicateInitialiser(){let r=this.state.inType;this.state.inType=!0,this.expect(14);let n=null,s=null;return this.match(54)?(this.state.inType=r,s=this.flowParsePredicate()):(n=this.flowParseType(),this.state.inType=r,this.match(54)&&(s=this.flowParsePredicate())),[n,s]}flowParseDeclareClass(r){return this.next(),this.flowParseInterfaceish(r,!0),this.finishNode(r,"DeclareClass")}flowParseDeclareFunction(r){this.next();let n=r.id=this.parseIdentifier(),s=this.startNode(),i=this.startNode();this.match(47)?s.typeParameters=this.flowParseTypeParameterDeclaration():s.typeParameters=null,this.expect(10);let a=this.flowParseFunctionTypeParams();return s.params=a.params,s.rest=a.rest,s.this=a._this,this.expect(11),[s.returnType,r.predicate]=this.flowParseTypeAndPredicateInitialiser(),i.typeAnnotation=this.finishNode(s,"FunctionTypeAnnotation"),n.typeAnnotation=this.finishNode(i,"TypeAnnotation"),this.resetEndLocation(n),this.semicolon(),this.scope.declareName(r.id.name,2048,r.id.loc.start),this.finishNode(r,"DeclareFunction")}flowParseDeclare(r,n){if(this.match(80))return this.flowParseDeclareClass(r);if(this.match(68))return this.flowParseDeclareFunction(r);if(this.match(74))return this.flowParseDeclareVariable(r);if(this.eatContextual(127))return this.match(16)?this.flowParseDeclareModuleExports(r):(n&&this.raise(De.NestedDeclareModule,this.state.lastTokStartLoc),this.flowParseDeclareModule(r));if(this.isContextual(130))return this.flowParseDeclareTypeAlias(r);if(this.isContextual(131))return this.flowParseDeclareOpaqueType(r);if(this.isContextual(129))return this.flowParseDeclareInterface(r);if(this.match(82))return this.flowParseDeclareExportDeclaration(r,n);this.unexpected()}flowParseDeclareVariable(r){return this.next(),r.id=this.flowParseTypeAnnotatableIdentifier(!0),this.scope.declareName(r.id.name,5,r.id.loc.start),this.semicolon(),this.finishNode(r,"DeclareVariable")}flowParseDeclareModule(r){this.scope.enter(0),this.match(134)?r.id=super.parseExprAtom():r.id=this.parseIdentifier();let n=r.body=this.startNode(),s=n.body=[];for(this.expect(5);!this.match(8);){let o=this.startNode();this.match(83)?(this.next(),!this.isContextual(130)&&!this.match(87)&&this.raise(De.InvalidNonTypeImportInDeclareModule,this.state.lastTokStartLoc),super.parseImport(o)):(this.expectContextual(125,De.UnsupportedStatementInDeclareModule),o=this.flowParseDeclare(o,!0)),s.push(o)}this.scope.exit(),this.expect(8),this.finishNode(n,"BlockStatement");let i=null,a=!1;return s.forEach(o=>{BH(o)?(i==="CommonJS"&&this.raise(De.AmbiguousDeclareModuleKind,o),i="ES"):o.type==="DeclareModuleExports"&&(a&&this.raise(De.DuplicateDeclareModuleExports,o),i==="ES"&&this.raise(De.AmbiguousDeclareModuleKind,o),i="CommonJS",a=!0)}),r.kind=i||"CommonJS",this.finishNode(r,"DeclareModule")}flowParseDeclareExportDeclaration(r,n){if(this.expect(82),this.eat(65))return this.match(68)||this.match(80)?r.declaration=this.flowParseDeclare(this.startNode()):(r.declaration=this.flowParseType(),this.semicolon()),r.default=!0,this.finishNode(r,"DeclareExportDeclaration");if(this.match(75)||this.isLet()||(this.isContextual(130)||this.isContextual(129))&&!n){let s=this.state.value;throw this.raise(De.UnsupportedDeclareExportKind,this.state.startLoc,{unsupportedExportKind:s,suggestion:kH[s]})}if(this.match(74)||this.match(68)||this.match(80)||this.isContextual(131))return r.declaration=this.flowParseDeclare(this.startNode()),r.default=!1,this.finishNode(r,"DeclareExportDeclaration");if(this.match(55)||this.match(5)||this.isContextual(129)||this.isContextual(130)||this.isContextual(131))return r=this.parseExport(r,null),r.type==="ExportNamedDeclaration"&&(r.type="ExportDeclaration",r.default=!1,delete r.exportKind),r.type="Declare"+r.type,r;this.unexpected()}flowParseDeclareModuleExports(r){return this.next(),this.expectContextual(111),r.typeAnnotation=this.flowParseTypeAnnotation(),this.semicolon(),this.finishNode(r,"DeclareModuleExports")}flowParseDeclareTypeAlias(r){this.next();let n=this.flowParseTypeAlias(r);return n.type="DeclareTypeAlias",n}flowParseDeclareOpaqueType(r){this.next();let n=this.flowParseOpaqueType(r,!0);return n.type="DeclareOpaqueType",n}flowParseDeclareInterface(r){return this.next(),this.flowParseInterfaceish(r,!1),this.finishNode(r,"DeclareInterface")}flowParseInterfaceish(r,n){if(r.id=this.flowParseRestrictedIdentifier(!n,!0),this.scope.declareName(r.id.name,n?17:8201,r.id.loc.start),this.match(47)?r.typeParameters=this.flowParseTypeParameterDeclaration():r.typeParameters=null,r.extends=[],this.eat(81))do r.extends.push(this.flowParseInterfaceExtends());while(!n&&this.eat(12));if(n){if(r.implements=[],r.mixins=[],this.eatContextual(117))do r.mixins.push(this.flowParseInterfaceExtends());while(this.eat(12));if(this.eatContextual(113))do r.implements.push(this.flowParseInterfaceExtends());while(this.eat(12))}r.body=this.flowParseObjectType({allowStatic:n,allowExact:!1,allowSpread:!1,allowProto:n,allowInexact:!1})}flowParseInterfaceExtends(){let r=this.startNode();return r.id=this.flowParseQualifiedTypeIdentifier(),this.match(47)?r.typeParameters=this.flowParseTypeParameterInstantiation():r.typeParameters=null,this.finishNode(r,"InterfaceExtends")}flowParseInterface(r){return this.flowParseInterfaceish(r,!1),this.finishNode(r,"InterfaceDeclaration")}checkNotUnderscore(r){r==="_"&&this.raise(De.UnexpectedReservedUnderscore,this.state.startLoc)}checkReservedType(r,n,s){NH.has(r)&&this.raise(s?De.AssignReservedType:De.UnexpectedReservedType,n,{reservedType:r})}flowParseRestrictedIdentifier(r,n){return this.checkReservedType(this.state.value,this.state.startLoc,n),this.parseIdentifier(r)}flowParseTypeAlias(r){return r.id=this.flowParseRestrictedIdentifier(!1,!0),this.scope.declareName(r.id.name,8201,r.id.loc.start),this.match(47)?r.typeParameters=this.flowParseTypeParameterDeclaration():r.typeParameters=null,r.right=this.flowParseTypeInitialiser(29),this.semicolon(),this.finishNode(r,"TypeAlias")}flowParseOpaqueType(r,n){return this.expectContextual(130),r.id=this.flowParseRestrictedIdentifier(!0,!0),this.scope.declareName(r.id.name,8201,r.id.loc.start),this.match(47)?r.typeParameters=this.flowParseTypeParameterDeclaration():r.typeParameters=null,r.supertype=null,this.match(14)&&(r.supertype=this.flowParseTypeInitialiser(14)),r.impltype=null,n||(r.impltype=this.flowParseTypeInitialiser(29)),this.semicolon(),this.finishNode(r,"OpaqueType")}flowParseTypeParameter(r=!1){let n=this.state.startLoc,s=this.startNode(),i=this.flowParseVariance(),a=this.flowParseTypeAnnotatableIdentifier();return s.name=a.name,s.variance=i,s.bound=a.typeAnnotation,this.match(29)?(this.eat(29),s.default=this.flowParseType()):r&&this.raise(De.MissingTypeParamDefault,n),this.finishNode(s,"TypeParameter")}flowParseTypeParameterDeclaration(){let r=this.state.inType,n=this.startNode();n.params=[],this.state.inType=!0,this.match(47)||this.match(143)?this.next():this.unexpected();let s=!1;do{let i=this.flowParseTypeParameter(s);n.params.push(i),i.default&&(s=!0),this.match(48)||this.expect(12)}while(!this.match(48));return this.expect(48),this.state.inType=r,this.finishNode(n,"TypeParameterDeclaration")}flowInTopLevelContext(r){if(this.curContext()!==at.brace){let n=this.state.context;this.state.context=[n[0]];try{return r()}finally{this.state.context=n}}else return r()}flowParseTypeParameterInstantiationInExpression(){if(this.reScan_lt()===47)return this.flowParseTypeParameterInstantiation()}flowParseTypeParameterInstantiation(){let r=this.startNode(),n=this.state.inType;return this.state.inType=!0,r.params=[],this.flowInTopLevelContext(()=>{this.expect(47);let s=this.state.noAnonFunctionType;for(this.state.noAnonFunctionType=!1;!this.match(48);)r.params.push(this.flowParseType()),this.match(48)||this.expect(12);this.state.noAnonFunctionType=s}),this.state.inType=n,!this.state.inType&&this.curContext()===at.brace&&this.reScan_lt_gt(),this.expect(48),this.finishNode(r,"TypeParameterInstantiation")}flowParseTypeParameterInstantiationCallOrNew(){if(this.reScan_lt()!==47)return;let r=this.startNode(),n=this.state.inType;for(r.params=[],this.state.inType=!0,this.expect(47);!this.match(48);)r.params.push(this.flowParseTypeOrImplicitInstantiation()),this.match(48)||this.expect(12);return this.expect(48),this.state.inType=n,this.finishNode(r,"TypeParameterInstantiation")}flowParseInterfaceType(){let r=this.startNode();if(this.expectContextual(129),r.extends=[],this.eat(81))do r.extends.push(this.flowParseInterfaceExtends());while(this.eat(12));return r.body=this.flowParseObjectType({allowStatic:!1,allowExact:!1,allowSpread:!1,allowProto:!1,allowInexact:!1}),this.finishNode(r,"InterfaceTypeAnnotation")}flowParseObjectPropertyKey(){return this.match(135)||this.match(134)?super.parseExprAtom():this.parseIdentifier(!0)}flowParseObjectTypeIndexer(r,n,s){return r.static=n,this.lookahead().type===14?(r.id=this.flowParseObjectPropertyKey(),r.key=this.flowParseTypeInitialiser()):(r.id=null,r.key=this.flowParseType()),this.expect(3),r.value=this.flowParseTypeInitialiser(),r.variance=s,this.finishNode(r,"ObjectTypeIndexer")}flowParseObjectTypeInternalSlot(r,n){return r.static=n,r.id=this.flowParseObjectPropertyKey(),this.expect(3),this.expect(3),this.match(47)||this.match(10)?(r.method=!0,r.optional=!1,r.value=this.flowParseObjectTypeMethodish(this.startNodeAt(r.loc.start))):(r.method=!1,this.eat(17)&&(r.optional=!0),r.value=this.flowParseTypeInitialiser()),this.finishNode(r,"ObjectTypeInternalSlot")}flowParseObjectTypeMethodish(r){for(r.params=[],r.rest=null,r.typeParameters=null,r.this=null,this.match(47)&&(r.typeParameters=this.flowParseTypeParameterDeclaration()),this.expect(10),this.match(78)&&(r.this=this.flowParseFunctionTypeParam(!0),r.this.name=null,this.match(11)||this.expect(12));!this.match(11)&&!this.match(21);)r.params.push(this.flowParseFunctionTypeParam(!1)),this.match(11)||this.expect(12);return this.eat(21)&&(r.rest=this.flowParseFunctionTypeParam(!1)),this.expect(11),r.returnType=this.flowParseTypeInitialiser(),this.finishNode(r,"FunctionTypeAnnotation")}flowParseObjectTypeCallProperty(r,n){let s=this.startNode();return r.static=n,r.value=this.flowParseObjectTypeMethodish(s),this.finishNode(r,"ObjectTypeCallProperty")}flowParseObjectType({allowStatic:r,allowExact:n,allowSpread:s,allowProto:i,allowInexact:a}){let o=this.state.inType;this.state.inType=!0;let l=this.startNode();l.callProperties=[],l.properties=[],l.indexers=[],l.internalSlots=[];let u,c,p=!1;for(n&&this.match(6)?(this.expect(6),u=9,c=!0):(this.expect(5),u=8,c=!1),l.exact=c;!this.match(u);){let y=!1,E=null,f=null,m=this.startNode();if(i&&this.isContextual(118)){let D=this.lookahead();D.type!==14&&D.type!==17&&(this.next(),E=this.state.startLoc,r=!1)}if(r&&this.isContextual(106)){let D=this.lookahead();D.type!==14&&D.type!==17&&(this.next(),y=!0)}let g=this.flowParseVariance();if(this.eat(0))E!=null&&this.unexpected(E),this.eat(0)?(g&&this.unexpected(g.loc.start),l.internalSlots.push(this.flowParseObjectTypeInternalSlot(m,y))):l.indexers.push(this.flowParseObjectTypeIndexer(m,y,g));else if(this.match(10)||this.match(47))E!=null&&this.unexpected(E),g&&this.unexpected(g.loc.start),l.callProperties.push(this.flowParseObjectTypeCallProperty(m,y));else{let D="init";if(this.isContextual(99)||this.isContextual(104)){let O=this.lookahead();JS(O.type)&&(D=this.state.value,this.next())}let _=this.flowParseObjectTypeProperty(m,y,E,g,D,s,a!=null?a:!c);_===null?(p=!0,f=this.state.lastTokStartLoc):l.properties.push(_)}this.flowObjectTypeSemicolon(),f&&!this.match(8)&&!this.match(9)&&this.raise(De.UnexpectedExplicitInexactInObject,f)}this.expect(u),s&&(l.inexact=p);let d=this.finishNode(l,"ObjectTypeAnnotation");return this.state.inType=o,d}flowParseObjectTypeProperty(r,n,s,i,a,o,l){if(this.eat(21))return this.match(12)||this.match(13)||this.match(8)||this.match(9)?(o?l||this.raise(De.InexactInsideExact,this.state.lastTokStartLoc):this.raise(De.InexactInsideNonObject,this.state.lastTokStartLoc),i&&this.raise(De.InexactVariance,i),null):(o||this.raise(De.UnexpectedSpreadType,this.state.lastTokStartLoc),s!=null&&this.unexpected(s),i&&this.raise(De.SpreadVariance,i),r.argument=this.flowParseType(),this.finishNode(r,"ObjectTypeSpreadProperty"));{r.key=this.flowParseObjectPropertyKey(),r.static=n,r.proto=s!=null,r.kind=a;let u=!1;return this.match(47)||this.match(10)?(r.method=!0,s!=null&&this.unexpected(s),i&&this.unexpected(i.loc.start),r.value=this.flowParseObjectTypeMethodish(this.startNodeAt(r.loc.start)),(a==="get"||a==="set")&&this.flowCheckGetterSetterParams(r),!o&&r.key.name==="constructor"&&r.value.this&&this.raise(De.ThisParamBannedInConstructor,r.value.this)):(a!=="init"&&this.unexpected(),r.method=!1,this.eat(17)&&(u=!0),r.value=this.flowParseTypeInitialiser(),r.variance=i),r.optional=u,this.finishNode(r,"ObjectTypeProperty")}}flowCheckGetterSetterParams(r){let n=r.kind==="get"?0:1,s=r.value.params.length+(r.value.rest?1:0);r.value.this&&this.raise(r.kind==="get"?De.GetterMayNotHaveThisParam:De.SetterMayNotHaveThisParam,r.value.this),s!==n&&this.raise(r.kind==="get"?B.BadGetterArity:B.BadSetterArity,r),r.kind==="set"&&r.value.rest&&this.raise(B.BadSetterRestParameter,r)}flowObjectTypeSemicolon(){!this.eat(13)&&!this.eat(12)&&!this.match(8)&&!this.match(9)&&this.unexpected()}flowParseQualifiedTypeIdentifier(r,n){r!=null||(r=this.state.startLoc);let s=n||this.flowParseRestrictedIdentifier(!0);for(;this.eat(16);){let i=this.startNodeAt(r);i.qualification=s,i.id=this.flowParseRestrictedIdentifier(!0),s=this.finishNode(i,"QualifiedTypeIdentifier")}return s}flowParseGenericType(r,n){let s=this.startNodeAt(r);return s.typeParameters=null,s.id=this.flowParseQualifiedTypeIdentifier(r,n),this.match(47)&&(s.typeParameters=this.flowParseTypeParameterInstantiation()),this.finishNode(s,"GenericTypeAnnotation")}flowParseTypeofType(){let r=this.startNode();return this.expect(87),r.argument=this.flowParsePrimaryType(),this.finishNode(r,"TypeofTypeAnnotation")}flowParseTupleType(){let r=this.startNode();for(r.types=[],this.expect(0);this.state.pos<this.length&&!this.match(3)&&(r.types.push(this.flowParseType()),!this.match(3));)this.expect(12);return this.expect(3),this.finishNode(r,"TupleTypeAnnotation")}flowParseFunctionTypeParam(r){let n=null,s=!1,i=null,a=this.startNode(),o=this.lookahead(),l=this.state.type===78;return o.type===14||o.type===17?(l&&!r&&this.raise(De.ThisParamMustBeFirst,a),n=this.parseIdentifier(l),this.eat(17)&&(s=!0,l&&this.raise(De.ThisParamMayNotBeOptional,a)),i=this.flowParseTypeInitialiser()):i=this.flowParseType(),a.name=n,a.optional=s,a.typeAnnotation=i,this.finishNode(a,"FunctionTypeParam")}reinterpretTypeAsFunctionTypeParam(r){let n=this.startNodeAt(r.loc.start);return n.name=null,n.optional=!1,n.typeAnnotation=r,this.finishNode(n,"FunctionTypeParam")}flowParseFunctionTypeParams(r=[]){let n=null,s=null;for(this.match(78)&&(s=this.flowParseFunctionTypeParam(!0),s.name=null,this.match(11)||this.expect(12));!this.match(11)&&!this.match(21);)r.push(this.flowParseFunctionTypeParam(!1)),this.match(11)||this.expect(12);return this.eat(21)&&(n=this.flowParseFunctionTypeParam(!1)),{params:r,rest:n,_this:s}}flowIdentToTypeAnnotation(r,n,s){switch(s.name){case"any":return this.finishNode(n,"AnyTypeAnnotation");case"bool":case"boolean":return this.finishNode(n,"BooleanTypeAnnotation");case"mixed":return this.finishNode(n,"MixedTypeAnnotation");case"empty":return this.finishNode(n,"EmptyTypeAnnotation");case"number":return this.finishNode(n,"NumberTypeAnnotation");case"string":return this.finishNode(n,"StringTypeAnnotation");case"symbol":return this.finishNode(n,"SymbolTypeAnnotation");default:return this.checkNotUnderscore(s.name),this.flowParseGenericType(r,s)}}flowParsePrimaryType(){let r=this.state.startLoc,n=this.startNode(),s,i,a=!1,o=this.state.noAnonFunctionType;switch(this.state.type){case 5:return this.flowParseObjectType({allowStatic:!1,allowExact:!1,allowSpread:!0,allowProto:!1,allowInexact:!0});case 6:return this.flowParseObjectType({allowStatic:!1,allowExact:!0,allowSpread:!0,allowProto:!1,allowInexact:!1});case 0:return this.state.noAnonFunctionType=!1,i=this.flowParseTupleType(),this.state.noAnonFunctionType=o,i;case 47:{let l=this.startNode();return l.typeParameters=this.flowParseTypeParameterDeclaration(),this.expect(10),s=this.flowParseFunctionTypeParams(),l.params=s.params,l.rest=s.rest,l.this=s._this,this.expect(11),this.expect(19),l.returnType=this.flowParseType(),this.finishNode(l,"FunctionTypeAnnotation")}case 10:{let l=this.startNode();if(this.next(),!this.match(11)&&!this.match(21))if(ot(this.state.type)||this.match(78)){let u=this.lookahead().type;a=u!==17&&u!==14}else a=!0;if(a){if(this.state.noAnonFunctionType=!1,i=this.flowParseType(),this.state.noAnonFunctionType=o,this.state.noAnonFunctionType||!(this.match(12)||this.match(11)&&this.lookahead().type===19))return this.expect(11),i;this.eat(12)}return i?s=this.flowParseFunctionTypeParams([this.reinterpretTypeAsFunctionTypeParam(i)]):s=this.flowParseFunctionTypeParams(),l.params=s.params,l.rest=s.rest,l.this=s._this,this.expect(11),this.expect(19),l.returnType=this.flowParseType(),l.typeParameters=null,this.finishNode(l,"FunctionTypeAnnotation")}case 134:return this.parseLiteral(this.state.value,"StringLiteralTypeAnnotation");case 85:case 86:return n.value=this.match(85),this.next(),this.finishNode(n,"BooleanLiteralTypeAnnotation");case 53:if(this.state.value==="-"){if(this.next(),this.match(135))return this.parseLiteralAtNode(-this.state.value,"NumberLiteralTypeAnnotation",n);if(this.match(136))return this.parseLiteralAtNode(-this.state.value,"BigIntLiteralTypeAnnotation",n);throw this.raise(De.UnexpectedSubtractionOperand,this.state.startLoc)}this.unexpected();return;case 135:return this.parseLiteral(this.state.value,"NumberLiteralTypeAnnotation");case 136:return this.parseLiteral(this.state.value,"BigIntLiteralTypeAnnotation");case 88:return this.next(),this.finishNode(n,"VoidTypeAnnotation");case 84:return this.next(),this.finishNode(n,"NullLiteralTypeAnnotation");case 78:return this.next(),this.finishNode(n,"ThisTypeAnnotation");case 55:return this.next(),this.finishNode(n,"ExistsTypeAnnotation");case 87:return this.flowParseTypeofType();default:if(Im(this.state.type)){let l=Ss(this.state.type);return this.next(),super.createIdentifier(n,l)}else if(ot(this.state.type))return this.isContextual(129)?this.flowParseInterfaceType():this.flowIdentToTypeAnnotation(r,n,this.parseIdentifier())}this.unexpected()}flowParsePostfixType(){let r=this.state.startLoc,n=this.flowParsePrimaryType(),s=!1;for(;(this.match(0)||this.match(18))&&!this.canInsertSemicolon();){let i=this.startNodeAt(r),a=this.eat(18);s=s||a,this.expect(0),!a&&this.match(3)?(i.elementType=n,this.next(),n=this.finishNode(i,"ArrayTypeAnnotation")):(i.objectType=n,i.indexType=this.flowParseType(),this.expect(3),s?(i.optional=a,n=this.finishNode(i,"OptionalIndexedAccessType")):n=this.finishNode(i,"IndexedAccessType"))}return n}flowParsePrefixType(){let r=this.startNode();return this.eat(17)?(r.typeAnnotation=this.flowParsePrefixType(),this.finishNode(r,"NullableTypeAnnotation")):this.flowParsePostfixType()}flowParseAnonFunctionWithoutParens(){let r=this.flowParsePrefixType();if(!this.state.noAnonFunctionType&&this.eat(19)){let n=this.startNodeAt(r.loc.start);return n.params=[this.reinterpretTypeAsFunctionTypeParam(r)],n.rest=null,n.this=null,n.returnType=this.flowParseType(),n.typeParameters=null,this.finishNode(n,"FunctionTypeAnnotation")}return r}flowParseIntersectionType(){let r=this.startNode();this.eat(45);let n=this.flowParseAnonFunctionWithoutParens();for(r.types=[n];this.eat(45);)r.types.push(this.flowParseAnonFunctionWithoutParens());return r.types.length===1?n:this.finishNode(r,"IntersectionTypeAnnotation")}flowParseUnionType(){let r=this.startNode();this.eat(43);let n=this.flowParseIntersectionType();for(r.types=[n];this.eat(43);)r.types.push(this.flowParseIntersectionType());return r.types.length===1?n:this.finishNode(r,"UnionTypeAnnotation")}flowParseType(){let r=this.state.inType;this.state.inType=!0;let n=this.flowParseUnionType();return this.state.inType=r,n}flowParseTypeOrImplicitInstantiation(){if(this.state.type===132&&this.state.value==="_"){let r=this.state.startLoc,n=this.parseIdentifier();return this.flowParseGenericType(r,n)}else return this.flowParseType()}flowParseTypeAnnotation(){let r=this.startNode();return r.typeAnnotation=this.flowParseTypeInitialiser(),this.finishNode(r,"TypeAnnotation")}flowParseTypeAnnotatableIdentifier(r){let n=r?this.parseIdentifier():this.flowParseRestrictedIdentifier();return this.match(14)&&(n.typeAnnotation=this.flowParseTypeAnnotation(),this.resetEndLocation(n)),n}typeCastToParameter(r){return r.expression.typeAnnotation=r.typeAnnotation,this.resetEndLocation(r.expression,r.typeAnnotation.loc.end),r.expression}flowParseVariance(){let r=null;return this.match(53)?(r=this.startNode(),this.state.value==="+"?r.kind="plus":r.kind="minus",this.next(),this.finishNode(r,"Variance")):r}parseFunctionBody(r,n,s=!1){if(n){this.forwardNoArrowParamsConversionAt(r,()=>super.parseFunctionBody(r,!0,s));return}super.parseFunctionBody(r,!1,s)}parseFunctionBodyAndFinish(r,n,s=!1){if(this.match(14)){let i=this.startNode();[i.typeAnnotation,r.predicate]=this.flowParseTypeAndPredicateInitialiser(),r.returnType=i.typeAnnotation?this.finishNode(i,"TypeAnnotation"):null}return super.parseFunctionBodyAndFinish(r,n,s)}parseStatementLike(r){if(this.state.strict&&this.isContextual(129)){let s=this.lookahead();if(en(s.type)){let i=this.startNode();return this.next(),this.flowParseInterface(i)}}else if(this.isContextual(126)){let s=this.startNode();return this.next(),this.flowParseEnumDeclaration(s)}let n=super.parseStatementLike(r);return this.flowPragma===void 0&&!this.isValidDirective(n)&&(this.flowPragma=null),n}parseExpressionStatement(r,n,s){if(n.type==="Identifier"){if(n.name==="declare"){if(this.match(80)||ot(this.state.type)||this.match(68)||this.match(74)||this.match(82))return this.flowParseDeclare(r)}else if(ot(this.state.type)){if(n.name==="interface")return this.flowParseInterface(r);if(n.name==="type")return this.flowParseTypeAlias(r);if(n.name==="opaque")return this.flowParseOpaqueType(r,!1)}}return super.parseExpressionStatement(r,n,s)}shouldParseExportDeclaration(){let{type:r}=this.state;return r===126||RS(r)?!this.state.containsEsc:super.shouldParseExportDeclaration()}isExportDefaultSpecifier(){let{type:r}=this.state;return r===126||RS(r)?this.state.containsEsc:super.isExportDefaultSpecifier()}parseExportDefaultExpression(){if(this.isContextual(126)){let r=this.startNode();return this.next(),this.flowParseEnumDeclaration(r)}return super.parseExportDefaultExpression()}parseConditional(r,n,s){if(!this.match(17))return r;if(this.state.maybeInArrowParameters){let d=this.lookaheadCharCode();if(d===44||d===61||d===58||d===41)return this.setOptionalParametersError(s),r}this.expect(17);let i=this.state.clone(),a=this.state.noArrowAt,o=this.startNodeAt(n),{consequent:l,failed:u}=this.tryParseConditionalConsequent(),[c,p]=this.getArrowLikeExpressions(l);if(u||p.length>0){let d=[...a];if(p.length>0){this.state=i,this.state.noArrowAt=d;for(let y=0;y<p.length;y++)d.push(p[y].start);({consequent:l,failed:u}=this.tryParseConditionalConsequent()),[c,p]=this.getArrowLikeExpressions(l)}u&&c.length>1&&this.raise(De.AmbiguousConditionalArrow,i.startLoc),u&&c.length===1&&(this.state=i,d.push(c[0].start),this.state.noArrowAt=d,{consequent:l,failed:u}=this.tryParseConditionalConsequent())}return this.getArrowLikeExpressions(l,!0),this.state.noArrowAt=a,this.expect(14),o.test=r,o.consequent=l,o.alternate=this.forwardNoArrowParamsConversionAt(o,()=>this.parseMaybeAssign(void 0,void 0)),this.finishNode(o,"ConditionalExpression")}tryParseConditionalConsequent(){this.state.noArrowParamsConversionAt.push(this.state.start);let r=this.parseMaybeAssignAllowIn(),n=!this.match(14);return this.state.noArrowParamsConversionAt.pop(),{consequent:r,failed:n}}getArrowLikeExpressions(r,n){let s=[r],i=[];for(;s.length!==0;){let a=s.pop();a.type==="ArrowFunctionExpression"&&a.body.type!=="BlockStatement"?(a.typeParameters||!a.returnType?this.finishArrowValidation(a):i.push(a),s.push(a.body)):a.type==="ConditionalExpression"&&(s.push(a.consequent),s.push(a.alternate))}return n?(i.forEach(a=>this.finishArrowValidation(a)),[i,[]]):FH(i,a=>a.params.every(o=>this.isAssignable(o,!0)))}finishArrowValidation(r){var n;this.toAssignableList(r.params,(n=r.extra)==null?void 0:n.trailingCommaLoc,!1),this.scope.enter(6),super.checkParams(r,!1,!0),this.scope.exit()}forwardNoArrowParamsConversionAt(r,n){let s;return this.state.noArrowParamsConversionAt.includes(this.offsetToSourcePos(r.start))?(this.state.noArrowParamsConversionAt.push(this.state.start),s=n(),this.state.noArrowParamsConversionAt.pop()):s=n(),s}parseParenItem(r,n){let s=super.parseParenItem(r,n);if(this.eat(17)&&(s.optional=!0,this.resetEndLocation(r)),this.match(14)){let i=this.startNodeAt(n);return i.expression=s,i.typeAnnotation=this.flowParseTypeAnnotation(),this.finishNode(i,"TypeCastExpression")}return s}assertModuleNodeAllowed(r){r.type==="ImportDeclaration"&&(r.importKind==="type"||r.importKind==="typeof")||r.type==="ExportNamedDeclaration"&&r.exportKind==="type"||r.type==="ExportAllDeclaration"&&r.exportKind==="type"||super.assertModuleNodeAllowed(r)}parseExportDeclaration(r){if(this.isContextual(130)){r.exportKind="type";let n=this.startNode();return this.next(),this.match(5)?(r.specifiers=this.parseExportSpecifiers(!0),super.parseExportFrom(r),null):this.flowParseTypeAlias(n)}else if(this.isContextual(131)){r.exportKind="type";let n=this.startNode();return this.next(),this.flowParseOpaqueType(n,!1)}else if(this.isContextual(129)){r.exportKind="type";let n=this.startNode();return this.next(),this.flowParseInterface(n)}else if(this.isContextual(126)){r.exportKind="value";let n=this.startNode();return this.next(),this.flowParseEnumDeclaration(n)}else return super.parseExportDeclaration(r)}eatExportStar(r){return super.eatExportStar(r)?!0:this.isContextual(130)&&this.lookahead().type===55?(r.exportKind="type",this.next(),this.next(),!0):!1}maybeParseExportNamespaceSpecifier(r){let{startLoc:n}=this.state,s=super.maybeParseExportNamespaceSpecifier(r);return s&&r.exportKind==="type"&&this.unexpected(n),s}parseClassId(r,n,s){super.parseClassId(r,n,s),this.match(47)&&(r.typeParameters=this.flowParseTypeParameterDeclaration())}parseClassMember(r,n,s){let{startLoc:i}=this.state;if(this.isContextual(125)){if(super.parseClassMemberFromModifier(r,n))return;n.declare=!0}super.parseClassMember(r,n,s),n.declare&&(n.type!=="ClassProperty"&&n.type!=="ClassPrivateProperty"&&n.type!=="PropertyDefinition"?this.raise(De.DeclareClassElement,i):n.value&&this.raise(De.DeclareClassFieldInitializer,n.value))}isIterator(r){return r==="iterator"||r==="asyncIterator"}readIterator(){let r=super.readWord1(),n="@@"+r;(!this.isIterator(r)||!this.state.inType)&&this.raise(B.InvalidIdentifier,this.state.curPosition(),{identifierName:n}),this.finishToken(132,n)}getTokenFromCode(r){let n=this.input.charCodeAt(this.state.pos+1);r===123&&n===124?this.finishOp(6,2):this.state.inType&&(r===62||r===60)?this.finishOp(r===62?48:47,1):this.state.inType&&r===63?n===46?this.finishOp(18,2):this.finishOp(17,1):gH(r,n,this.input.charCodeAt(this.state.pos+2))?(this.state.pos+=2,this.readIterator()):super.getTokenFromCode(r)}isAssignable(r,n){return r.type==="TypeCastExpression"?this.isAssignable(r.expression,n):super.isAssignable(r,n)}toAssignable(r,n=!1){!n&&r.type==="AssignmentExpression"&&r.left.type==="TypeCastExpression"&&(r.left=this.typeCastToParameter(r.left)),super.toAssignable(r,n)}toAssignableList(r,n,s){for(let i=0;i<r.length;i++){let a=r[i];(a==null?void 0:a.type)==="TypeCastExpression"&&(r[i]=this.typeCastToParameter(a))}super.toAssignableList(r,n,s)}toReferencedList(r,n){for(let i=0;i<r.length;i++){var s;let a=r[i];a&&a.type==="TypeCastExpression"&&!((s=a.extra)!=null&&s.parenthesized)&&(r.length>1||!n)&&this.raise(De.TypeCastInPattern,a.typeAnnotation)}return r}parseArrayLike(r,n,s,i){let a=super.parseArrayLike(r,n,s,i);return n&&!this.state.maybeInArrowParameters&&this.toReferencedList(a.elements),a}isValidLVal(r,n,s){return r==="TypeCastExpression"||super.isValidLVal(r,n,s)}parseClassProperty(r){return this.match(14)&&(r.typeAnnotation=this.flowParseTypeAnnotation()),super.parseClassProperty(r)}parseClassPrivateProperty(r){return this.match(14)&&(r.typeAnnotation=this.flowParseTypeAnnotation()),super.parseClassPrivateProperty(r)}isClassMethod(){return this.match(47)||super.isClassMethod()}isClassProperty(){return this.match(14)||super.isClassProperty()}isNonstaticConstructor(r){return!this.match(14)&&super.isNonstaticConstructor(r)}pushClassMethod(r,n,s,i,a,o){if(n.variance&&this.unexpected(n.variance.loc.start),delete n.variance,this.match(47)&&(n.typeParameters=this.flowParseTypeParameterDeclaration()),super.pushClassMethod(r,n,s,i,a,o),n.params&&a){let l=n.params;l.length>0&&this.isThisParam(l[0])&&this.raise(De.ThisParamBannedInConstructor,n)}else if(n.type==="MethodDefinition"&&a&&n.value.params){let l=n.value.params;l.length>0&&this.isThisParam(l[0])&&this.raise(De.ThisParamBannedInConstructor,n)}}pushClassPrivateMethod(r,n,s,i){n.variance&&this.unexpected(n.variance.loc.start),delete n.variance,this.match(47)&&(n.typeParameters=this.flowParseTypeParameterDeclaration()),super.pushClassPrivateMethod(r,n,s,i)}parseClassSuper(r){if(super.parseClassSuper(r),r.superClass&&(this.match(47)||this.match(51))&&(r.superTypeParameters=this.flowParseTypeParameterInstantiationInExpression()),this.isContextual(113)){this.next();let n=r.implements=[];do{let s=this.startNode();s.id=this.flowParseRestrictedIdentifier(!0),this.match(47)?s.typeParameters=this.flowParseTypeParameterInstantiation():s.typeParameters=null,n.push(this.finishNode(s,"ClassImplements"))}while(this.eat(12))}}checkGetterSetterParams(r){super.checkGetterSetterParams(r);let n=this.getObjectOrClassMethodParams(r);if(n.length>0){let s=n[0];this.isThisParam(s)&&r.kind==="get"?this.raise(De.GetterMayNotHaveThisParam,s):this.isThisParam(s)&&this.raise(De.SetterMayNotHaveThisParam,s)}}parsePropertyNamePrefixOperator(r){r.variance=this.flowParseVariance()}parseObjPropValue(r,n,s,i,a,o,l){r.variance&&this.unexpected(r.variance.loc.start),delete r.variance;let u;this.match(47)&&!o&&(u=this.flowParseTypeParameterDeclaration(),this.match(10)||this.unexpected());let c=super.parseObjPropValue(r,n,s,i,a,o,l);return u&&((c.value||c).typeParameters=u),c}parseFunctionParamType(r){return this.eat(17)&&(r.type!=="Identifier"&&this.raise(De.PatternIsOptional,r),this.isThisParam(r)&&this.raise(De.ThisParamMayNotBeOptional,r),r.optional=!0),this.match(14)?r.typeAnnotation=this.flowParseTypeAnnotation():this.isThisParam(r)&&this.raise(De.ThisParamAnnotationRequired,r),this.match(29)&&this.isThisParam(r)&&this.raise(De.ThisParamNoDefault,r),this.resetEndLocation(r),r}parseMaybeDefault(r,n){let s=super.parseMaybeDefault(r,n);return s.type==="AssignmentPattern"&&s.typeAnnotation&&s.right.start<s.typeAnnotation.start&&this.raise(De.TypeBeforeInitializer,s.typeAnnotation),s}checkImportReflection(r){super.checkImportReflection(r),r.module&&r.importKind!=="value"&&this.raise(De.ImportReflectionHasImportType,r.specifiers[0].loc.start)}parseImportSpecifierLocal(r,n,s){n.local=$S(r)?this.flowParseRestrictedIdentifier(!0,!0):this.parseIdentifier(),r.specifiers.push(this.finishImportSpecifier(n,s))}isPotentialImportPhase(r){if(super.isPotentialImportPhase(r))return!0;if(this.isContextual(130)){if(!r)return!0;let n=this.lookaheadCharCode();return n===123||n===42}return!r&&this.isContextual(87)}applyImportPhase(r,n,s,i){if(super.applyImportPhase(r,n,s,i),n){if(!s&&this.match(65))return;r.exportKind=s==="type"?s:"value"}else s==="type"&&this.match(55)&&this.unexpected(),r.importKind=s==="type"||s==="typeof"?s:"value"}parseImportSpecifier(r,n,s,i,a){let o=r.imported,l=null;o.type==="Identifier"&&(o.name==="type"?l="type":o.name==="typeof"&&(l="typeof"));let u=!1;if(this.isContextual(93)&&!this.isLookaheadContextual("as")){let p=this.parseIdentifier(!0);l!==null&&!en(this.state.type)?(r.imported=p,r.importKind=l,r.local=Rn(p)):(r.imported=o,r.importKind=null,r.local=this.parseIdentifier())}else{if(l!==null&&en(this.state.type))r.imported=this.parseIdentifier(!0),r.importKind=l;else{if(n)throw this.raise(B.ImportBindingIsString,r,{importName:o.value});r.imported=o,r.importKind=null}this.eatContextual(93)?r.local=this.parseIdentifier():(u=!0,r.local=Rn(r.imported))}let c=$S(r);return s&&c&&this.raise(De.ImportTypeShorthandOnlyInPureImport,r),(s||c)&&this.checkReservedType(r.local.name,r.local.loc.start,!0),u&&!s&&!c&&this.checkReservedWord(r.local.name,r.loc.start,!0,!0),this.finishImportSpecifier(r,"ImportSpecifier")}parseBindingAtom(){switch(this.state.type){case 78:return this.parseIdentifier(!0);default:return super.parseBindingAtom()}}parseFunctionParams(r,n){let s=r.kind;s!=="get"&&s!=="set"&&this.match(47)&&(r.typeParameters=this.flowParseTypeParameterDeclaration()),super.parseFunctionParams(r,n)}parseVarId(r,n){super.parseVarId(r,n),this.match(14)&&(r.id.typeAnnotation=this.flowParseTypeAnnotation(),this.resetEndLocation(r.id))}parseAsyncArrowFromCallExpression(r,n){if(this.match(14)){let s=this.state.noAnonFunctionType;this.state.noAnonFunctionType=!0,r.returnType=this.flowParseTypeAnnotation(),this.state.noAnonFunctionType=s}return super.parseAsyncArrowFromCallExpression(r,n)}shouldParseAsyncArrow(){return this.match(14)||super.shouldParseAsyncArrow()}parseMaybeAssign(r,n){var s;let i=null,a;if(this.hasPlugin("jsx")&&(this.match(143)||this.match(47))){if(i=this.state.clone(),a=this.tryParse(()=>super.parseMaybeAssign(r,n),i),!a.error)return a.node;let{context:u}=this.state,c=u[u.length-1];(c===at.j_oTag||c===at.j_expr)&&u.pop()}if((s=a)!=null&&s.error||this.match(47)){var o,l;i=i||this.state.clone();let u,c=this.tryParse(d=>{var y;u=this.flowParseTypeParameterDeclaration();let E=this.forwardNoArrowParamsConversionAt(u,()=>{let m=super.parseMaybeAssign(r,n);return this.resetStartLocationFromNode(m,u),m});(y=E.extra)!=null&&y.parenthesized&&d();let f=this.maybeUnwrapTypeCastExpression(E);return f.type!=="ArrowFunctionExpression"&&d(),f.typeParameters=u,this.resetStartLocationFromNode(f,u),E},i),p=null;if(c.node&&this.maybeUnwrapTypeCastExpression(c.node).type==="ArrowFunctionExpression"){if(!c.error&&!c.aborted)return c.node.async&&this.raise(De.UnexpectedTypeParameterBeforeAsyncArrowFunction,u),c.node;p=c.node}if((o=a)!=null&&o.node)return this.state=a.failState,a.node;if(p)return this.state=c.failState,p;throw(l=a)!=null&&l.thrown?a.error:c.thrown?c.error:this.raise(De.UnexpectedTokenAfterTypeParameter,u)}return super.parseMaybeAssign(r,n)}parseArrow(r){if(this.match(14)){let n=this.tryParse(()=>{let s=this.state.noAnonFunctionType;this.state.noAnonFunctionType=!0;let i=this.startNode();return[i.typeAnnotation,r.predicate]=this.flowParseTypeAndPredicateInitialiser(),this.state.noAnonFunctionType=s,this.canInsertSemicolon()&&this.unexpected(),this.match(19)||this.unexpected(),i});if(n.thrown)return null;n.error&&(this.state=n.failState),r.returnType=n.node.typeAnnotation?this.finishNode(n.node,"TypeAnnotation"):null}return super.parseArrow(r)}shouldParseArrow(r){return this.match(14)||super.shouldParseArrow(r)}setArrowFunctionParameters(r,n){this.state.noArrowParamsConversionAt.includes(this.offsetToSourcePos(r.start))?r.params=n:super.setArrowFunctionParameters(r,n)}checkParams(r,n,s,i=!0){if(!(s&&this.state.noArrowParamsConversionAt.includes(this.offsetToSourcePos(r.start)))){for(let a=0;a<r.params.length;a++)this.isThisParam(r.params[a])&&a>0&&this.raise(De.ThisParamMustBeFirst,r.params[a]);super.checkParams(r,n,s,i)}}parseParenAndDistinguishExpression(r){return super.parseParenAndDistinguishExpression(r&&!this.state.noArrowAt.includes(this.sourceToOffsetPos(this.state.start)))}parseSubscripts(r,n,s){if(r.type==="Identifier"&&r.name==="async"&&this.state.noArrowAt.includes(n.index)){this.next();let i=this.startNodeAt(n);i.callee=r,i.arguments=super.parseCallExpressionArguments(11),r=this.finishNode(i,"CallExpression")}else if(r.type==="Identifier"&&r.name==="async"&&this.match(47)){let i=this.state.clone(),a=this.tryParse(l=>this.parseAsyncArrowWithTypeParameters(n)||l(),i);if(!a.error&&!a.aborted)return a.node;let o=this.tryParse(()=>super.parseSubscripts(r,n,s),i);if(o.node&&!o.error)return o.node;if(a.node)return this.state=a.failState,a.node;if(o.node)return this.state=o.failState,o.node;throw a.error||o.error}return super.parseSubscripts(r,n,s)}parseSubscript(r,n,s,i){if(this.match(18)&&this.isLookaheadToken_lt()){if(i.optionalChainMember=!0,s)return i.stop=!0,r;this.next();let a=this.startNodeAt(n);return a.callee=r,a.typeArguments=this.flowParseTypeParameterInstantiationInExpression(),this.expect(10),a.arguments=this.parseCallExpressionArguments(11),a.optional=!0,this.finishCallExpression(a,!0)}else if(!s&&this.shouldParseTypes()&&(this.match(47)||this.match(51))){let a=this.startNodeAt(n);a.callee=r;let o=this.tryParse(()=>(a.typeArguments=this.flowParseTypeParameterInstantiationCallOrNew(),this.expect(10),a.arguments=super.parseCallExpressionArguments(11),i.optionalChainMember&&(a.optional=!1),this.finishCallExpression(a,i.optionalChainMember)));if(o.node)return o.error&&(this.state=o.failState),o.node}return super.parseSubscript(r,n,s,i)}parseNewCallee(r){super.parseNewCallee(r);let n=null;this.shouldParseTypes()&&this.match(47)&&(n=this.tryParse(()=>this.flowParseTypeParameterInstantiationCallOrNew()).node),r.typeArguments=n}parseAsyncArrowWithTypeParameters(r){let n=this.startNodeAt(r);if(this.parseFunctionParams(n,!1),!!this.parseArrow(n))return super.parseArrowExpression(n,void 0,!0)}readToken_mult_modulo(r){let n=this.input.charCodeAt(this.state.pos+1);if(r===42&&n===47&&this.state.hasFlowComment){this.state.hasFlowComment=!1,this.state.pos+=2,this.nextToken();return}super.readToken_mult_modulo(r)}readToken_pipe_amp(r){let n=this.input.charCodeAt(this.state.pos+1);if(r===124&&n===125){this.finishOp(9,2);return}super.readToken_pipe_amp(r)}parseTopLevel(r,n){let s=super.parseTopLevel(r,n);return this.state.hasFlowComment&&this.raise(De.UnterminatedFlowComment,this.state.curPosition()),s}skipBlockComment(){if(this.hasPlugin("flowComments")&&this.skipFlowComment()){if(this.state.hasFlowComment)throw this.raise(De.NestedFlowComment,this.state.startLoc);this.hasFlowCommentCompletion();let r=this.skipFlowComment();r&&(this.state.pos+=r,this.state.hasFlowComment=!0);return}return super.skipBlockComment(this.state.hasFlowComment?"*-/":"*/")}skipFlowComment(){let{pos:r}=this.state,n=2;for(;[32,9].includes(this.input.charCodeAt(r+n));)n++;let s=this.input.charCodeAt(n+r),i=this.input.charCodeAt(n+r+1);return s===58&&i===58?n+2:this.input.slice(n+r,n+r+12)==="flow-include"?n+12:s===58&&i!==58?n:!1}hasFlowCommentCompletion(){if(this.input.indexOf("*/",this.state.pos)===-1)throw this.raise(B.UnterminatedComment,this.state.curPosition())}flowEnumErrorBooleanMemberNotInitialized(r,{enumName:n,memberName:s}){this.raise(De.EnumBooleanMemberNotInitialized,r,{memberName:s,enumName:n})}flowEnumErrorInvalidMemberInitializer(r,n){return this.raise(n.explicitType?n.explicitType==="symbol"?De.EnumInvalidMemberInitializerSymbolType:De.EnumInvalidMemberInitializerPrimaryType:De.EnumInvalidMemberInitializerUnknownType,r,n)}flowEnumErrorNumberMemberNotInitialized(r,n){this.raise(De.EnumNumberMemberNotInitialized,r,n)}flowEnumErrorStringMemberInconsistentlyInitialized(r,n){this.raise(De.EnumStringMemberInconsistentlyInitialized,r,n)}flowEnumMemberInit(){let r=this.state.startLoc,n=()=>this.match(12)||this.match(8);switch(this.state.type){case 135:{let s=this.parseNumericLiteral(this.state.value);return n()?{type:"number",loc:s.loc.start,value:s}:{type:"invalid",loc:r}}case 134:{let s=this.parseStringLiteral(this.state.value);return n()?{type:"string",loc:s.loc.start,value:s}:{type:"invalid",loc:r}}case 85:case 86:{let s=this.parseBooleanLiteral(this.match(85));return n()?{type:"boolean",loc:s.loc.start,value:s}:{type:"invalid",loc:r}}default:return{type:"invalid",loc:r}}}flowEnumMemberRaw(){let r=this.state.startLoc,n=this.parseIdentifier(!0),s=this.eat(29)?this.flowEnumMemberInit():{type:"none",loc:r};return{id:n,init:s}}flowEnumCheckExplicitTypeMismatch(r,n,s){let{explicitType:i}=n;i!==null&&i!==s&&this.flowEnumErrorInvalidMemberInitializer(r,n)}flowEnumMembers({enumName:r,explicitType:n}){let s=new Set,i={booleanMembers:[],numberMembers:[],stringMembers:[],defaultedMembers:[]},a=!1;for(;!this.match(8);){if(this.eat(21)){a=!0;break}let o=this.startNode(),{id:l,init:u}=this.flowEnumMemberRaw(),c=l.name;if(c==="")continue;/^[a-z]/.test(c)&&this.raise(De.EnumInvalidMemberName,l,{memberName:c,suggestion:c[0].toUpperCase()+c.slice(1),enumName:r}),s.has(c)&&this.raise(De.EnumDuplicateMemberName,l,{memberName:c,enumName:r}),s.add(c);let p={enumName:r,explicitType:n,memberName:c};switch(o.id=l,u.type){case"boolean":{this.flowEnumCheckExplicitTypeMismatch(u.loc,p,"boolean"),o.init=u.value,i.booleanMembers.push(this.finishNode(o,"EnumBooleanMember"));break}case"number":{this.flowEnumCheckExplicitTypeMismatch(u.loc,p,"number"),o.init=u.value,i.numberMembers.push(this.finishNode(o,"EnumNumberMember"));break}case"string":{this.flowEnumCheckExplicitTypeMismatch(u.loc,p,"string"),o.init=u.value,i.stringMembers.push(this.finishNode(o,"EnumStringMember"));break}case"invalid":throw this.flowEnumErrorInvalidMemberInitializer(u.loc,p);case"none":switch(n){case"boolean":this.flowEnumErrorBooleanMemberNotInitialized(u.loc,p);break;case"number":this.flowEnumErrorNumberMemberNotInitialized(u.loc,p);break;default:i.defaultedMembers.push(this.finishNode(o,"EnumDefaultedMember"))}}this.match(8)||this.expect(12)}return{members:i,hasUnknownMembers:a}}flowEnumStringMembers(r,n,{enumName:s}){if(r.length===0)return n;if(n.length===0)return r;if(n.length>r.length){for(let i of r)this.flowEnumErrorStringMemberInconsistentlyInitialized(i,{enumName:s});return n}else{for(let i of n)this.flowEnumErrorStringMemberInconsistentlyInitialized(i,{enumName:s});return r}}flowEnumParseExplicitType({enumName:r}){if(!this.eatContextual(102))return null;if(!ot(this.state.type))throw this.raise(De.EnumInvalidExplicitTypeUnknownSupplied,this.state.startLoc,{enumName:r});let{value:n}=this.state;return this.next(),n!=="boolean"&&n!=="number"&&n!=="string"&&n!=="symbol"&&this.raise(De.EnumInvalidExplicitType,this.state.startLoc,{enumName:r,invalidEnumType:n}),n}flowEnumBody(r,n){let s=n.name,i=n.loc.start,a=this.flowEnumParseExplicitType({enumName:s});this.expect(5);let{members:o,hasUnknownMembers:l}=this.flowEnumMembers({enumName:s,explicitType:a});switch(r.hasUnknownMembers=l,a){case"boolean":return r.explicitType=!0,r.members=o.booleanMembers,this.expect(8),this.finishNode(r,"EnumBooleanBody");case"number":return r.explicitType=!0,r.members=o.numberMembers,this.expect(8),this.finishNode(r,"EnumNumberBody");case"string":return r.explicitType=!0,r.members=this.flowEnumStringMembers(o.stringMembers,o.defaultedMembers,{enumName:s}),this.expect(8),this.finishNode(r,"EnumStringBody");case"symbol":return r.members=o.defaultedMembers,this.expect(8),this.finishNode(r,"EnumSymbolBody");default:{let u=()=>(r.members=[],this.expect(8),this.finishNode(r,"EnumStringBody"));r.explicitType=!1;let c=o.booleanMembers.length,p=o.numberMembers.length,d=o.stringMembers.length,y=o.defaultedMembers.length;if(!c&&!p&&!d&&!y)return u();if(!c&&!p)return r.members=this.flowEnumStringMembers(o.stringMembers,o.defaultedMembers,{enumName:s}),this.expect(8),this.finishNode(r,"EnumStringBody");if(!p&&!d&&c>=y){for(let E of o.defaultedMembers)this.flowEnumErrorBooleanMemberNotInitialized(E.loc.start,{enumName:s,memberName:E.id.name});return r.members=o.booleanMembers,this.expect(8),this.finishNode(r,"EnumBooleanBody")}else if(!c&&!d&&p>=y){for(let E of o.defaultedMembers)this.flowEnumErrorNumberMemberNotInitialized(E.loc.start,{enumName:s,memberName:E.id.name});return r.members=o.numberMembers,this.expect(8),this.finishNode(r,"EnumNumberBody")}else return this.raise(De.EnumInconsistentMemberValues,i,{enumName:s}),u()}}}flowParseEnumDeclaration(r){let n=this.parseIdentifier();return r.id=n,r.body=this.flowEnumBody(this.startNode(),n),this.finishNode(r,"EnumDeclaration")}jsxParseOpeningElementAfterName(r){return this.shouldParseTypes()&&(this.match(47)||this.match(51))&&(r.typeArguments=this.flowParseTypeParameterInstantiationInExpression()),super.jsxParseOpeningElementAfterName(r)}isLookaheadToken_lt(){let r=this.nextTokenStart();if(this.input.charCodeAt(r)===60){let n=this.input.charCodeAt(r+1);return n!==60&&n!==61}return!1}reScan_lt_gt(){let{type:r}=this.state;r===47?(this.state.pos-=1,this.readToken_lt()):r===48&&(this.state.pos-=1,this.readToken_gt())}reScan_lt(){let{type:r}=this.state;return r===51?(this.state.pos-=2,this.finishOp(47,1),47):r}maybeUnwrapTypeCastExpression(r){return r.type==="TypeCastExpression"?r.expression:r}},MH={__proto__:null,quot:'"',amp:"&",apos:"'",lt:"<",gt:">",nbsp:"\xA0",iexcl:"\xA1",cent:"\xA2",pound:"\xA3",curren:"\xA4",yen:"\xA5",brvbar:"\xA6",sect:"\xA7",uml:"\xA8",copy:"\xA9",ordf:"\xAA",laquo:"\xAB",not:"\xAC",shy:"\xAD",reg:"\xAE",macr:"\xAF",deg:"\xB0",plusmn:"\xB1",sup2:"\xB2",sup3:"\xB3",acute:"\xB4",micro:"\xB5",para:"\xB6",middot:"\xB7",cedil:"\xB8",sup1:"\xB9",ordm:"\xBA",raquo:"\xBB",frac14:"\xBC",frac12:"\xBD",frac34:"\xBE",iquest:"\xBF",Agrave:"\xC0",Aacute:"\xC1",Acirc:"\xC2",Atilde:"\xC3",Auml:"\xC4",Aring:"\xC5",AElig:"\xC6",Ccedil:"\xC7",Egrave:"\xC8",Eacute:"\xC9",Ecirc:"\xCA",Euml:"\xCB",Igrave:"\xCC",Iacute:"\xCD",Icirc:"\xCE",Iuml:"\xCF",ETH:"\xD0",Ntilde:"\xD1",Ograve:"\xD2",Oacute:"\xD3",Ocirc:"\xD4",Otilde:"\xD5",Ouml:"\xD6",times:"\xD7",Oslash:"\xD8",Ugrave:"\xD9",Uacute:"\xDA",Ucirc:"\xDB",Uuml:"\xDC",Yacute:"\xDD",THORN:"\xDE",szlig:"\xDF",agrave:"\xE0",aacute:"\xE1",acirc:"\xE2",atilde:"\xE3",auml:"\xE4",aring:"\xE5",aelig:"\xE6",ccedil:"\xE7",egrave:"\xE8",eacute:"\xE9",ecirc:"\xEA",euml:"\xEB",igrave:"\xEC",iacute:"\xED",icirc:"\xEE",iuml:"\xEF",eth:"\xF0",ntilde:"\xF1",ograve:"\xF2",oacute:"\xF3",ocirc:"\xF4",otilde:"\xF5",ouml:"\xF6",divide:"\xF7",oslash:"\xF8",ugrave:"\xF9",uacute:"\xFA",ucirc:"\xFB",uuml:"\xFC",yacute:"\xFD",thorn:"\xFE",yuml:"\xFF",OElig:"\u0152",oelig:"\u0153",Scaron:"\u0160",scaron:"\u0161",Yuml:"\u0178",fnof:"\u0192",circ:"\u02C6",tilde:"\u02DC",Alpha:"\u0391",Beta:"\u0392",Gamma:"\u0393",Delta:"\u0394",Epsilon:"\u0395",Zeta:"\u0396",Eta:"\u0397",Theta:"\u0398",Iota:"\u0399",Kappa:"\u039A",Lambda:"\u039B",Mu:"\u039C",Nu:"\u039D",Xi:"\u039E",Omicron:"\u039F",Pi:"\u03A0",Rho:"\u03A1",Sigma:"\u03A3",Tau:"\u03A4",Upsilon:"\u03A5",Phi:"\u03A6",Chi:"\u03A7",Psi:"\u03A8",Omega:"\u03A9",alpha:"\u03B1",beta:"\u03B2",gamma:"\u03B3",delta:"\u03B4",epsilon:"\u03B5",zeta:"\u03B6",eta:"\u03B7",theta:"\u03B8",iota:"\u03B9",kappa:"\u03BA",lambda:"\u03BB",mu:"\u03BC",nu:"\u03BD",xi:"\u03BE",omicron:"\u03BF",pi:"\u03C0",rho:"\u03C1",sigmaf:"\u03C2",sigma:"\u03C3",tau:"\u03C4",upsilon:"\u03C5",phi:"\u03C6",chi:"\u03C7",psi:"\u03C8",omega:"\u03C9",thetasym:"\u03D1",upsih:"\u03D2",piv:"\u03D6",ensp:"\u2002",emsp:"\u2003",thinsp:"\u2009",zwnj:"\u200C",zwj:"\u200D",lrm:"\u200E",rlm:"\u200F",ndash:"\u2013",mdash:"\u2014",lsquo:"\u2018",rsquo:"\u2019",sbquo:"\u201A",ldquo:"\u201C",rdquo:"\u201D",bdquo:"\u201E",dagger:"\u2020",Dagger:"\u2021",bull:"\u2022",hellip:"\u2026",permil:"\u2030",prime:"\u2032",Prime:"\u2033",lsaquo:"\u2039",rsaquo:"\u203A",oline:"\u203E",frasl:"\u2044",euro:"\u20AC",image:"\u2111",weierp:"\u2118",real:"\u211C",trade:"\u2122",alefsym:"\u2135",larr:"\u2190",uarr:"\u2191",rarr:"\u2192",darr:"\u2193",harr:"\u2194",crarr:"\u21B5",lArr:"\u21D0",uArr:"\u21D1",rArr:"\u21D2",dArr:"\u21D3",hArr:"\u21D4",forall:"\u2200",part:"\u2202",exist:"\u2203",empty:"\u2205",nabla:"\u2207",isin:"\u2208",notin:"\u2209",ni:"\u220B",prod:"\u220F",sum:"\u2211",minus:"\u2212",lowast:"\u2217",radic:"\u221A",prop:"\u221D",infin:"\u221E",ang:"\u2220",and:"\u2227",or:"\u2228",cap:"\u2229",cup:"\u222A",int:"\u222B",there4:"\u2234",sim:"\u223C",cong:"\u2245",asymp:"\u2248",ne:"\u2260",equiv:"\u2261",le:"\u2264",ge:"\u2265",sub:"\u2282",sup:"\u2283",nsub:"\u2284",sube:"\u2286",supe:"\u2287",oplus:"\u2295",otimes:"\u2297",perp:"\u22A5",sdot:"\u22C5",lceil:"\u2308",rceil:"\u2309",lfloor:"\u230A",rfloor:"\u230B",lang:"\u2329",rang:"\u232A",loz:"\u25CA",spades:"\u2660",clubs:"\u2663",hearts:"\u2665",diams:"\u2666"},si=Mn`jsx`({AttributeIsEmpty:"JSX attributes must only be assigned a non-empty expression.",MissingClosingTagElement:({openingTagName:e})=>`Expected corresponding JSX closing tag for <${e}>.`,MissingClosingTagFragment:"Expected corresponding JSX closing tag for <>.",UnexpectedSequenceExpression:"Sequence expressions cannot be directly nested inside JSX. Did you mean to wrap it in parentheses (...)?",UnexpectedToken:({unexpected:e,HTMLEntity:t})=>`Unexpected token \`${e}\`. Did you mean \`${t}\` or \`{'${e}'}\`?`,UnsupportedJsxValue:"JSX value should be either an expression or a quoted JSX text.",UnterminatedJsxContent:"Unterminated JSX contents.",UnwrappedAdjacentJSXElements:"Adjacent JSX elements must be wrapped in an enclosing tag. Did you want a JSX fragment <>...</>?"});function Ts(e){return e?e.type==="JSXOpeningFragment"||e.type==="JSXClosingFragment":!1}function Yi(e){if(e.type==="JSXIdentifier")return e.name;if(e.type==="JSXNamespacedName")return e.namespace.name+":"+e.name.name;if(e.type==="JSXMemberExpression")return Yi(e.object)+"."+Yi(e.property);throw new Error("Node had unexpected type: "+e.type)}var RH=e=>class extends e{jsxReadToken(){let r="",n=this.state.pos;for(;;){if(this.state.pos>=this.length)throw this.raise(si.UnterminatedJsxContent,this.state.startLoc);let s=this.input.charCodeAt(this.state.pos);switch(s){case 60:case 123:if(this.state.pos===this.state.start){s===60&&this.state.canStartJSXElement?(++this.state.pos,this.finishToken(143)):super.getTokenFromCode(s);return}r+=this.input.slice(n,this.state.pos),this.finishToken(142,r);return;case 38:r+=this.input.slice(n,this.state.pos),r+=this.jsxReadEntity(),n=this.state.pos;break;case 62:case 125:default:Ji(s)?(r+=this.input.slice(n,this.state.pos),r+=this.jsxReadNewLine(!0),n=this.state.pos):++this.state.pos}}}jsxReadNewLine(r){let n=this.input.charCodeAt(this.state.pos),s;return++this.state.pos,n===13&&this.input.charCodeAt(this.state.pos)===10?(++this.state.pos,s=r?`
`:`\r
`):s=String.fromCharCode(n),++this.state.curLine,this.state.lineStart=this.state.pos,s}jsxReadString(r){let n="",s=++this.state.pos;for(;;){if(this.state.pos>=this.length)throw this.raise(B.UnterminatedString,this.state.startLoc);let i=this.input.charCodeAt(this.state.pos);if(i===r)break;i===38?(n+=this.input.slice(s,this.state.pos),n+=this.jsxReadEntity(),s=this.state.pos):Ji(i)?(n+=this.input.slice(s,this.state.pos),n+=this.jsxReadNewLine(!1),s=this.state.pos):++this.state.pos}n+=this.input.slice(s,this.state.pos++),this.finishToken(134,n)}jsxReadEntity(){let r=++this.state.pos;if(this.codePointAtPos(this.state.pos)===35){++this.state.pos;let n=10;this.codePointAtPos(this.state.pos)===120&&(n=16,++this.state.pos);let s=this.readInt(n,void 0,!1,"bail");if(s!==null&&this.codePointAtPos(this.state.pos)===59)return++this.state.pos,String.fromCodePoint(s)}else{let n=0,s=!1;for(;n++<10&&this.state.pos<this.length&&!(s=this.codePointAtPos(this.state.pos)===59);)++this.state.pos;if(s){let i=this.input.slice(r,this.state.pos),a=MH[i];if(++this.state.pos,a)return a}}return this.state.pos=r,"&"}jsxReadWord(){let r,n=this.state.pos;do r=this.input.charCodeAt(++this.state.pos);while(Xi(r)||r===45);this.finishToken(141,this.input.slice(n,this.state.pos))}jsxParseIdentifier(){let r=this.startNode();return this.match(141)?r.name=this.state.value:Im(this.state.type)?r.name=Ss(this.state.type):this.unexpected(),this.next(),this.finishNode(r,"JSXIdentifier")}jsxParseNamespacedName(){let r=this.state.startLoc,n=this.jsxParseIdentifier();if(!this.eat(14))return n;let s=this.startNodeAt(r);return s.namespace=n,s.name=this.jsxParseIdentifier(),this.finishNode(s,"JSXNamespacedName")}jsxParseElementName(){let r=this.state.startLoc,n=this.jsxParseNamespacedName();if(n.type==="JSXNamespacedName")return n;for(;this.eat(16);){let s=this.startNodeAt(r);s.object=n,s.property=this.jsxParseIdentifier(),n=this.finishNode(s,"JSXMemberExpression")}return n}jsxParseAttributeValue(){let r;switch(this.state.type){case 5:return r=this.startNode(),this.setContext(at.brace),this.next(),r=this.jsxParseExpressionContainer(r,at.j_oTag),r.expression.type==="JSXEmptyExpression"&&this.raise(si.AttributeIsEmpty,r),r;case 143:case 134:return this.parseExprAtom();default:throw this.raise(si.UnsupportedJsxValue,this.state.startLoc)}}jsxParseEmptyExpression(){let r=this.startNodeAt(this.state.lastTokEndLoc);return this.finishNodeAt(r,"JSXEmptyExpression",this.state.startLoc)}jsxParseSpreadChild(r){return this.next(),r.expression=this.parseExpression(),this.setContext(at.j_expr),this.state.canStartJSXElement=!0,this.expect(8),this.finishNode(r,"JSXSpreadChild")}jsxParseExpressionContainer(r,n){if(this.match(8))r.expression=this.jsxParseEmptyExpression();else{let s=this.parseExpression();r.expression=s}return this.setContext(n),this.state.canStartJSXElement=!0,this.expect(8),this.finishNode(r,"JSXExpressionContainer")}jsxParseAttribute(){let r=this.startNode();return this.match(5)?(this.setContext(at.brace),this.next(),this.expect(21),r.argument=this.parseMaybeAssignAllowIn(),this.setContext(at.j_oTag),this.state.canStartJSXElement=!0,this.expect(8),this.finishNode(r,"JSXSpreadAttribute")):(r.name=this.jsxParseNamespacedName(),r.value=this.eat(29)?this.jsxParseAttributeValue():null,this.finishNode(r,"JSXAttribute"))}jsxParseOpeningElementAt(r){let n=this.startNodeAt(r);return this.eat(144)?this.finishNode(n,"JSXOpeningFragment"):(n.name=this.jsxParseElementName(),this.jsxParseOpeningElementAfterName(n))}jsxParseOpeningElementAfterName(r){let n=[];for(;!this.match(56)&&!this.match(144);)n.push(this.jsxParseAttribute());return r.attributes=n,r.selfClosing=this.eat(56),this.expect(144),this.finishNode(r,"JSXOpeningElement")}jsxParseClosingElementAt(r){let n=this.startNodeAt(r);return this.eat(144)?this.finishNode(n,"JSXClosingFragment"):(n.name=this.jsxParseElementName(),this.expect(144),this.finishNode(n,"JSXClosingElement"))}jsxParseElementAt(r){let n=this.startNodeAt(r),s=[],i=this.jsxParseOpeningElementAt(r),a=null;if(!i.selfClosing){e:for(;;)switch(this.state.type){case 143:if(r=this.state.startLoc,this.next(),this.eat(56)){a=this.jsxParseClosingElementAt(r);break e}s.push(this.jsxParseElementAt(r));break;case 142:s.push(this.parseLiteral(this.state.value,"JSXText"));break;case 5:{let o=this.startNode();this.setContext(at.brace),this.next(),this.match(21)?s.push(this.jsxParseSpreadChild(o)):s.push(this.jsxParseExpressionContainer(o,at.j_expr));break}default:this.unexpected()}Ts(i)&&!Ts(a)&&a!==null?this.raise(si.MissingClosingTagFragment,a):!Ts(i)&&Ts(a)?this.raise(si.MissingClosingTagElement,a,{openingTagName:Yi(i.name)}):!Ts(i)&&!Ts(a)&&Yi(a.name)!==Yi(i.name)&&this.raise(si.MissingClosingTagElement,a,{openingTagName:Yi(i.name)})}if(Ts(i)?(n.openingFragment=i,n.closingFragment=a):(n.openingElement=i,n.closingElement=a),n.children=s,this.match(47))throw this.raise(si.UnwrappedAdjacentJSXElements,this.state.startLoc);return Ts(i)?this.finishNode(n,"JSXFragment"):this.finishNode(n,"JSXElement")}jsxParseElement(){let r=this.state.startLoc;return this.next(),this.jsxParseElementAt(r)}setContext(r){let{context:n}=this.state;n[n.length-1]=r}parseExprAtom(r){return this.match(143)?this.jsxParseElement():this.match(47)&&this.input.charCodeAt(this.state.pos)!==33?(this.replaceToken(143),this.jsxParseElement()):super.parseExprAtom(r)}skipSpace(){this.curContext().preserveSpace||super.skipSpace()}getTokenFromCode(r){let n=this.curContext();if(n===at.j_expr){this.jsxReadToken();return}if(n===at.j_oTag||n===at.j_cTag){if(jn(r)){this.jsxReadWord();return}if(r===62){++this.state.pos,this.finishToken(144);return}if((r===34||r===39)&&n===at.j_oTag){this.jsxReadString(r);return}}if(r===60&&this.state.canStartJSXElement&&this.input.charCodeAt(this.state.pos+1)!==33){++this.state.pos,this.finishToken(143);return}super.getTokenFromCode(r)}updateContext(r){let{context:n,type:s}=this.state;if(s===56&&r===143)n.splice(-2,2,at.j_cTag),this.state.canStartJSXElement=!1;else if(s===143)n.push(at.j_oTag);else if(s===144){let i=n[n.length-1];i===at.j_oTag&&r===56||i===at.j_cTag?(n.pop(),this.state.canStartJSXElement=n[n.length-1]===at.j_expr):(this.setContext(at.j_expr),this.state.canStartJSXElement=!0)}else this.state.canStartJSXElement=tH(s)}},bm=class extends ho{constructor(...t){super(...t),this.tsNames=new Map}},Em=class extends mo{constructor(...t){super(...t),this.importsStack=[]}createScope(t){return this.importsStack.push(new Set),new bm(t)}enter(t){t===256&&this.importsStack.push(new Set),super.enter(t)}exit(){let t=super.exit();return t===256&&this.importsStack.pop(),t}hasImport(t,r){let n=this.importsStack.length;if(this.importsStack[n-1].has(t))return!0;if(!r&&n>1){for(let s=0;s<n-1;s++)if(this.importsStack[s].has(t))return!0}return!1}declareName(t,r,n){if(r&4096){this.hasImport(t,!0)&&this.parser.raise(B.VarRedeclaration,n,{identifierName:t}),this.importsStack[this.importsStack.length-1].add(t);return}let s=this.currentScope(),i=s.tsNames.get(t)||0;if(r&1024){this.maybeExportDefined(s,t),s.tsNames.set(t,i|16);return}super.declareName(t,r,n),r&2&&(r&1||(this.checkRedeclarationInScope(s,t,r,n),this.maybeExportDefined(s,t)),i=i|1),r&256&&(i=i|2),r&512&&(i=i|4),r&128&&(i=i|8),i&&s.tsNames.set(t,i)}isRedeclaredInScope(t,r,n){let s=t.tsNames.get(r);if((s&2)>0){if(n&256){let i=!!(n&512),a=(s&4)>0;return i!==a}return!0}return n&128&&(s&8)>0?t.names.get(r)&2?!!(n&1):!1:n&2&&(s&1)>0?!0:super.isRedeclaredInScope(t,r,n)}checkLocalExport(t){let{name:r}=t;if(this.hasImport(r))return;let n=this.scopeStack.length;for(let s=n-1;s>=0;s--){let a=this.scopeStack[s].tsNames.get(r);if((a&1)>0||(a&16)>0)return}super.checkLocalExport(t)}},ox=e=>e.type==="ParenthesizedExpression"?ox(e.expression):e,Tm=class extends gm{toAssignable(t,r=!1){var n,s;let i;switch((t.type==="ParenthesizedExpression"||(n=t.extra)!=null&&n.parenthesized)&&(i=ox(t),r?i.type==="Identifier"?this.expressionScope.recordArrowParameterBindingError(B.InvalidParenthesizedAssignment,t):i.type!=="MemberExpression"&&!this.isOptionalMemberExpression(i)&&this.raise(B.InvalidParenthesizedAssignment,t):this.raise(B.InvalidParenthesizedAssignment,t)),t.type){case"Identifier":case"ObjectPattern":case"ArrayPattern":case"AssignmentPattern":case"RestElement":break;case"ObjectExpression":t.type="ObjectPattern";for(let o=0,l=t.properties.length,u=l-1;o<l;o++){var a;let c=t.properties[o],p=o===u;this.toAssignableObjectExpressionProp(c,p,r),p&&c.type==="RestElement"&&(a=t.extra)!=null&&a.trailingCommaLoc&&this.raise(B.RestTrailingComma,t.extra.trailingCommaLoc)}break;case"ObjectProperty":{let{key:o,value:l}=t;this.isPrivateName(o)&&this.classScope.usePrivateName(this.getPrivateNameSV(o),o.loc.start),this.toAssignable(l,r);break}case"SpreadElement":throw new Error("Internal @babel/parser error (this is a bug, please report it). SpreadElement should be converted by .toAssignable's caller.");case"ArrayExpression":t.type="ArrayPattern",this.toAssignableList(t.elements,(s=t.extra)==null?void 0:s.trailingCommaLoc,r);break;case"AssignmentExpression":t.operator!=="="&&this.raise(B.MissingEqInAssignment,t.left.loc.end),t.type="AssignmentPattern",delete t.operator,this.toAssignable(t.left,r);break;case"ParenthesizedExpression":this.toAssignable(i,r);break}}toAssignableObjectExpressionProp(t,r,n){if(t.type==="ObjectMethod")this.raise(t.kind==="get"||t.kind==="set"?B.PatternHasAccessor:B.PatternHasMethod,t.key);else if(t.type==="SpreadElement"){t.type="RestElement";let s=t.argument;this.checkToRestConversion(s,!1),this.toAssignable(s,n),r||this.raise(B.RestTrailingComma,t)}else this.toAssignable(t,n)}toAssignableList(t,r,n){let s=t.length-1;for(let i=0;i<=s;i++){let a=t[i];a&&(this.toAssignableListItem(t,i,n),a.type==="RestElement"&&(i<s?this.raise(B.RestTrailingComma,a):r&&this.raise(B.RestTrailingComma,r)))}}toAssignableListItem(t,r,n){let s=t[r];if(s.type==="SpreadElement"){s.type="RestElement";let i=s.argument;this.checkToRestConversion(i,!0),this.toAssignable(i,n)}else this.toAssignable(s,n)}isAssignable(t,r){switch(t.type){case"Identifier":case"ObjectPattern":case"ArrayPattern":case"AssignmentPattern":case"RestElement":return!0;case"ObjectExpression":{let n=t.properties.length-1;return t.properties.every((s,i)=>s.type!=="ObjectMethod"&&(i===n||s.type!=="SpreadElement")&&this.isAssignable(s))}case"ObjectProperty":return this.isAssignable(t.value);case"SpreadElement":return this.isAssignable(t.argument);case"ArrayExpression":return t.elements.every(n=>n===null||this.isAssignable(n));case"AssignmentExpression":return t.operator==="=";case"ParenthesizedExpression":return this.isAssignable(t.expression);case"MemberExpression":case"OptionalMemberExpression":return!r;default:return!1}}toReferencedList(t,r){return t}toReferencedListDeep(t,r){this.toReferencedList(t,r);for(let n of t)(n==null?void 0:n.type)==="ArrayExpression"&&this.toReferencedListDeep(n.elements)}parseSpread(t){let r=this.startNode();return this.next(),r.argument=this.parseMaybeAssignAllowIn(t,void 0),this.finishNode(r,"SpreadElement")}parseRestBinding(){let t=this.startNode();return this.next(),t.argument=this.parseBindingAtom(),this.finishNode(t,"RestElement")}parseBindingAtom(){switch(this.state.type){case 0:{let t=this.startNode();return this.next(),t.elements=this.parseBindingList(3,93,1),this.finishNode(t,"ArrayPattern")}case 5:return this.parseObjectLike(8,!0)}return this.parseIdentifier()}parseBindingList(t,r,n){let s=n&1,i=[],a=!0;for(;!this.eat(t);)if(a?a=!1:this.expect(12),s&&this.match(12))i.push(null);else{if(this.eat(t))break;if(this.match(21)){let o=this.parseRestBinding();if((this.hasPlugin("flow")||n&2)&&(o=this.parseFunctionParamType(o)),i.push(o),!this.checkCommaAfterRest(r)){this.expect(t);break}}else{let o=[];if(n&2)for(this.match(26)&&this.hasPlugin("decorators")&&this.raise(B.UnsupportedParameterDecorator,this.state.startLoc);this.match(26);)o.push(this.parseDecorator());i.push(this.parseBindingElement(n,o))}}return i}parseBindingRestProperty(t){return this.next(),t.argument=this.parseIdentifier(),this.checkCommaAfterRest(125),this.finishNode(t,"RestElement")}parseBindingProperty(){let{type:t,startLoc:r}=this.state;if(t===21)return this.parseBindingRestProperty(this.startNode());let n=this.startNode();return t===139?(this.expectPlugin("destructuringPrivate",r),this.classScope.usePrivateName(this.state.value,r),n.key=this.parsePrivateName()):this.parsePropertyName(n),n.method=!1,this.parseObjPropValue(n,r,!1,!1,!0,!1)}parseBindingElement(t,r){let n=this.parseMaybeDefault();(this.hasPlugin("flow")||t&2)&&this.parseFunctionParamType(n);let s=this.parseMaybeDefault(n.loc.start,n);return r.length&&(n.decorators=r),s}parseFunctionParamType(t){return t}parseMaybeDefault(t,r){if(t!=null||(t=this.state.startLoc),r=r!=null?r:this.parseBindingAtom(),!this.eat(29))return r;let n=this.startNodeAt(t);return n.left=r,n.right=this.parseMaybeAssignAllowIn(),this.finishNode(n,"AssignmentPattern")}isValidLVal(t,r,n){switch(t){case"AssignmentPattern":return"left";case"RestElement":return"argument";case"ObjectProperty":return"value";case"ParenthesizedExpression":return"expression";case"ArrayPattern":return"elements";case"ObjectPattern":return"properties"}return!1}isOptionalMemberExpression(t){return t.type==="OptionalMemberExpression"}checkLVal(t,r,n=64,s=!1,i=!1,a=!1){var o;let l=t.type;if(this.isObjectMethod(t))return;let u=this.isOptionalMemberExpression(t);if(u||l==="MemberExpression"){u&&(this.expectPlugin("optionalChainingAssign",t.loc.start),r.type!=="AssignmentExpression"&&this.raise(B.InvalidLhsOptionalChaining,t,{ancestor:r})),n!==64&&this.raise(B.InvalidPropertyBindingPattern,t);return}if(l==="Identifier"){this.checkIdentifier(t,n,i);let{name:f}=t;s&&(s.has(f)?this.raise(B.ParamDupe,t):s.add(f));return}let c=this.isValidLVal(l,!(a||(o=t.extra)!=null&&o.parenthesized)&&r.type==="AssignmentExpression",n);if(c===!0)return;if(c===!1){let f=n===64?B.InvalidLhs:B.InvalidLhsBinding;this.raise(f,t,{ancestor:r});return}let p,d;typeof c=="string"?(p=c,d=l==="ParenthesizedExpression"):[p,d]=c;let y=l==="ArrayPattern"||l==="ObjectPattern"?{type:l}:r,E=t[p];if(Array.isArray(E))for(let f of E)f&&this.checkLVal(f,y,n,s,i,d);else E&&this.checkLVal(E,y,n,s,i,d)}checkIdentifier(t,r,n=!1){this.state.strict&&(n?rx(t.name,this.inModule):tx(t.name))&&(r===64?this.raise(B.StrictEvalArguments,t,{referenceName:t.name}):this.raise(B.StrictEvalArgumentsBinding,t,{bindingName:t.name})),r&8192&&t.name==="let"&&this.raise(B.LetInLexicalBinding,t),r&64||this.declareNameFromIdentifier(t,r)}declareNameFromIdentifier(t,r){this.scope.declareName(t.name,r,t.loc.start)}checkToRestConversion(t,r){switch(t.type){case"ParenthesizedExpression":this.checkToRestConversion(t.expression,r);break;case"Identifier":case"MemberExpression":break;case"ArrayExpression":case"ObjectExpression":if(r)break;default:this.raise(B.InvalidRestAssignmentPattern,t)}}checkCommaAfterRest(t){return this.match(12)?(this.raise(this.lookaheadCharCode()===t?B.RestTrailingComma:B.ElementAfterRest,this.state.startLoc),!0):!1}};function qH(e){if(e==null)throw new Error(`Unexpected ${e} value.`);return e}function WS(e){if(!e)throw new Error("Assert fail")}var me=Mn`typescript`({AbstractMethodHasImplementation:({methodName:e})=>`Method '${e}' cannot have an implementation because it is marked abstract.`,AbstractPropertyHasInitializer:({propertyName:e})=>`Property '${e}' cannot have an initializer because it is marked abstract.`,AccessorCannotBeOptional:"An 'accessor' property cannot be declared optional.",AccessorCannotDeclareThisParameter:"'get' and 'set' accessors cannot declare 'this' parameters.",AccessorCannotHaveTypeParameters:"An accessor cannot have type parameters.",ClassMethodHasDeclare:"Class methods cannot have the 'declare' modifier.",ClassMethodHasReadonly:"Class methods cannot have the 'readonly' modifier.",ConstInitializerMustBeStringOrNumericLiteralOrLiteralEnumReference:"A 'const' initializer in an ambient context must be a string or numeric literal or literal enum reference.",ConstructorHasTypeParameters:"Type parameters cannot appear on a constructor declaration.",DeclareAccessor:({kind:e})=>`'declare' is not allowed in ${e}ters.`,DeclareClassFieldHasInitializer:"Initializers are not allowed in ambient contexts.",DeclareFunctionHasImplementation:"An implementation cannot be declared in ambient contexts.",DuplicateAccessibilityModifier:({modifier:e})=>"Accessibility modifier already seen.",DuplicateModifier:({modifier:e})=>`Duplicate modifier: '${e}'.`,EmptyHeritageClauseType:({token:e})=>`'${e}' list cannot be empty.`,EmptyTypeArguments:"Type argument list cannot be empty.",EmptyTypeParameters:"Type parameter list cannot be empty.",ExpectedAmbientAfterExportDeclare:"'export declare' must be followed by an ambient declaration.",ImportAliasHasImportType:"An import alias can not use 'import type'.",ImportReflectionHasImportType:"An `import module` declaration can not use `type` modifier",IncompatibleModifiers:({modifiers:e})=>`'${e[0]}' modifier cannot be used with '${e[1]}' modifier.`,IndexSignatureHasAbstract:"Index signatures cannot have the 'abstract' modifier.",IndexSignatureHasAccessibility:({modifier:e})=>`Index signatures cannot have an accessibility modifier ('${e}').`,IndexSignatureHasDeclare:"Index signatures cannot have the 'declare' modifier.",IndexSignatureHasOverride:"'override' modifier cannot appear on an index signature.",IndexSignatureHasStatic:"Index signatures cannot have the 'static' modifier.",InitializerNotAllowedInAmbientContext:"Initializers are not allowed in ambient contexts.",InvalidHeritageClauseType:({token:e})=>`'${e}' list can only include identifiers or qualified-names with optional type arguments.`,InvalidModifierOnTypeMember:({modifier:e})=>`'${e}' modifier cannot appear on a type member.`,InvalidModifierOnTypeParameter:({modifier:e})=>`'${e}' modifier cannot appear on a type parameter.`,InvalidModifierOnTypeParameterPositions:({modifier:e})=>`'${e}' modifier can only appear on a type parameter of a class, interface or type alias.`,InvalidModifiersOrder:({orderedModifiers:e})=>`'${e[0]}' modifier must precede '${e[1]}' modifier.`,InvalidPropertyAccessAfterInstantiationExpression:"Invalid property access after an instantiation expression. You can either wrap the instantiation expression in parentheses, or delete the type arguments.",InvalidTupleMemberLabel:"Tuple members must be labeled with a simple identifier.",MissingInterfaceName:"'interface' declarations must be followed by an identifier.",NonAbstractClassHasAbstractMethod:"Abstract methods can only appear within an abstract class.",NonClassMethodPropertyHasAbstractModifer:"'abstract' modifier can only appear on a class, method, or property declaration.",OptionalTypeBeforeRequired:"A required element cannot follow an optional element.",OverrideNotInSubClass:"This member cannot have an 'override' modifier because its containing class does not extend another class.",PatternIsOptional:"A binding pattern parameter cannot be optional in an implementation signature.",PrivateElementHasAbstract:"Private elements cannot have the 'abstract' modifier.",PrivateElementHasAccessibility:({modifier:e})=>`Private elements cannot have an accessibility modifier ('${e}').`,ReadonlyForMethodSignature:"'readonly' modifier can only appear on a property declaration or index signature.",ReservedArrowTypeParam:"This syntax is reserved in files with the .mts or .cts extension. Add a trailing comma, as in `<T,>() => ...`.",ReservedTypeAssertion:"This syntax is reserved in files with the .mts or .cts extension. Use an `as` expression instead.",SetAccessorCannotHaveOptionalParameter:"A 'set' accessor cannot have an optional parameter.",SetAccessorCannotHaveRestParameter:"A 'set' accessor cannot have rest parameter.",SetAccessorCannotHaveReturnType:"A 'set' accessor cannot have a return type annotation.",SingleTypeParameterWithoutTrailingComma:({typeParameterName:e})=>`Single type parameter ${e} should have a trailing comma. Example usage: <${e},>.`,StaticBlockCannotHaveModifier:"Static class blocks cannot have any modifier.",TupleOptionalAfterType:"A labeled tuple optional element must be declared using a question mark after the name and before the colon (`name?: type`), rather than after the type (`name: type?`).",TypeAnnotationAfterAssign:"Type annotations must come before default assignments, e.g. instead of `age = 25: number` use `age: number = 25`.",TypeImportCannotSpecifyDefaultAndNamed:"A type-only import can specify a default import or named bindings, but not both.",TypeModifierIsUsedInTypeExports:"The 'type' modifier cannot be used on a named export when 'export type' is used on its export statement.",TypeModifierIsUsedInTypeImports:"The 'type' modifier cannot be used on a named import when 'import type' is used on its import statement.",UnexpectedParameterModifier:"A parameter property is only allowed in a constructor implementation.",UnexpectedReadonly:"'readonly' type modifier is only permitted on array and tuple literal types.",UnexpectedTypeAnnotation:"Did not expect a type annotation here.",UnexpectedTypeCastInParameter:"Unexpected type cast in parameter position.",UnsupportedImportTypeArgument:"Argument in a type import must be a string literal.",UnsupportedParameterPropertyKind:"A parameter property may not be declared using a binding pattern.",UnsupportedSignatureParameterKind:({type:e})=>`Name in a signature must be an Identifier, ObjectPattern or ArrayPattern, instead got ${e}.`});function UH(e){switch(e){case"any":return"TSAnyKeyword";case"boolean":return"TSBooleanKeyword";case"bigint":return"TSBigIntKeyword";case"never":return"TSNeverKeyword";case"number":return"TSNumberKeyword";case"object":return"TSObjectKeyword";case"string":return"TSStringKeyword";case"symbol":return"TSSymbolKeyword";case"undefined":return"TSUndefinedKeyword";case"unknown":return"TSUnknownKeyword";default:return}}function KS(e){return e==="private"||e==="public"||e==="protected"}function VH(e){return e==="in"||e==="out"}var $H=e=>class extends e{constructor(...r){super(...r),this.tsParseInOutModifiers=this.tsParseModifiers.bind(this,{allowedModifiers:["in","out"],disallowedModifiers:["const","public","private","protected","readonly","declare","abstract","override"],errorTemplate:me.InvalidModifierOnTypeParameter}),this.tsParseConstModifier=this.tsParseModifiers.bind(this,{allowedModifiers:["const"],disallowedModifiers:["in","out"],errorTemplate:me.InvalidModifierOnTypeParameterPositions}),this.tsParseInOutConstModifiers=this.tsParseModifiers.bind(this,{allowedModifiers:["in","out","const"],disallowedModifiers:["public","private","protected","readonly","declare","abstract","override"],errorTemplate:me.InvalidModifierOnTypeParameter})}getScopeHandler(){return Em}tsIsIdentifier(){return ot(this.state.type)}tsTokenCanFollowModifier(){return this.match(0)||this.match(5)||this.match(55)||this.match(21)||this.match(139)||this.isLiteralPropertyName()}tsNextTokenOnSameLineAndCanFollowModifier(){return this.next(),this.hasPrecedingLineBreak()?!1:this.tsTokenCanFollowModifier()}tsNextTokenCanFollowModifier(){return this.match(106)?(this.next(),this.tsTokenCanFollowModifier()):this.tsNextTokenOnSameLineAndCanFollowModifier()}tsParseModifier(r,n){if(!ot(this.state.type)&&this.state.type!==58&&this.state.type!==75)return;let s=this.state.value;if(r.includes(s)){if(n&&this.tsIsStartOfStaticBlocks())return;if(this.tsTryParse(this.tsNextTokenCanFollowModifier.bind(this)))return s}}tsParseModifiers({allowedModifiers:r,disallowedModifiers:n,stopOnStartOfClassStaticBlock:s,errorTemplate:i=me.InvalidModifierOnTypeMember},a){let o=(u,c,p,d)=>{c===p&&a[d]&&this.raise(me.InvalidModifiersOrder,u,{orderedModifiers:[p,d]})},l=(u,c,p,d)=>{(a[p]&&c===d||a[d]&&c===p)&&this.raise(me.IncompatibleModifiers,u,{modifiers:[p,d]})};for(;;){let{startLoc:u}=this.state,c=this.tsParseModifier(r.concat(n!=null?n:[]),s);if(!c)break;KS(c)?a.accessibility?this.raise(me.DuplicateAccessibilityModifier,u,{modifier:c}):(o(u,c,c,"override"),o(u,c,c,"static"),o(u,c,c,"readonly"),a.accessibility=c):VH(c)?(a[c]&&this.raise(me.DuplicateModifier,u,{modifier:c}),a[c]=!0,o(u,c,"in","out")):(hasOwnProperty.call(a,c)?this.raise(me.DuplicateModifier,u,{modifier:c}):(o(u,c,"static","readonly"),o(u,c,"static","override"),o(u,c,"override","readonly"),o(u,c,"abstract","override"),l(u,c,"declare","override"),l(u,c,"static","abstract")),a[c]=!0),n!=null&&n.includes(c)&&this.raise(i,u,{modifier:c})}}tsIsListTerminator(r){switch(r){case"EnumMembers":case"TypeMembers":return this.match(8);case"HeritageClauseElement":return this.match(5);case"TupleElementTypes":return this.match(3);case"TypeParametersOrArguments":return this.match(48)}}tsParseList(r,n){let s=[];for(;!this.tsIsListTerminator(r);)s.push(n());return s}tsParseDelimitedList(r,n,s){return qH(this.tsParseDelimitedListWorker(r,n,!0,s))}tsParseDelimitedListWorker(r,n,s,i){let a=[],o=-1;for(;!this.tsIsListTerminator(r);){o=-1;let l=n();if(l==null)return;if(a.push(l),this.eat(12)){o=this.state.lastTokStartLoc.index;continue}if(this.tsIsListTerminator(r))break;s&&this.expect(12);return}return i&&(i.value=o),a}tsParseBracketedList(r,n,s,i,a){i||(s?this.expect(0):this.expect(47));let o=this.tsParseDelimitedList(r,n,a);return s?this.expect(3):this.expect(48),o}tsParseImportType(){let r=this.startNode();return this.expect(83),this.expect(10),this.match(134)?r.argument=this.parseStringLiteral(this.state.value):(this.raise(me.UnsupportedImportTypeArgument,this.state.startLoc),r.argument=super.parseExprAtom()),this.eat(12)&&!this.match(11)?(r.options=super.parseMaybeAssignAllowIn(),this.eat(12)):r.options=null,this.expect(11),this.eat(16)&&(r.qualifier=this.tsParseEntityName(3)),this.match(47)&&(r.typeParameters=this.tsParseTypeArguments()),this.finishNode(r,"TSImportType")}tsParseEntityName(r){let n;if(r&1&&this.match(78))if(r&2)n=this.parseIdentifier(!0);else{let s=this.startNode();this.next(),n=this.finishNode(s,"ThisExpression")}else n=this.parseIdentifier(!!(r&1));for(;this.eat(16);){let s=this.startNodeAtNode(n);s.left=n,s.right=this.parseIdentifier(!!(r&1)),n=this.finishNode(s,"TSQualifiedName")}return n}tsParseTypeReference(){let r=this.startNode();return r.typeName=this.tsParseEntityName(1),!this.hasPrecedingLineBreak()&&this.match(47)&&(r.typeParameters=this.tsParseTypeArguments()),this.finishNode(r,"TSTypeReference")}tsParseThisTypePredicate(r){this.next();let n=this.startNodeAtNode(r);return n.parameterName=r,n.typeAnnotation=this.tsParseTypeAnnotation(!1),n.asserts=!1,this.finishNode(n,"TSTypePredicate")}tsParseThisTypeNode(){let r=this.startNode();return this.next(),this.finishNode(r,"TSThisType")}tsParseTypeQuery(){let r=this.startNode();return this.expect(87),this.match(83)?r.exprName=this.tsParseImportType():r.exprName=this.tsParseEntityName(3),!this.hasPrecedingLineBreak()&&this.match(47)&&(r.typeParameters=this.tsParseTypeArguments()),this.finishNode(r,"TSTypeQuery")}tsParseTypeParameter(r){let n=this.startNode();return r(n),n.name=this.tsParseTypeParameterName(),n.constraint=this.tsEatThenParseType(81),n.default=this.tsEatThenParseType(29),this.finishNode(n,"TSTypeParameter")}tsTryParseTypeParameters(r){if(this.match(47))return this.tsParseTypeParameters(r)}tsParseTypeParameters(r){let n=this.startNode();this.match(47)||this.match(143)?this.next():this.unexpected();let s={value:-1};return n.params=this.tsParseBracketedList("TypeParametersOrArguments",this.tsParseTypeParameter.bind(this,r),!1,!0,s),n.params.length===0&&this.raise(me.EmptyTypeParameters,n),s.value!==-1&&this.addExtra(n,"trailingComma",s.value),this.finishNode(n,"TSTypeParameterDeclaration")}tsFillSignature(r,n){let s=r===19,i="parameters",a="typeAnnotation";n.typeParameters=this.tsTryParseTypeParameters(this.tsParseConstModifier),this.expect(10),n[i]=this.tsParseBindingListForSignature(),s?n[a]=this.tsParseTypeOrTypePredicateAnnotation(r):this.match(r)&&(n[a]=this.tsParseTypeOrTypePredicateAnnotation(r))}tsParseBindingListForSignature(){let r=super.parseBindingList(11,41,2);for(let n of r){let{type:s}=n;(s==="AssignmentPattern"||s==="TSParameterProperty")&&this.raise(me.UnsupportedSignatureParameterKind,n,{type:s})}return r}tsParseTypeMemberSemicolon(){!this.eat(12)&&!this.isLineTerminator()&&this.expect(13)}tsParseSignatureMember(r,n){return this.tsFillSignature(14,n),this.tsParseTypeMemberSemicolon(),this.finishNode(n,r)}tsIsUnambiguouslyIndexSignature(){return this.next(),ot(this.state.type)?(this.next(),this.match(14)):!1}tsTryParseIndexSignature(r){if(!(this.match(0)&&this.tsLookAhead(this.tsIsUnambiguouslyIndexSignature.bind(this))))return;this.expect(0);let n=this.parseIdentifier();n.typeAnnotation=this.tsParseTypeAnnotation(),this.resetEndLocation(n),this.expect(3),r.parameters=[n];let s=this.tsTryParseTypeAnnotation();return s&&(r.typeAnnotation=s),this.tsParseTypeMemberSemicolon(),this.finishNode(r,"TSIndexSignature")}tsParsePropertyOrMethodSignature(r,n){this.eat(17)&&(r.optional=!0);let s=r;if(this.match(10)||this.match(47)){n&&this.raise(me.ReadonlyForMethodSignature,r);let i=s;i.kind&&this.match(47)&&this.raise(me.AccessorCannotHaveTypeParameters,this.state.curPosition()),this.tsFillSignature(14,i),this.tsParseTypeMemberSemicolon();let a="parameters",o="typeAnnotation";if(i.kind==="get")i[a].length>0&&(this.raise(B.BadGetterArity,this.state.curPosition()),this.isThisParam(i[a][0])&&this.raise(me.AccessorCannotDeclareThisParameter,this.state.curPosition()));else if(i.kind==="set"){if(i[a].length!==1)this.raise(B.BadSetterArity,this.state.curPosition());else{let l=i[a][0];this.isThisParam(l)&&this.raise(me.AccessorCannotDeclareThisParameter,this.state.curPosition()),l.type==="Identifier"&&l.optional&&this.raise(me.SetAccessorCannotHaveOptionalParameter,this.state.curPosition()),l.type==="RestElement"&&this.raise(me.SetAccessorCannotHaveRestParameter,this.state.curPosition())}i[o]&&this.raise(me.SetAccessorCannotHaveReturnType,i[o])}else i.kind="method";return this.finishNode(i,"TSMethodSignature")}else{let i=s;n&&(i.readonly=!0);let a=this.tsTryParseTypeAnnotation();return a&&(i.typeAnnotation=a),this.tsParseTypeMemberSemicolon(),this.finishNode(i,"TSPropertySignature")}}tsParseTypeMember(){let r=this.startNode();if(this.match(10)||this.match(47))return this.tsParseSignatureMember("TSCallSignatureDeclaration",r);if(this.match(77)){let s=this.startNode();return this.next(),this.match(10)||this.match(47)?this.tsParseSignatureMember("TSConstructSignatureDeclaration",r):(r.key=this.createIdentifier(s,"new"),this.tsParsePropertyOrMethodSignature(r,!1))}this.tsParseModifiers({allowedModifiers:["readonly"],disallowedModifiers:["declare","abstract","private","protected","public","static","override"]},r);let n=this.tsTryParseIndexSignature(r);return n||(super.parsePropertyName(r),!r.computed&&r.key.type==="Identifier"&&(r.key.name==="get"||r.key.name==="set")&&this.tsTokenCanFollowModifier()&&(r.kind=r.key.name,super.parsePropertyName(r)),this.tsParsePropertyOrMethodSignature(r,!!r.readonly))}tsParseTypeLiteral(){let r=this.startNode();return r.members=this.tsParseObjectTypeMembers(),this.finishNode(r,"TSTypeLiteral")}tsParseObjectTypeMembers(){this.expect(5);let r=this.tsParseList("TypeMembers",this.tsParseTypeMember.bind(this));return this.expect(8),r}tsIsStartOfMappedType(){return this.next(),this.eat(53)?this.isContextual(122):(this.isContextual(122)&&this.next(),!this.match(0)||(this.next(),!this.tsIsIdentifier())?!1:(this.next(),this.match(58)))}tsParseMappedType(){let r=this.startNode();this.expect(5),this.match(53)?(r.readonly=this.state.value,this.next(),this.expectContextual(122)):this.eatContextual(122)&&(r.readonly=!0),this.expect(0);{let n=this.startNode();n.name=this.tsParseTypeParameterName(),n.constraint=this.tsExpectThenParseType(58),r.typeParameter=this.finishNode(n,"TSTypeParameter")}return r.nameType=this.eatContextual(93)?this.tsParseType():null,this.expect(3),this.match(53)?(r.optional=this.state.value,this.next(),this.expect(17)):this.eat(17)&&(r.optional=!0),r.typeAnnotation=this.tsTryParseType(),this.semicolon(),this.expect(8),this.finishNode(r,"TSMappedType")}tsParseTupleType(){let r=this.startNode();r.elementTypes=this.tsParseBracketedList("TupleElementTypes",this.tsParseTupleElementType.bind(this),!0,!1);let n=!1;return r.elementTypes.forEach(s=>{let{type:i}=s;n&&i!=="TSRestType"&&i!=="TSOptionalType"&&!(i==="TSNamedTupleMember"&&s.optional)&&this.raise(me.OptionalTypeBeforeRequired,s),n||(n=i==="TSNamedTupleMember"&&s.optional||i==="TSOptionalType")}),this.finishNode(r,"TSTupleType")}tsParseTupleElementType(){let r=this.state.startLoc,n=this.eat(21),{startLoc:s}=this.state,i,a,o,l,c=en(this.state.type)?this.lookaheadCharCode():null;if(c===58)i=!0,o=!1,a=this.parseIdentifier(!0),this.expect(14),l=this.tsParseType();else if(c===63){o=!0;let p=this.state.value,d=this.tsParseNonArrayType();this.lookaheadCharCode()===58?(i=!0,a=this.createIdentifier(this.startNodeAt(s),p),this.expect(17),this.expect(14),l=this.tsParseType()):(i=!1,l=d,this.expect(17))}else l=this.tsParseType(),o=this.eat(17),i=this.eat(14);if(i){let p;a?(p=this.startNodeAt(s),p.optional=o,p.label=a,p.elementType=l,this.eat(17)&&(p.optional=!0,this.raise(me.TupleOptionalAfterType,this.state.lastTokStartLoc))):(p=this.startNodeAt(s),p.optional=o,this.raise(me.InvalidTupleMemberLabel,l),p.label=l,p.elementType=this.tsParseType()),l=this.finishNode(p,"TSNamedTupleMember")}else if(o){let p=this.startNodeAt(s);p.typeAnnotation=l,l=this.finishNode(p,"TSOptionalType")}if(n){let p=this.startNodeAt(r);p.typeAnnotation=l,l=this.finishNode(p,"TSRestType")}return l}tsParseParenthesizedType(){let r=this.startNode();return this.expect(10),r.typeAnnotation=this.tsParseType(),this.expect(11),this.finishNode(r,"TSParenthesizedType")}tsParseFunctionOrConstructorType(r,n){let s=this.startNode();return r==="TSConstructorType"&&(s.abstract=!!n,n&&this.next(),this.next()),this.tsInAllowConditionalTypesContext(()=>this.tsFillSignature(19,s)),this.finishNode(s,r)}tsParseLiteralTypeNode(){let r=this.startNode();switch(this.state.type){case 135:case 136:case 134:case 85:case 86:r.literal=super.parseExprAtom();break;default:this.unexpected()}return this.finishNode(r,"TSLiteralType")}tsParseTemplateLiteralType(){{let r=this.startNode();return r.literal=super.parseTemplate(!1),this.finishNode(r,"TSLiteralType")}}parseTemplateSubstitution(){return this.state.inType?this.tsParseType():super.parseTemplateSubstitution()}tsParseThisTypeOrThisTypePredicate(){let r=this.tsParseThisTypeNode();return this.isContextual(116)&&!this.hasPrecedingLineBreak()?this.tsParseThisTypePredicate(r):r}tsParseNonArrayType(){switch(this.state.type){case 134:case 135:case 136:case 85:case 86:return this.tsParseLiteralTypeNode();case 53:if(this.state.value==="-"){let r=this.startNode(),n=this.lookahead();return n.type!==135&&n.type!==136&&this.unexpected(),r.literal=this.parseMaybeUnary(),this.finishNode(r,"TSLiteralType")}break;case 78:return this.tsParseThisTypeOrThisTypePredicate();case 87:return this.tsParseTypeQuery();case 83:return this.tsParseImportType();case 5:return this.tsLookAhead(this.tsIsStartOfMappedType.bind(this))?this.tsParseMappedType():this.tsParseTypeLiteral();case 0:return this.tsParseTupleType();case 10:return this.tsParseParenthesizedType();case 25:case 24:return this.tsParseTemplateLiteralType();default:{let{type:r}=this.state;if(ot(r)||r===88||r===84){let n=r===88?"TSVoidKeyword":r===84?"TSNullKeyword":UH(this.state.value);if(n!==void 0&&this.lookaheadCharCode()!==46){let s=this.startNode();return this.next(),this.finishNode(s,n)}return this.tsParseTypeReference()}}}this.unexpected()}tsParseArrayTypeOrHigher(){let{startLoc:r}=this.state,n=this.tsParseNonArrayType();for(;!this.hasPrecedingLineBreak()&&this.eat(0);)if(this.match(3)){let s=this.startNodeAt(r);s.elementType=n,this.expect(3),n=this.finishNode(s,"TSArrayType")}else{let s=this.startNodeAt(r);s.objectType=n,s.indexType=this.tsParseType(),this.expect(3),n=this.finishNode(s,"TSIndexedAccessType")}return n}tsParseTypeOperator(){let r=this.startNode(),n=this.state.value;return this.next(),r.operator=n,r.typeAnnotation=this.tsParseTypeOperatorOrHigher(),n==="readonly"&&this.tsCheckTypeAnnotationForReadOnly(r),this.finishNode(r,"TSTypeOperator")}tsCheckTypeAnnotationForReadOnly(r){switch(r.typeAnnotation.type){case"TSTupleType":case"TSArrayType":return;default:this.raise(me.UnexpectedReadonly,r)}}tsParseInferType(){let r=this.startNode();this.expectContextual(115);let n=this.startNode();return n.name=this.tsParseTypeParameterName(),n.constraint=this.tsTryParse(()=>this.tsParseConstraintForInferType()),r.typeParameter=this.finishNode(n,"TSTypeParameter"),this.finishNode(r,"TSInferType")}tsParseConstraintForInferType(){if(this.eat(81)){let r=this.tsInDisallowConditionalTypesContext(()=>this.tsParseType());if(this.state.inDisallowConditionalTypesContext||!this.match(17))return r}}tsParseTypeOperatorOrHigher(){return oH(this.state.type)&&!this.state.containsEsc?this.tsParseTypeOperator():this.isContextual(115)?this.tsParseInferType():this.tsInAllowConditionalTypesContext(()=>this.tsParseArrayTypeOrHigher())}tsParseUnionOrIntersectionType(r,n,s){let i=this.startNode(),a=this.eat(s),o=[];do o.push(n());while(this.eat(s));return o.length===1&&!a?o[0]:(i.types=o,this.finishNode(i,r))}tsParseIntersectionTypeOrHigher(){return this.tsParseUnionOrIntersectionType("TSIntersectionType",this.tsParseTypeOperatorOrHigher.bind(this),45)}tsParseUnionTypeOrHigher(){return this.tsParseUnionOrIntersectionType("TSUnionType",this.tsParseIntersectionTypeOrHigher.bind(this),43)}tsIsStartOfFunctionType(){return this.match(47)?!0:this.match(10)&&this.tsLookAhead(this.tsIsUnambiguouslyStartOfFunctionType.bind(this))}tsSkipParameterStart(){if(ot(this.state.type)||this.match(78))return this.next(),!0;if(this.match(5)){let{errors:r}=this.state,n=r.length;try{return this.parseObjectLike(8,!0),r.length===n}catch{return!1}}if(this.match(0)){this.next();let{errors:r}=this.state,n=r.length;try{return super.parseBindingList(3,93,1),r.length===n}catch{return!1}}return!1}tsIsUnambiguouslyStartOfFunctionType(){return this.next(),!!(this.match(11)||this.match(21)||this.tsSkipParameterStart()&&(this.match(14)||this.match(12)||this.match(17)||this.match(29)||this.match(11)&&(this.next(),this.match(19))))}tsParseTypeOrTypePredicateAnnotation(r){return this.tsInType(()=>{let n=this.startNode();this.expect(r);let s=this.startNode(),i=!!this.tsTryParse(this.tsParseTypePredicateAsserts.bind(this));if(i&&this.match(78)){let l=this.tsParseThisTypeOrThisTypePredicate();return l.type==="TSThisType"?(s.parameterName=l,s.asserts=!0,s.typeAnnotation=null,l=this.finishNode(s,"TSTypePredicate")):(this.resetStartLocationFromNode(l,s),l.asserts=!0),n.typeAnnotation=l,this.finishNode(n,"TSTypeAnnotation")}let a=this.tsIsIdentifier()&&this.tsTryParse(this.tsParseTypePredicatePrefix.bind(this));if(!a)return i?(s.parameterName=this.parseIdentifier(),s.asserts=i,s.typeAnnotation=null,n.typeAnnotation=this.finishNode(s,"TSTypePredicate"),this.finishNode(n,"TSTypeAnnotation")):this.tsParseTypeAnnotation(!1,n);let o=this.tsParseTypeAnnotation(!1);return s.parameterName=a,s.typeAnnotation=o,s.asserts=i,n.typeAnnotation=this.finishNode(s,"TSTypePredicate"),this.finishNode(n,"TSTypeAnnotation")})}tsTryParseTypeOrTypePredicateAnnotation(){if(this.match(14))return this.tsParseTypeOrTypePredicateAnnotation(14)}tsTryParseTypeAnnotation(){if(this.match(14))return this.tsParseTypeAnnotation()}tsTryParseType(){return this.tsEatThenParseType(14)}tsParseTypePredicatePrefix(){let r=this.parseIdentifier();if(this.isContextual(116)&&!this.hasPrecedingLineBreak())return this.next(),r}tsParseTypePredicateAsserts(){if(this.state.type!==109)return!1;let r=this.state.containsEsc;return this.next(),!ot(this.state.type)&&!this.match(78)?!1:(r&&this.raise(B.InvalidEscapedReservedWord,this.state.lastTokStartLoc,{reservedWord:"asserts"}),!0)}tsParseTypeAnnotation(r=!0,n=this.startNode()){return this.tsInType(()=>{r&&this.expect(14),n.typeAnnotation=this.tsParseType()}),this.finishNode(n,"TSTypeAnnotation")}tsParseType(){WS(this.state.inType);let r=this.tsParseNonConditionalType();if(this.state.inDisallowConditionalTypesContext||this.hasPrecedingLineBreak()||!this.eat(81))return r;let n=this.startNodeAtNode(r);return n.checkType=r,n.extendsType=this.tsInDisallowConditionalTypesContext(()=>this.tsParseNonConditionalType()),this.expect(17),n.trueType=this.tsInAllowConditionalTypesContext(()=>this.tsParseType()),this.expect(14),n.falseType=this.tsInAllowConditionalTypesContext(()=>this.tsParseType()),this.finishNode(n,"TSConditionalType")}isAbstractConstructorSignature(){return this.isContextual(124)&&this.lookahead().type===77}tsParseNonConditionalType(){return this.tsIsStartOfFunctionType()?this.tsParseFunctionOrConstructorType("TSFunctionType"):this.match(77)?this.tsParseFunctionOrConstructorType("TSConstructorType"):this.isAbstractConstructorSignature()?this.tsParseFunctionOrConstructorType("TSConstructorType",!0):this.tsParseUnionTypeOrHigher()}tsParseTypeAssertion(){this.getPluginOption("typescript","disallowAmbiguousJSXLike")&&this.raise(me.ReservedTypeAssertion,this.state.startLoc);let r=this.startNode();return r.typeAnnotation=this.tsInType(()=>(this.next(),this.match(75)?this.tsParseTypeReference():this.tsParseType())),this.expect(48),r.expression=this.parseMaybeUnary(),this.finishNode(r,"TSTypeAssertion")}tsParseHeritageClause(r){let n=this.state.startLoc,s=this.tsParseDelimitedList("HeritageClauseElement",()=>{{let i=this.startNode();return i.expression=this.tsParseEntityName(3),this.match(47)&&(i.typeParameters=this.tsParseTypeArguments()),this.finishNode(i,"TSExpressionWithTypeArguments")}});return s.length||this.raise(me.EmptyHeritageClauseType,n,{token:r}),s}tsParseInterfaceDeclaration(r,n={}){if(this.hasFollowingLineBreak())return null;this.expectContextual(129),n.declare&&(r.declare=!0),ot(this.state.type)?(r.id=this.parseIdentifier(),this.checkIdentifier(r.id,130)):(r.id=null,this.raise(me.MissingInterfaceName,this.state.startLoc)),r.typeParameters=this.tsTryParseTypeParameters(this.tsParseInOutConstModifiers),this.eat(81)&&(r.extends=this.tsParseHeritageClause("extends"));let s=this.startNode();return s.body=this.tsInType(this.tsParseObjectTypeMembers.bind(this)),r.body=this.finishNode(s,"TSInterfaceBody"),this.finishNode(r,"TSInterfaceDeclaration")}tsParseTypeAliasDeclaration(r){return r.id=this.parseIdentifier(),this.checkIdentifier(r.id,2),r.typeAnnotation=this.tsInType(()=>{if(r.typeParameters=this.tsTryParseTypeParameters(this.tsParseInOutModifiers),this.expect(29),this.isContextual(114)&&this.lookahead().type!==16){let n=this.startNode();return this.next(),this.finishNode(n,"TSIntrinsicKeyword")}return this.tsParseType()}),this.semicolon(),this.finishNode(r,"TSTypeAliasDeclaration")}tsInTopLevelContext(r){if(this.curContext()!==at.brace){let n=this.state.context;this.state.context=[n[0]];try{return r()}finally{this.state.context=n}}else return r()}tsInType(r){let n=this.state.inType;this.state.inType=!0;try{return r()}finally{this.state.inType=n}}tsInDisallowConditionalTypesContext(r){let n=this.state.inDisallowConditionalTypesContext;this.state.inDisallowConditionalTypesContext=!0;try{return r()}finally{this.state.inDisallowConditionalTypesContext=n}}tsInAllowConditionalTypesContext(r){let n=this.state.inDisallowConditionalTypesContext;this.state.inDisallowConditionalTypesContext=!1;try{return r()}finally{this.state.inDisallowConditionalTypesContext=n}}tsEatThenParseType(r){if(this.match(r))return this.tsNextThenParseType()}tsExpectThenParseType(r){return this.tsInType(()=>(this.expect(r),this.tsParseType()))}tsNextThenParseType(){return this.tsInType(()=>(this.next(),this.tsParseType()))}tsParseEnumMember(){let r=this.startNode();return r.id=this.match(134)?super.parseStringLiteral(this.state.value):this.parseIdentifier(!0),this.eat(29)&&(r.initializer=super.parseMaybeAssignAllowIn()),this.finishNode(r,"TSEnumMember")}tsParseEnumDeclaration(r,n={}){return n.const&&(r.const=!0),n.declare&&(r.declare=!0),this.expectContextual(126),r.id=this.parseIdentifier(),this.checkIdentifier(r.id,r.const?8971:8459),this.expect(5),r.members=this.tsParseDelimitedList("EnumMembers",this.tsParseEnumMember.bind(this)),this.expect(8),this.finishNode(r,"TSEnumDeclaration")}tsParseEnumBody(){let r=this.startNode();return this.expect(5),r.members=this.tsParseDelimitedList("EnumMembers",this.tsParseEnumMember.bind(this)),this.expect(8),this.finishNode(r,"TSEnumBody")}tsParseModuleBlock(){let r=this.startNode();return this.scope.enter(0),this.expect(5),super.parseBlockOrModuleBlockBody(r.body=[],void 0,!0,8),this.scope.exit(),this.finishNode(r,"TSModuleBlock")}tsParseModuleOrNamespaceDeclaration(r,n=!1){if(r.id=this.parseIdentifier(),n||this.checkIdentifier(r.id,1024),this.eat(16)){let s=this.startNode();this.tsParseModuleOrNamespaceDeclaration(s,!0),r.body=s}else this.scope.enter(256),this.prodParam.enter(0),r.body=this.tsParseModuleBlock(),this.prodParam.exit(),this.scope.exit();return this.finishNode(r,"TSModuleDeclaration")}tsParseAmbientExternalModuleDeclaration(r){return this.isContextual(112)?(r.kind="global",r.global=!0,r.id=this.parseIdentifier()):this.match(134)?(r.kind="module",r.id=super.parseStringLiteral(this.state.value)):this.unexpected(),this.match(5)?(this.scope.enter(256),this.prodParam.enter(0),r.body=this.tsParseModuleBlock(),this.prodParam.exit(),this.scope.exit()):this.semicolon(),this.finishNode(r,"TSModuleDeclaration")}tsParseImportEqualsDeclaration(r,n,s){r.isExport=s||!1,r.id=n||this.parseIdentifier(),this.checkIdentifier(r.id,4096),this.expect(29);let i=this.tsParseModuleReference();return r.importKind==="type"&&i.type!=="TSExternalModuleReference"&&this.raise(me.ImportAliasHasImportType,i),r.moduleReference=i,this.semicolon(),this.finishNode(r,"TSImportEqualsDeclaration")}tsIsExternalModuleReference(){return this.isContextual(119)&&this.lookaheadCharCode()===40}tsParseModuleReference(){return this.tsIsExternalModuleReference()?this.tsParseExternalModuleReference():this.tsParseEntityName(0)}tsParseExternalModuleReference(){let r=this.startNode();return this.expectContextual(119),this.expect(10),this.match(134)||this.unexpected(),r.expression=super.parseExprAtom(),this.expect(11),this.sawUnambiguousESM=!0,this.finishNode(r,"TSExternalModuleReference")}tsLookAhead(r){let n=this.state.clone(),s=r();return this.state=n,s}tsTryParseAndCatch(r){let n=this.tryParse(s=>r()||s());if(!(n.aborted||!n.node))return n.error&&(this.state=n.failState),n.node}tsTryParse(r){let n=this.state.clone(),s=r();if(s!==void 0&&s!==!1)return s;this.state=n}tsTryParseDeclare(r){if(this.isLineTerminator())return;let n=this.state.type,s;return this.isContextual(100)&&(n=74,s="let"),this.tsInAmbientContext(()=>{switch(n){case 68:return r.declare=!0,super.parseFunctionStatement(r,!1,!1);case 80:return r.declare=!0,this.parseClass(r,!0,!1);case 126:return this.tsParseEnumDeclaration(r,{declare:!0});case 112:return this.tsParseAmbientExternalModuleDeclaration(r);case 75:case 74:return!this.match(75)||!this.isLookaheadContextual("enum")?(r.declare=!0,this.parseVarStatement(r,s||this.state.value,!0)):(this.expect(75),this.tsParseEnumDeclaration(r,{const:!0,declare:!0}));case 129:{let i=this.tsParseInterfaceDeclaration(r,{declare:!0});if(i)return i}default:if(ot(n))return this.tsParseDeclaration(r,this.state.value,!0,null)}})}tsTryParseExportDeclaration(){return this.tsParseDeclaration(this.startNode(),this.state.value,!0,null)}tsParseExpressionStatement(r,n,s){switch(n.name){case"declare":{let i=this.tsTryParseDeclare(r);return i&&(i.declare=!0),i}case"global":if(this.match(5)){this.scope.enter(256),this.prodParam.enter(0);let i=r;return i.kind="global",r.global=!0,i.id=n,i.body=this.tsParseModuleBlock(),this.scope.exit(),this.prodParam.exit(),this.finishNode(i,"TSModuleDeclaration")}break;default:return this.tsParseDeclaration(r,n.name,!1,s)}}tsParseDeclaration(r,n,s,i){switch(n){case"abstract":if(this.tsCheckLineTerminator(s)&&(this.match(80)||ot(this.state.type)))return this.tsParseAbstractDeclaration(r,i);break;case"module":if(this.tsCheckLineTerminator(s)){if(this.match(134))return this.tsParseAmbientExternalModuleDeclaration(r);if(ot(this.state.type))return r.kind="module",this.tsParseModuleOrNamespaceDeclaration(r)}break;case"namespace":if(this.tsCheckLineTerminator(s)&&ot(this.state.type))return r.kind="namespace",this.tsParseModuleOrNamespaceDeclaration(r);break;case"type":if(this.tsCheckLineTerminator(s)&&ot(this.state.type))return this.tsParseTypeAliasDeclaration(r);break}}tsCheckLineTerminator(r){return r?this.hasFollowingLineBreak()?!1:(this.next(),!0):!this.isLineTerminator()}tsTryParseGenericAsyncArrowFunction(r){if(!this.match(47))return;let n=this.state.maybeInArrowParameters;this.state.maybeInArrowParameters=!0;let s=this.tsTryParseAndCatch(()=>{let i=this.startNodeAt(r);return i.typeParameters=this.tsParseTypeParameters(this.tsParseConstModifier),super.parseFunctionParams(i),i.returnType=this.tsTryParseTypeOrTypePredicateAnnotation(),this.expect(19),i});if(this.state.maybeInArrowParameters=n,!!s)return super.parseArrowExpression(s,null,!0)}tsParseTypeArgumentsInExpression(){if(this.reScan_lt()===47)return this.tsParseTypeArguments()}tsParseTypeArguments(){let r=this.startNode();return r.params=this.tsInType(()=>this.tsInTopLevelContext(()=>(this.expect(47),this.tsParseDelimitedList("TypeParametersOrArguments",this.tsParseType.bind(this))))),r.params.length===0?this.raise(me.EmptyTypeArguments,r):!this.state.inType&&this.curContext()===at.brace&&this.reScan_lt_gt(),this.expect(48),this.finishNode(r,"TSTypeParameterInstantiation")}tsIsDeclarationStart(){return lH(this.state.type)}isExportDefaultSpecifier(){return this.tsIsDeclarationStart()?!1:super.isExportDefaultSpecifier()}parseBindingElement(r,n){let s=this.state.startLoc,i={};this.tsParseModifiers({allowedModifiers:["public","private","protected","override","readonly"]},i);let a=i.accessibility,o=i.override,l=i.readonly;!(r&4)&&(a||l||o)&&this.raise(me.UnexpectedParameterModifier,s);let u=this.parseMaybeDefault();r&2&&this.parseFunctionParamType(u);let c=this.parseMaybeDefault(u.loc.start,u);if(a||l||o){let p=this.startNodeAt(s);return n.length&&(p.decorators=n),a&&(p.accessibility=a),l&&(p.readonly=l),o&&(p.override=o),c.type!=="Identifier"&&c.type!=="AssignmentPattern"&&this.raise(me.UnsupportedParameterPropertyKind,p),p.parameter=c,this.finishNode(p,"TSParameterProperty")}return n.length&&(u.decorators=n),c}isSimpleParameter(r){return r.type==="TSParameterProperty"&&super.isSimpleParameter(r.parameter)||super.isSimpleParameter(r)}tsDisallowOptionalPattern(r){for(let n of r.params)n.type!=="Identifier"&&n.optional&&!this.state.isAmbientContext&&this.raise(me.PatternIsOptional,n)}setArrowFunctionParameters(r,n,s){super.setArrowFunctionParameters(r,n,s),this.tsDisallowOptionalPattern(r)}parseFunctionBodyAndFinish(r,n,s=!1){this.match(14)&&(r.returnType=this.tsParseTypeOrTypePredicateAnnotation(14));let i=n==="FunctionDeclaration"?"TSDeclareFunction":n==="ClassMethod"||n==="ClassPrivateMethod"?"TSDeclareMethod":void 0;return i&&!this.match(5)&&this.isLineTerminator()?this.finishNode(r,i):i==="TSDeclareFunction"&&this.state.isAmbientContext&&(this.raise(me.DeclareFunctionHasImplementation,r),r.declare)?super.parseFunctionBodyAndFinish(r,i,s):(this.tsDisallowOptionalPattern(r),super.parseFunctionBodyAndFinish(r,n,s))}registerFunctionStatementId(r){!r.body&&r.id?this.checkIdentifier(r.id,1024):super.registerFunctionStatementId(r)}tsCheckForInvalidTypeCasts(r){r.forEach(n=>{(n==null?void 0:n.type)==="TSTypeCastExpression"&&this.raise(me.UnexpectedTypeAnnotation,n.typeAnnotation)})}toReferencedList(r,n){return this.tsCheckForInvalidTypeCasts(r),r}parseArrayLike(r,n,s,i){let a=super.parseArrayLike(r,n,s,i);return a.type==="ArrayExpression"&&this.tsCheckForInvalidTypeCasts(a.elements),a}parseSubscript(r,n,s,i){if(!this.hasPrecedingLineBreak()&&this.match(35)){this.state.canStartJSXElement=!1,this.next();let o=this.startNodeAt(n);return o.expression=r,this.finishNode(o,"TSNonNullExpression")}let a=!1;if(this.match(18)&&this.lookaheadCharCode()===60){if(s)return i.stop=!0,r;i.optionalChainMember=a=!0,this.next()}if(this.match(47)||this.match(51)){let o,l=this.tsTryParseAndCatch(()=>{if(!s&&this.atPossibleAsyncArrow(r)){let d=this.tsTryParseGenericAsyncArrowFunction(n);if(d)return d}let u=this.tsParseTypeArgumentsInExpression();if(!u)return;if(a&&!this.match(10)){o=this.state.curPosition();return}if(fu(this.state.type)){let d=super.parseTaggedTemplateExpression(r,n,i);return d.typeParameters=u,d}if(!s&&this.eat(10)){let d=this.startNodeAt(n);return d.callee=r,d.arguments=this.parseCallExpressionArguments(11),this.tsCheckForInvalidTypeCasts