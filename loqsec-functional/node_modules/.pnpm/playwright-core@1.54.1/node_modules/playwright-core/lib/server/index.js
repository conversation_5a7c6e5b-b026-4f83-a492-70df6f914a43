"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var server_exports = {};
__export(server_exports, {
  DispatcherConnection: () => import_dispatcher.DispatcherConnection,
  PlaywrightDispatcher: () => import_playwrightDispatcher.PlaywrightDispatcher,
  Registry: () => import_registry.Registry,
  RootDispatcher: () => import_dispatcher.RootDispatcher,
  createPlaywright: () => import_playwright.createPlaywright,
  installBrowsersForNpmInstall: () => import_registry.installBrowsersForNpmInstall,
  installRootRedirect: () => import_traceViewer.installRootRedirect,
  openTraceInBrowser: () => import_traceViewer.openTraceInBrowser,
  openTraceViewerApp: () => import_traceViewer.openTraceViewerApp,
  registry: () => import_registry.registry,
  registryDirectory: () => import_registry.registryDirectory,
  runTraceViewerApp: () => import_traceViewer.runTraceViewerApp,
  serverSideCallMetadata: () => import_instrumentation.serverSideCallMetadata,
  startTraceViewerServer: () => import_traceViewer.startTraceViewerServer,
  writeDockerVersion: () => import_registry.writeDockerVersion
});
module.exports = __toCommonJS(server_exports);
var import_registry = require("./registry");
var import_dispatcher = require("./dispatchers/dispatcher");
var import_playwrightDispatcher = require("./dispatchers/playwrightDispatcher");
var import_playwright = require("./playwright");
var import_traceViewer = require("./trace/viewer/traceViewer");
var import_instrumentation = require("./instrumentation");
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  DispatcherConnection,
  PlaywrightDispatcher,
  Registry,
  RootDispatcher,
  createPlaywright,
  installBrowsersForNpmInstall,
  installRootRedirect,
  openTraceInBrowser,
  openTraceViewerApp,
  registry,
  registryDirectory,
  runTraceViewerApp,
  serverSideCallMetadata,
  startTraceViewerServer,
  writeDockerVersion
});
