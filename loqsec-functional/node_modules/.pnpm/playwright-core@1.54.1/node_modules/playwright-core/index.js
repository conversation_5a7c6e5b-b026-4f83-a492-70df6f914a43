/**
 * Copyright (c) Microsoft Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
const minimumMajorNodeVersion = 18;
const currentNodeVersion = process.versions.node;
const semver = currentNodeVersion.split('.');
const [major] = [+semver[0]];

if (major < minimumMajorNodeVersion) {
  console.error(
      'You are running Node.js ' +
      currentNodeVersion +
      '.\n' +
      `<PERSON>wright requires Node.js ${minimumMajorNodeVersion} or higher. \n` +
      'Please update your version of Node.js.'
  );
  process.exit(1);
}

module.exports = require('./lib/inprocess');
