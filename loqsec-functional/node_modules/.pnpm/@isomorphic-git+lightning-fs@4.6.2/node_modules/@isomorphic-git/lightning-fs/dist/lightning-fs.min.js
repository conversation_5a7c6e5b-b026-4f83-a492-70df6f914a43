!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):"object"==typeof exports?exports.LightningFS=e():t.LightningFS=e()}(self,(()=>(()=>{var t={806:function(t,e,i){!function(t){function e(t){if("utf-8"!==(t=void 0===t?"utf-8":t))throw new RangeError("Failed to construct 'TextEncoder': The encoding label provided ('"+t+"') is invalid.")}function i(t,e){if(e=void 0===e?{fatal:!1}:e,"utf-8"!==(t=void 0===t?"utf-8":t))throw new RangeError("Failed to construct 'TextDecoder': The encoding label provided ('"+t+"') is invalid.");if(e.fatal)throw Error("Failed to construct 'TextDecoder': the 'fatal' option is unsupported.")}if(t.TextEncoder&&t.TextDecoder)return!1;Object.defineProperty(e.prototype,"encoding",{value:"utf-8"}),e.prototype.encode=function(t,e){if((e=void 0===e?{stream:!1}:e).stream)throw Error("Failed to encode: the 'stream' option is unsupported.");e=0;for(var i=t.length,s=0,r=Math.max(32,i+(i>>1)+7),n=new Uint8Array(r>>3<<3);e<i;){var o=t.charCodeAt(e++);if(55296<=o&&56319>=o){if(e<i){var a=t.charCodeAt(e);56320==(64512&a)&&(++e,o=((1023&o)<<10)+(1023&a)+65536)}if(55296<=o&&56319>=o)continue}if(s+4>n.length&&(r+=8,r=(r*=1+e/t.length*2)>>3<<3,(a=new Uint8Array(r)).set(n),n=a),0==(4294967168&o))n[s++]=o;else{if(0==(4294965248&o))n[s++]=o>>6&31|192;else if(0==(4294901760&o))n[s++]=o>>12&15|224,n[s++]=o>>6&63|128;else{if(0!=(4292870144&o))continue;n[s++]=o>>18&7|240,n[s++]=o>>12&63|128,n[s++]=o>>6&63|128}n[s++]=63&o|128}}return n.slice(0,s)},Object.defineProperty(i.prototype,"encoding",{value:"utf-8"}),Object.defineProperty(i.prototype,"fatal",{value:!1}),Object.defineProperty(i.prototype,"ignoreBOM",{value:!1}),i.prototype.decode=function(t,e){if((e=void 0===e?{stream:!1}:e).stream)throw Error("Failed to decode: the 'stream' option is unsupported.");e=0;for(var i=(t=new Uint8Array(t)).length,s=[];e<i;){var r=t[e++];if(0===r)break;if(0==(128&r))s.push(r);else if(192==(224&r)){var n=63&t[e++];s.push((31&r)<<6|n)}else if(224==(240&r)){n=63&t[e++];var o=63&t[e++];s.push((31&r)<<12|n<<6|o)}else 240==(248&r)&&(65535<(r=(7&r)<<18|(n=63&t[e++])<<12|(o=63&t[e++])<<6|63&t[e++])&&(r-=65536,s.push(r>>>10&1023|55296),r=56320|1023&r),s.push(r))}return String.fromCharCode.apply(null,s)},t.TextEncoder=e,t.TextDecoder=i}("undefined"!=typeof window?window:void 0!==i.g?i.g:this)},36:(t,e,i)=>{i(806),t.exports={encode:t=>(new TextEncoder).encode(t),decode:t=>(new TextDecoder).decode(t)}},708:t=>{t.exports=function(t,e,i){var s;return function(){if(!e)return t.apply(this,arguments);var r=this,n=arguments,o=i&&!s;return clearTimeout(s),s=setTimeout((function(){if(s=null,!o)return t.apply(r,n)}),e),o?t.apply(this,arguments):void 0}}},97:t=>{t.exports=function(t){var e,i;if("function"!=typeof t)throw new Error("expected a function but got "+t);return function(){return e?i:(e=!0,i=t.apply(this,arguments))}}},977:(t,e,i)=>{const s=i(61),{EEXIST:r,ENOENT:n,ENOTDIR:o,ENOTEMPTY:a,EISDIR:h}=i(658);t.exports=class{constructor(){}_makeRoot(t=new Map){return t.set(0,{mode:511,type:"dir",size:0,ino:0,mtimeMs:Date.now()}),t}activate(t=null){this._root=null===t?new Map([["/",this._makeRoot()]]):"string"==typeof t?new Map([["/",this._makeRoot(this.parse(t))]]):t}get activated(){return!!this._root}deactivate(){this._root=void 0}size(){return this._countInodes(this._root.get("/"))-1}_countInodes(t){let e=1;for(let[i,s]of t)0!==i&&(e+=this._countInodes(s));return e}autoinc(){return this._maxInode(this._root.get("/"))+1}_maxInode(t){let e=t.get(0).ino;for(let[i,s]of t)0!==i&&(e=Math.max(e,this._maxInode(s)));return e}print(t=this._root.get("/")){let e="";const i=(t,s)=>{for(let[r,n]of t){if(0===r)continue;let t=n.get(0),o=t.mode.toString(8);e+=`${"\t".repeat(s)}${r}\t${o}`,"file"===t.type?e+=`\t${t.size}\t${t.mtimeMs}\n`:(e+="\n",i(n,s+1))}};return i(t,0),e}parse(t){let e=0;function i(t){const i=++e,s=1===t.length?"dir":"file";let[r,n,o]=t;return r=parseInt(r,8),n=n?parseInt(n):0,o=o?parseInt(o):Date.now(),new Map([[0,{mode:r,type:s,size:n,mtimeMs:o,ino:i}]])}let s=t.trim().split("\n"),r=this._makeRoot(),n=[{indent:-1,node:r},{indent:0,node:null}];for(let t of s){let e=t.match(/^\t*/)[0].length;t=t.slice(e);let[s,...r]=t.split("\t"),o=i(r);if(e<=n[n.length-1].indent)for(;e<=n[n.length-1].indent;)n.pop();n.push({indent:e,node:o}),n[n.length-2].node.set(s,o)}return r}_lookup(t,e=!0){let i=this._root,r="/",o=s.split(t);for(let a=0;a<o.length;++a){let h=o[a];if(i=i.get(h),!i)throw new n(t);if(e||a<o.length-1){const t=i.get(0);if("symlink"===t.type){let e=s.resolve(r,t.target);i=this._lookup(e)}r=r?s.join(r,h):h}}return i}mkdir(t,{mode:e}){if("/"===t)throw new r;let i=this._lookup(s.dirname(t)),n=s.basename(t);if(i.has(n))throw new r;let o=new Map,a={mode:e,type:"dir",size:0,mtimeMs:Date.now(),ino:this.autoinc()};o.set(0,a),i.set(n,o)}rmdir(t){let e=this._lookup(t);if("dir"!==e.get(0).type)throw new o;if(e.size>1)throw new a;let i=this._lookup(s.dirname(t)),r=s.basename(t);i.delete(r)}readdir(t){let e=this._lookup(t);if("dir"!==e.get(0).type)throw new o;return[...e.keys()].filter((t=>"string"==typeof t))}writeStat(t,e,{mode:i}){let r,n;try{n=this.stat(t)}catch(t){}if(void 0!==n){if("dir"===n.type)throw new h;null==i&&(i=n.mode),r=n.ino}null==i&&(i=438),null==r&&(r=this.autoinc());let o=this._lookup(s.dirname(t)),a=s.basename(t),c={mode:i,type:"file",size:e,mtimeMs:Date.now(),ino:r},l=new Map;return l.set(0,c),o.set(a,l),c}unlink(t){let e=this._lookup(s.dirname(t)),i=s.basename(t);e.delete(i)}rename(t,e){let i=s.basename(e),r=this._lookup(t);this._lookup(s.dirname(e)).set(i,r),this.unlink(t)}stat(t){return this._lookup(t).get(0)}lstat(t){return this._lookup(t,!1).get(0)}readlink(t){return this._lookup(t,!1).get(0).target}symlink(t,e){let i,r;try{let t=this.stat(e);null===r&&(r=t.mode),i=t.ino}catch(t){}null==r&&(r=40960),null==i&&(i=this.autoinc());let n=this._lookup(s.dirname(e)),o=s.basename(e),a={mode:r,type:"symlink",target:t,size:0,mtimeMs:Date.now(),ino:i},h=new Map;return h.set(0,a),n.set(o,h),a}_du(t){let e=0;for(const[i,s]of t.entries())e+=0===i?s.size:this._du(s);return e}du(t){let e=this._lookup(t);return this._du(e)}}},778:(t,e,i)=>{const{encode:s,decode:r}=i(36),n=i(708),o=i(977),{ENOENT:a,ENOTEMPTY:h,ETIMEDOUT:c}=i(658),l=i(117),u=i(798),d=i(587),_=i(750),p=i(61);t.exports=class{constructor(){this.saveSuperblock=n((()=>{this.flush()}),500)}async init(t,{wipe:e,url:i,urlauto:s,fileDbName:r=t,db:n=null,fileStoreName:a=t+"_files",lockDbName:h=t+"_lock",lockStoreName:c=t+"_lock"}={}){this._name=t,this._idb=n||new l(r,a),this._mutex=navigator.locks?new _(t):new d(h,c),this._cache=new o(t),this._opts={wipe:e,url:i},this._needsWipe=!!e,i&&(this._http=new u(i),this._urlauto=!!s)}async activate(){if(this._cache.activated)return;this._needsWipe&&(this._needsWipe=!1,await this._idb.wipe(),await this._mutex.release({force:!0})),await this._mutex.has()||await this._mutex.wait();const t=await this._idb.loadSuperblock();if(t)this._cache.activate(t);else if(this._http){const t=await this._http.loadSuperblock();this._cache.activate(t),await this._saveSuperblock()}else this._cache.activate();if(!await this._mutex.has())throw new c}async deactivate(){await this._mutex.has()&&await this._saveSuperblock(),this._cache.deactivate();try{await this._mutex.release()}catch(t){console.log(t)}await this._idb.close()}async _saveSuperblock(){this._cache.activated&&(this._lastSavedAt=Date.now(),await this._idb.saveSuperblock(this._cache._root))}_writeStat(t,e,i){let s=p.split(p.dirname(t)),r=s.shift();for(let t of s){r=p.join(r,t);try{this._cache.mkdir(r,{mode:511})}catch(t){}}return this._cache.writeStat(t,e,i)}async readFile(t,e){const i="string"==typeof e?e:e&&e.encoding;if(i&&"utf8"!==i)throw new Error('Only "utf8" encoding is supported in readFile');let s=null,n=null;try{n=this._cache.stat(t),s=await this._idb.readFile(n.ino)}catch(t){if(!this._urlauto)throw t}if(!s&&this._http){let e=this._cache.lstat(t);for(;"symlink"===e.type;)t=p.resolve(p.dirname(t),e.target),e=this._cache.lstat(t);s=await this._http.readFile(t)}if(s&&(n&&n.size==s.byteLength||(n=await this._writeStat(t,s.byteLength,{mode:n?n.mode:438}),this.saveSuperblock()),"utf8"===i?s=r(s):s.toString=()=>r(s)),!n)throw new a(t);return s}async writeFile(t,e,i){const{mode:r,encoding:n="utf8"}=i;if("string"==typeof e){if("utf8"!==n)throw new Error('Only "utf8" encoding is supported in writeFile');e=s(e)}const o=await this._cache.writeStat(t,e.byteLength,{mode:r});await this._idb.writeFile(o.ino,e)}async unlink(t,e){const i=this._cache.lstat(t);this._cache.unlink(t),"symlink"!==i.type&&await this._idb.unlink(i.ino)}readdir(t,e){return this._cache.readdir(t)}mkdir(t,e){const{mode:i=511}=e;this._cache.mkdir(t,{mode:i})}rmdir(t,e){if("/"===t)throw new h;this._cache.rmdir(t)}rename(t,e){this._cache.rename(t,e)}stat(t,e){return this._cache.stat(t)}lstat(t,e){return this._cache.lstat(t)}readlink(t,e){return this._cache.readlink(t)}symlink(t,e){this._cache.symlink(t,e)}async backFile(t,e){let i=await this._http.sizeFile(t);await this._writeStat(t,i,e)}du(t){return this._cache.du(t)}flush(){return this._saveSuperblock()}}},798:t=>{t.exports=class{constructor(t){this._url=t}loadSuperblock(){return fetch(this._url+"/.superblock.txt").then((t=>t.ok?t.text():null))}async readFile(t){const e=await fetch(this._url+t);if(200===e.status)return e.arrayBuffer();throw new Error("ENOENT")}async sizeFile(t){const e=await fetch(this._url+t,{method:"HEAD"});if(200===e.status)return e.headers.get("content-length");throw new Error("ENOENT")}}},117:(t,e,i)=>{const s=i(341);t.exports=class{constructor(t,e){this._database=t,this._storename=e,this._store=new s.Store(this._database,this._storename)}saveSuperblock(t){return s.set("!root",t,this._store)}loadSuperblock(){return s.get("!root",this._store)}readFile(t){return s.get(t,this._store)}writeFile(t,e){return s.set(t,e,this._store)}unlink(t){return s.del(t,this._store)}wipe(){return s.clear(this._store)}close(){return s.close(this._store)}}},587:(t,e,i)=>{const s=i(341),r=t=>new Promise((e=>setTimeout(e,t)));t.exports=class{constructor(t,e){this._id=Math.random(),this._database=t,this._storename=e,this._store=new s.Store(this._database,this._storename),this._lock=null}async has({margin:t=2e3}={}){if(this._lock&&this._lock.holder===this._id){const e=Date.now();return this._lock.expires>e+t||await this.renew()}return!1}async renew({ttl:t=5e3}={}){let e;return await s.update("lock",(i=>{const s=Date.now()+t;return e=i&&i.holder===this._id,this._lock=e?{holder:this._id,expires:s}:i,this._lock}),this._store),e}async acquire({ttl:t=5e3}={}){let e,i,r;if(await s.update("lock",(s=>{const n=Date.now(),o=n+t;return i=s&&s.expires<n,e=void 0===s||i,r=s&&s.holder===this._id,this._lock=e?{holder:this._id,expires:o}:s,this._lock}),this._store),r)throw new Error("Mutex double-locked");return e}async wait({interval:t=100,limit:e=6e3,ttl:i}={}){for(;e--;){if(await this.acquire({ttl:i}))return!0;await r(t)}throw new Error("Mutex timeout")}async release({force:t=!1}={}){let e,i,r;if(await s.update("lock",(s=>(e=t||s&&s.holder===this._id,i=void 0===s,r=s&&s.holder!==this._id,this._lock=e?void 0:s,this._lock)),this._store),await s.close(this._store),!e&&!t){if(i)throw new Error("Mutex double-freed");if(r)throw new Error("Mutex lost ownership")}return e}}},750:t=>{t.exports=class{constructor(t){this._id=Math.random(),this._database=t,this._has=!1,this._release=null}async has(){return this._has}async acquire(){return new Promise((t=>{navigator.locks.request(this._database+"_lock",{ifAvailable:!0},(e=>(this._has=!!e,t(!!e),new Promise((t=>{this._release=t})))))}))}async wait({timeout:t=6e5}={}){return new Promise(((e,i)=>{const s=new AbortController;setTimeout((()=>{s.abort(),i(new Error("Mutex timeout"))}),t),navigator.locks.request(this._database+"_lock",{signal:s.signal},(t=>(this._has=!!t,e(!!t),new Promise((t=>{this._release=t})))))}))}async release({force:t=!1}={}){this._has=!1,this._release?this._release():t&&navigator.locks.request(this._database+"_lock",{steal:!0},(t=>!0))}}},382:(t,e,i)=>{const s=i(778),r=i(841),n=i(61);function o(t,e,...i){return void 0!==e&&"function"!=typeof e||(e={}),"string"==typeof e&&(e={encoding:e}),[t=n.normalize(t),e,...i]}function a(t,e,i,...s){return void 0!==i&&"function"!=typeof i||(i={}),"string"==typeof i&&(i={encoding:i}),[t=n.normalize(t),e,i,...s]}function h(t,e,...i){return[n.normalize(t),n.normalize(e),...i]}t.exports=class{constructor(t,e={}){this.init=this.init.bind(this),this.readFile=this._wrap(this.readFile,o,!1),this.writeFile=this._wrap(this.writeFile,a,!0),this.unlink=this._wrap(this.unlink,o,!0),this.readdir=this._wrap(this.readdir,o,!1),this.mkdir=this._wrap(this.mkdir,o,!0),this.rmdir=this._wrap(this.rmdir,o,!0),this.rename=this._wrap(this.rename,h,!0),this.stat=this._wrap(this.stat,o,!1),this.lstat=this._wrap(this.lstat,o,!1),this.readlink=this._wrap(this.readlink,o,!1),this.symlink=this._wrap(this.symlink,h,!0),this.backFile=this._wrap(this.backFile,o,!0),this.du=this._wrap(this.du,o,!1),this._deactivationPromise=null,this._deactivationTimeout=null,this._activationPromise=null,this._operations=new Set,t&&this.init(t,e)}async init(...t){return this._initPromiseResolve&&await this._initPromise,this._initPromise=this._init(...t),this._initPromise}async _init(t,e={}){await this._gracefulShutdown(),this._activationPromise&&await this._deactivate(),this._backend&&this._backend.destroy&&await this._backend.destroy(),this._backend=e.backend||new s,this._backend.init&&await this._backend.init(t,e),this._initPromiseResolve&&(this._initPromiseResolve(),this._initPromiseResolve=null),e.defer||this.stat("/")}async _gracefulShutdown(){this._operations.size>0&&(this._isShuttingDown=!0,await new Promise((t=>this._gracefulShutdownResolve=t)),this._isShuttingDown=!1,this._gracefulShutdownResolve=null)}_wrap(t,e,i){return async(...s)=>{s=e(...s);let r={name:t.name,args:s};this._operations.add(r);try{return await this._activate(),await t.apply(this,s)}finally{this._operations.delete(r),i&&this._backend.saveSuperblock(),0===this._operations.size&&(this._deactivationTimeout||clearTimeout(this._deactivationTimeout),this._deactivationTimeout=setTimeout(this._deactivate.bind(this),500))}}}async _activate(){this._initPromise||console.warn(new Error(`Attempted to use LightningFS ${this._name} before it was initialized.`)),await this._initPromise,this._deactivationTimeout&&(clearTimeout(this._deactivationTimeout),this._deactivationTimeout=null),this._deactivationPromise&&await this._deactivationPromise,this._deactivationPromise=null,this._activationPromise||(this._activationPromise=this._backend.activate?this._backend.activate():Promise.resolve()),await this._activationPromise}async _deactivate(){return this._activationPromise&&await this._activationPromise,this._deactivationPromise||(this._deactivationPromise=this._backend.deactivate?this._backend.deactivate():Promise.resolve()),this._activationPromise=null,this._gracefulShutdownResolve&&this._gracefulShutdownResolve(),this._deactivationPromise}async readFile(t,e){return this._backend.readFile(t,e)}async writeFile(t,e,i){return await this._backend.writeFile(t,e,i),null}async unlink(t,e){return await this._backend.unlink(t,e),null}async readdir(t,e){return this._backend.readdir(t,e)}async mkdir(t,e){return await this._backend.mkdir(t,e),null}async rmdir(t,e){return await this._backend.rmdir(t,e),null}async rename(t,e){return await this._backend.rename(t,e),null}async stat(t,e){const i=await this._backend.stat(t,e);return new r(i)}async lstat(t,e){const i=await this._backend.lstat(t,e);return new r(i)}async readlink(t,e){return this._backend.readlink(t,e)}async symlink(t,e){return await this._backend.symlink(t,e),null}async backFile(t,e){return await this._backend.backFile(t,e),null}async du(t){return this._backend.du(t)}async flush(){return this._backend.flush()}}},841:t=>{t.exports=class{constructor(t){this.type=t.type,this.mode=t.mode,this.size=t.size,this.ino=t.ino,this.mtimeMs=t.mtimeMs,this.ctimeMs=t.ctimeMs||t.mtimeMs,this.uid=1,this.gid=1,this.dev=1}isFile(){return"file"===this.type}isDirectory(){return"dir"===this.type}isSymbolicLink(){return"symlink"===this.type}}},658:t=>{function e(t){return class extends Error{constructor(...e){super(...e),this.code=t,this.message?this.message=t+": "+this.message:this.message=t}}}const i=e("EEXIST"),s=e("ENOENT"),r=e("ENOTDIR"),n=e("ENOTEMPTY"),o=e("ETIMEDOUT"),a=e("EISDIR");t.exports={EEXIST:i,ENOENT:s,ENOTDIR:r,ENOTEMPTY:n,ETIMEDOUT:o,EISDIR:a}},138:(t,e,i)=>{const s=i(97),r=i(382);function n(t,e){return"function"==typeof t&&(e=t),[(...t)=>e(null,...t),e=s(e)]}t.exports=class{constructor(...t){this.promises=new r(...t),this.init=this.init.bind(this),this.readFile=this.readFile.bind(this),this.writeFile=this.writeFile.bind(this),this.unlink=this.unlink.bind(this),this.readdir=this.readdir.bind(this),this.mkdir=this.mkdir.bind(this),this.rmdir=this.rmdir.bind(this),this.rename=this.rename.bind(this),this.stat=this.stat.bind(this),this.lstat=this.lstat.bind(this),this.readlink=this.readlink.bind(this),this.symlink=this.symlink.bind(this),this.backFile=this.backFile.bind(this),this.du=this.du.bind(this),this.flush=this.flush.bind(this)}init(t,e){return this.promises.init(t,e)}readFile(t,e,i){const[s,r]=n(e,i);this.promises.readFile(t,e).then(s).catch(r)}writeFile(t,e,i,s){const[r,o]=n(i,s);this.promises.writeFile(t,e,i).then(r).catch(o)}unlink(t,e,i){const[s,r]=n(e,i);this.promises.unlink(t,e).then(s).catch(r)}readdir(t,e,i){const[s,r]=n(e,i);this.promises.readdir(t,e).then(s).catch(r)}mkdir(t,e,i){const[s,r]=n(e,i);this.promises.mkdir(t,e).then(s).catch(r)}rmdir(t,e,i){const[s,r]=n(e,i);this.promises.rmdir(t,e).then(s).catch(r)}rename(t,e,i){const[s,r]=n(i);this.promises.rename(t,e).then(s).catch(r)}stat(t,e,i){const[s,r]=n(e,i);this.promises.stat(t).then(s).catch(r)}lstat(t,e,i){const[s,r]=n(e,i);this.promises.lstat(t).then(s).catch(r)}readlink(t,e,i){const[s,r]=n(e,i);this.promises.readlink(t).then(s).catch(r)}symlink(t,e,i){const[s,r]=n(i);this.promises.symlink(t,e).then(s).catch(r)}backFile(t,e,i){const[s,r]=n(e,i);this.promises.backFile(t,e).then(s).catch(r)}du(t,e){const[i,s]=n(e);this.promises.du(t).then(i).catch(s)}flush(t){const[e,i]=n(t);this.promises.flush().then(e).catch(i)}}},61:t=>{function e(t){if(0===t.length)return".";let e=s(t);return e=e.reduce(r,[]),i(...e)}function i(...t){if(0===t.length)return"";let e=t.join("/");return e=e.replace(/\/{2,}/g,"/"),e}function s(t){if(0===t.length)return[];if("/"===t)return["/"];let e=t.split("/");return""===e[e.length-1]&&e.pop(),"/"===t[0]?e[0]="/":"."!==e[0]&&e.unshift("."),e}function r(t,e){if(0===t.length)return t.push(e),t;if("."===e)return t;if(".."===e){if(1===t.length){if("/"===t[0])throw new Error("Unable to normalize path - traverses above root directory");if("."===t[0])return t.push(e),t}return".."===t[t.length-1]?(t.push(".."),t):(t.pop(),t)}return t.push(e),t}t.exports={join:i,normalize:e,split:s,basename:function(t){if("/"===t)throw new Error(`Cannot get basename of "${t}"`);const e=t.lastIndexOf("/");return-1===e?t:t.slice(e+1)},dirname:function(t){const e=t.lastIndexOf("/");if(-1===e)throw new Error(`Cannot get dirname of "${t}"`);return 0===e?"/":t.slice(0,e)},resolve:function(...t){let s="";for(let r of t)s=r.startsWith("/")?r:e(i(s,r));return s}}},341:(t,e,i)=>{"use strict";i.r(e),i.d(e,{Store:()=>s,clear:()=>l,close:()=>d,del:()=>c,get:()=>o,keys:()=>u,set:()=>a,update:()=>h});class s{constructor(t="keyval-store",e="keyval"){this.storeName=e,this._dbName=t,this._storeName=e,this._init()}_init(){this._dbp||(this._dbp=new Promise(((t,e)=>{const i=indexedDB.open(this._dbName);i.onerror=()=>e(i.error),i.onsuccess=()=>t(i.result),i.onupgradeneeded=()=>{i.result.createObjectStore(this._storeName)}})))}_withIDBStore(t,e){return this._init(),this._dbp.then((i=>new Promise(((s,r)=>{const n=i.transaction(this.storeName,t);n.oncomplete=()=>s(),n.onabort=n.onerror=()=>r(n.error),e(n.objectStore(this.storeName))}))))}_close(){return this._init(),this._dbp.then((t=>{t.close(),this._dbp=void 0}))}}let r;function n(){return r||(r=new s),r}function o(t,e=n()){let i;return e._withIDBStore("readwrite",(e=>{i=e.get(t)})).then((()=>i.result))}function a(t,e,i=n()){return i._withIDBStore("readwrite",(i=>{i.put(e,t)}))}function h(t,e,i=n()){return i._withIDBStore("readwrite",(i=>{const s=i.get(t);s.onsuccess=()=>{i.put(e(s.result),t)}}))}function c(t,e=n()){return e._withIDBStore("readwrite",(e=>{e.delete(t)}))}function l(t=n()){return t._withIDBStore("readwrite",(t=>{t.clear()}))}function u(t=n()){const e=[];return t._withIDBStore("readwrite",(t=>{(t.openKeyCursor||t.openCursor).call(t).onsuccess=function(){this.result&&(e.push(this.result.key),this.result.continue())}})).then((()=>e))}function d(t=n()){return t._close()}}},e={};function i(s){var r=e[s];if(void 0!==r)return r.exports;var n=e[s]={exports:{}};return t[s].call(n.exports,n,n.exports,i),n.exports}return i.d=(t,e)=>{for(var s in e)i.o(e,s)&&!i.o(t,s)&&Object.defineProperty(t,s,{enumerable:!0,get:e[s]})},i.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"==typeof window)return window}}(),i.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),i.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},i(138)})()));