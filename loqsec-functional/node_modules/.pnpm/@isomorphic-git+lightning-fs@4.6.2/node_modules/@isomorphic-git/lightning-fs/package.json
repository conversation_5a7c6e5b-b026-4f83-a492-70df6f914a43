{"publishConfig": {"access": "public"}, "name": "@isomorphic-git/lightning-fs", "version": "4.6.2", "description": "A lean and fast 'fs' for the browser", "main": "src/index.js", "unpkg": "dist/lightning-fs.min.js", "bin": {"superblocktxt": "src/superblocktxt.js"}, "scripts": {"test": "karma start --single-run", "build": "webpack", "semantic-release": "semantic-release"}, "dependencies": {"@isomorphic-git/idb-keyval": "3.3.2", "isomorphic-textencoder": "1.0.1", "just-debounce-it": "1.1.0", "just-once": "1.1.0"}, "devDependencies": {"karma": "^6.4.2", "karma-browserstack-launcher": "^1.5.1", "karma-chrome-launcher": "3.1.0", "karma-edge-launcher": "^0.4.2", "karma-fail-fast-reporter": "1.0.5", "karma-firefox-launcher": "1.2.0", "karma-ie-launcher": "1.0.0", "karma-jasmine": "2.0.1", "karma-junit-reporter": "1.2.0", "karma-safari-launcher": "^1.0.0", "karma-sauce-launcher": "^1.2.0", "karma-verbose-reporter": "0.0.6", "karma-webpack": "^5.0.0", "prettier": "^1.15.3", "puppeteer": "^1.10.0", "semantic-release": "^21.0.9", "webpack": "^5.88.2", "webpack-cli": "^5.1.4"}, "repository": {"type": "git", "url": "git+https://github.com/isomorphic-git/lightning-fs.git"}, "files": ["**/*.js", "index.d.ts", "!__tests__", "!coverage"], "keywords": ["browser", "fs", "indexeddb", "idb"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/isomorphic-git/lightning-fs/issues"}, "homepage": "https://github.com/isomorphic-git/lightning-fs#readme"}