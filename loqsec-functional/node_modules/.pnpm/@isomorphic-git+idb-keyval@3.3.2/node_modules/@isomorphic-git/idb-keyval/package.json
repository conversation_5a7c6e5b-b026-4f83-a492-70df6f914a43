{"publishConfig": {"access": "public"}, "name": "@isomorphic-git/idb-keyval", "version": "3.3.2", "description": "A super-simple-small keyval store built on top of IndexedDB", "main": "./dist/idb-keyval-cjs.js", "module": "./dist/idb-keyval.mjs", "types": "./dist/idb-keyval.d.ts", "scripts": {"build": "del dist && rollup -c && npm run compress-iife && npm run create-compat && npm run create-cjs-compat && npm run compress-amd", "compress-iife": "uglifyjs --compress --mangle -o dist/idb-keyval-iife.min.js dist/idb-keyval-iife.js", "create-compat": "babel dist/idb-keyval-iife.js | uglifyjs --compress --mangle > dist/idb-keyval-iife-compat.min.js", "create-cjs-compat": "babel dist/idb-keyval-cjs.js | uglifyjs --compress --mangle > dist/idb-keyval-cjs-compat.min.js", "compress-amd": "uglifyjs --compress --mangle -o dist/idb-keyval-amd.min.js dist/idb-keyval-amd.js"}, "repository": {"type": "git", "url": "git+https://github.com/isomorphic-git/idb-keyval.git"}, "keywords": ["idb", "indexeddb", "store", "keyval", "localstorage", "storage", "promise"], "license": "Apache-2.0", "bugs": {"url": "https://github.com/isomorphic-git/idb-keyval/issues"}, "homepage": "https://github.com/isomorphic-git/idb-keyval#readme", "devDependencies": {"babel-cli": "^6.26.0", "babel-preset-env": "^1.6.1", "del-cli": "^1.1.0", "rollup": "^0.56.5", "rollup-plugin-typescript2": "^0.12.0", "typescript": "^2.7.2", "uglify-es": "^3.3.9"}}