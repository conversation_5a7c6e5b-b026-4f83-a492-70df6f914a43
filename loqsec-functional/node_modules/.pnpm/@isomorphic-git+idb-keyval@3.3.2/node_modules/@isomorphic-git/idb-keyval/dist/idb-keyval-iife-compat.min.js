"use strict";var _createClass=function(){function t(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),e}}();function _classCallCheck(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}var idbKeyval=function(t){var e=function(){function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"keyval-store",n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"keyval";_classCallCheck(this,t),this.storeName=n,this._dbName=e,this._storeName=n,this._init()}return _createClass(t,[{key:"_init",value:function(){var t=this;this._dbp||(this._dbp=new Promise(function(e,n){var r=indexedDB.open(t._dbName);r.onerror=function(){return n(r.error)},r.onsuccess=function(){return e(r.result)},r.onupgradeneeded=function(){r.result.createObjectStore(t._storeName)}}))}},{key:"_withIDBStore",value:function(t,e){var n=this;return this._init(),this._dbp.then(function(r){return new Promise(function(o,i){var u=r.transaction(n.storeName,t);u.oncomplete=function(){return o()},u.onabort=u.onerror=function(){return i(u.error)},e(u.objectStore(n.storeName))})})}},{key:"_close",value:function(){var t=this;return this._init(),this._dbp.then(function(e){e.close(),t._dbp=void 0})}}]),t}(),n=void 0;function r(){return n||(n=new e),n}return t.Store=e,t.get=function(t){var e=void 0;return(arguments.length>1&&void 0!==arguments[1]?arguments[1]:r())._withIDBStore("readwrite",function(n){e=n.get(t)}).then(function(){return e.result})},t.set=function(t,e){return(arguments.length>2&&void 0!==arguments[2]?arguments[2]:r())._withIDBStore("readwrite",function(n){n.put(e,t)})},t.update=function(t,e){return(arguments.length>2&&void 0!==arguments[2]?arguments[2]:r())._withIDBStore("readwrite",function(n){var r=n.get(t);r.onsuccess=function(){n.put(e(r.result),t)}})},t.del=function(t){return(arguments.length>1&&void 0!==arguments[1]?arguments[1]:r())._withIDBStore("readwrite",function(e){e.delete(t)})},t.clear=function(){return(arguments.length>0&&void 0!==arguments[0]?arguments[0]:r())._withIDBStore("readwrite",function(t){t.clear()})},t.keys=function(){var t=[];return(arguments.length>0&&void 0!==arguments[0]?arguments[0]:r())._withIDBStore("readwrite",function(e){(e.openKeyCursor||e.openCursor).call(e).onsuccess=function(){this.result&&(t.push(this.result.key),this.result.continue())}}).then(function(){return t})},t.close=function(){return(arguments.length>0&&void 0!==arguments[0]?arguments[0]:r())._close()},t}({});
