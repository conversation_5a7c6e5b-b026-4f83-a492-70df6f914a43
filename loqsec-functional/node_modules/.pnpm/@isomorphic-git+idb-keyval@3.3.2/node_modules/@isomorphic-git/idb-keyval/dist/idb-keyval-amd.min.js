define(["exports"],function(e){"use strict";class t{constructor(e="keyval-store",t="keyval"){this.storeName=t,this._dbName=e,this._storeName=t,this._init()}_init(){this._dbp||(this._dbp=new Promise((e,t)=>{const r=indexedDB.open(this._dbName);r.onerror=(()=>t(r.error)),r.onsuccess=(()=>e(r.result)),r.onupgradeneeded=(()=>{r.result.createObjectStore(this._storeName)})}))}_withIDBStore(e,t){return this._init(),this._dbp.then(r=>new Promise((n,o)=>{const s=r.transaction(this.storeName,e);s.oncomplete=(()=>n()),s.onabort=s.onerror=(()=>o(s.error)),t(s.objectStore(this.storeName))}))}_close(){return this._init(),this._dbp.then(e=>{e.close(),this._dbp=void 0})}}let r;function n(){return r||(r=new t),r}e.Store=t,e.get=function(e,t=n()){let r;return t._withIDBStore("readwrite",t=>{r=t.get(e)}).then(()=>r.result)},e.set=function(e,t,r=n()){return r._withIDBStore("readwrite",r=>{r.put(t,e)})},e.update=function(e,t,r=n()){return r._withIDBStore("readwrite",r=>{const n=r.get(e);n.onsuccess=(()=>{r.put(t(n.result),e)})})},e.del=function(e,t=n()){return t._withIDBStore("readwrite",t=>{t.delete(e)})},e.clear=function(e=n()){return e._withIDBStore("readwrite",e=>{e.clear()})},e.keys=function(e=n()){const t=[];return e._withIDBStore("readwrite",e=>{(e.openKeyCursor||e.openCursor).call(e).onsuccess=function(){this.result&&(t.push(this.result.key),this.result.continue())}}).then(()=>t)},e.close=function(e=n()){return e._close()},Object.defineProperty(e,"__esModule",{value:!0})});