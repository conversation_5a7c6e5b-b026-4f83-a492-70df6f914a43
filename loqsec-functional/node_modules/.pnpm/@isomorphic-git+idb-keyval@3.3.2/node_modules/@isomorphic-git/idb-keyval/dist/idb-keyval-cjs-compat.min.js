"use strict";var _createClass=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}();function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(exports,"__esModule",{value:!0});var Store=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"keyval-store",r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"keyval";_classCallCheck(this,e),this.storeName=r,this._dbName=t,this._storeName=r,this._init()}return _createClass(e,[{key:"_init",value:function(){var e=this;this._dbp||(this._dbp=new Promise(function(t,r){var n=indexedDB.open(e._dbName);n.onerror=function(){return r(n.error)},n.onsuccess=function(){return t(n.result)},n.onupgradeneeded=function(){n.result.createObjectStore(e._storeName)}}))}},{key:"_withIDBStore",value:function(e,t){var r=this;return this._init(),this._dbp.then(function(n){return new Promise(function(o,i){var u=n.transaction(r.storeName,e);u.oncomplete=function(){return o()},u.onabort=u.onerror=function(){return i(u.error)},t(u.objectStore(r.storeName))})})}},{key:"_close",value:function(){var e=this;return this._init(),this._dbp.then(function(t){t.close(),e._dbp=void 0})}}]),e}(),store=void 0;function getDefaultStore(){return store||(store=new Store),store}function get(e){var t=void 0;return(arguments.length>1&&void 0!==arguments[1]?arguments[1]:getDefaultStore())._withIDBStore("readwrite",function(r){t=r.get(e)}).then(function(){return t.result})}function set(e,t){return(arguments.length>2&&void 0!==arguments[2]?arguments[2]:getDefaultStore())._withIDBStore("readwrite",function(r){r.put(t,e)})}function update(e,t){return(arguments.length>2&&void 0!==arguments[2]?arguments[2]:getDefaultStore())._withIDBStore("readwrite",function(r){var n=r.get(e);n.onsuccess=function(){r.put(t(n.result),e)}})}function del(e){return(arguments.length>1&&void 0!==arguments[1]?arguments[1]:getDefaultStore())._withIDBStore("readwrite",function(t){t.delete(e)})}function clear(){return(arguments.length>0&&void 0!==arguments[0]?arguments[0]:getDefaultStore())._withIDBStore("readwrite",function(e){e.clear()})}function keys(){var e=[];return(arguments.length>0&&void 0!==arguments[0]?arguments[0]:getDefaultStore())._withIDBStore("readwrite",function(t){(t.openKeyCursor||t.openCursor).call(t).onsuccess=function(){this.result&&(e.push(this.result.key),this.result.continue())}}).then(function(){return e})}function close(){return(arguments.length>0&&void 0!==arguments[0]?arguments[0]:getDefaultStore())._close()}exports.Store=Store,exports.get=get,exports.set=set,exports.update=update,exports.del=del,exports.clear=clear,exports.keys=keys,exports.close=close;
