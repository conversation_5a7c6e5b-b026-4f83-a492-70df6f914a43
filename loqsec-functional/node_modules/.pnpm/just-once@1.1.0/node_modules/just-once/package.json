{"name": "just-once", "version": "1.1.0", "description": "create a function that can only be invoked once", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": "https://github.com/angus-c/just", "keywords": ["function", "once", "no-dependencies", "just"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/angus-c/just/issues"}}