{"name": "@napi-rs/canvas-darwin-x64", "version": "0.1.74", "os": ["darwin"], "cpu": ["x64"], "main": "skia.darwin-x64.node", "files": ["skia.darwin-x64.node"], "description": "Canvas for Node.js with skia backend", "keywords": ["napi-rs", "NAPI", "N-API", "Rust", "node-addon", "node-addon-api", "canvas", "image", "pdf", "svg", "skia"], "license": "MIT", "engines": {"node": ">= 10"}, "publishConfig": {"registry": "https://registry.npmjs.org/", "access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/Brooooooklyn/canvas.git"}}