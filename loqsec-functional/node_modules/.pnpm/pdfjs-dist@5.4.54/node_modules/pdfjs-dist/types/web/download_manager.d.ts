export type IDownloadManager = import("./interfaces").IDownloadManager;
/**
 * @implements {IDownloadManager}
 */
export class DownloadManager implements IDownloadManager {
    downloadData(data: any, filename: any, contentType: any): void;
    /**
     * @returns {boolean} Indicating if the data was opened.
     */
    openOrDownloadData(data: any, filename: any, dest?: null): boolean;
    download(data: any, url: any, filename: any): void;
    #private;
}
