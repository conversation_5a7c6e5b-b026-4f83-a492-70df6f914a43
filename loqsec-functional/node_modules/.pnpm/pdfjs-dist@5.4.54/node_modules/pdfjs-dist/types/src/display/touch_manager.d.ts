export class TouchManager {
    constructor({ container, isPinchingDisabled, isPinchingStopped, onPinchStart, onPinching, onPinchEnd, signal, }: {
        container: any;
        isPinchingDisabled?: null | undefined;
        isPinchingStopped?: null | undefined;
        onPinchStart?: null | undefined;
        onPinching?: null | undefined;
        onPinchEnd?: null | undefined;
        signal: any;
    });
    /**
     * NOTE: Don't shadow this value since `devicePixelRatio` may change if the
     * window resolution changes, e.g. if the viewer is moved to another monitor.
     */
    get MIN_TOUCH_DISTANCE_TO_PINCH(): number;
    destroy(): void;
    #private;
}
