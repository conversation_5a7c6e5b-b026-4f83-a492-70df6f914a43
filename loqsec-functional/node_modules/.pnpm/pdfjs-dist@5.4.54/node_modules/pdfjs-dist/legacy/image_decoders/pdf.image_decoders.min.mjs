/**
 * @licstart The following is the entire license notice for the
 * JavaScript code in this page
 *
 * Copyright 2024 Mozilla Foundation
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * @licend The above is the entire license notice for the
 * JavaScript code in this page
 */
/**
 * pdfjsVersion = 5.4.54
 * pdfjsBuild = 295fb3ec4
 */var e={34:(e,t,r)=>{var n=r(4901);e.exports=function(e){return"object"==typeof e?null!==e:n(e)}},81:(e,t,r)=>{var n=r(9565),i=r(9306),o=r(8551),s=r(6823),a=r(851),c=TypeError;e.exports=function(e,t){var r=arguments.length<2?a(e):t;if(i(r))return o(n(r,e));throw new c(s(e)+" is not iterable")}},283:(e,t,r)=>{var n=r(9504),i=r(9039),o=r(4901),s=r(9297),a=r(3724),c=r(350).CONFIGURABLE,f=r(3706),u=r(1181),l=u.enforce,h=u.get,d=String,p=Object.defineProperty,m=n("".slice),g=n("".replace),y=n([].join),b=a&&!i((function(){return 8!==p((function(){}),"length",{value:8}).length})),w=String(String).split("String"),v=e.exports=function(e,t,r){"Symbol("===m(d(t),0,7)&&(t="["+g(d(t),/^Symbol\(([^)]*)\).*$/,"$1")+"]");r&&r.getter&&(t="get "+t);r&&r.setter&&(t="set "+t);(!s(e,"name")||c&&e.name!==t)&&(a?p(e,"name",{value:t,configurable:!0}):e.name=t);b&&r&&s(r,"arity")&&e.length!==r.arity&&p(e,"length",{value:r.arity});try{r&&s(r,"constructor")&&r.constructor?a&&p(e,"prototype",{writable:!1}):e.prototype&&(e.prototype=void 0)}catch(e){}var n=l(e);s(n,"source")||(n.source=y(w,"string"==typeof t?t:""));return e};Function.prototype.toString=v((function toString(){return o(this)&&h(this).source||f(this)}),"toString")},350:(e,t,r)=>{var n=r(3724),i=r(9297),o=Function.prototype,s=n&&Object.getOwnPropertyDescriptor,a=i(o,"name"),c=a&&"something"===function something(){}.name,f=a&&(!n||n&&s(o,"name").configurable);e.exports={EXISTS:a,PROPER:c,CONFIGURABLE:f}},397:(e,t,r)=>{var n=r(7751);e.exports=n("document","documentElement")},421:e=>{e.exports={}},507:(e,t,r)=>{var n=r(9565);e.exports=function(e,t,r){for(var i,o,s=r?e:e.iterator,a=e.next;!(i=n(a,s)).done;)if(void 0!==(o=t(i.value)))return o}},616:(e,t,r)=>{var n=r(9039);e.exports=!n((function(){var e=function(){}.bind();return"function"!=typeof e||e.hasOwnProperty("prototype")}))},655:(e,t,r)=>{var n=r(6955),i=String;e.exports=function(e){if("Symbol"===n(e))throw new TypeError("Cannot convert a Symbol value to a string");return i(e)}},679:(e,t,r)=>{var n=r(1625),i=TypeError;e.exports=function(e,t){if(n(t,e))return e;throw new i("Incorrect invocation")}},684:e=>{e.exports=function(e,t){var r="function"==typeof Iterator&&Iterator.prototype[e];if(r)try{r.call({next:null},t).next()}catch(e){return!0}}},741:e=>{var t=Math.ceil,r=Math.floor;e.exports=Math.trunc||function trunc(e){var n=+e;return(n>0?r:t)(n)}},757:(e,t,r)=>{var n=r(7751),i=r(4901),o=r(1625),s=r(7040),a=Object;e.exports=s?function(e){return"symbol"==typeof e}:function(e){var t=n("Symbol");return i(t)&&o(t.prototype,a(e))}},851:(e,t,r)=>{var n=r(6955),i=r(5966),o=r(4117),s=r(6269),a=r(8227)("iterator");e.exports=function(e){if(!o(e))return i(e,a)||i(e,"@@iterator")||s[n(e)]}},944:e=>{var t=TypeError;e.exports=function(e){var r=e&&e.alphabet;if(void 0===r||"base64"===r||"base64url"===r)return r||"base64";throw new t("Incorrect `alphabet` option")}},1072:(e,t,r)=>{var n=r(1828),i=r(8727);e.exports=Object.keys||function keys(e){return n(e,i)}},1103:e=>{e.exports=function(e){try{return{error:!1,value:e()}}catch(e){return{error:!0,value:e}}}},1108:(e,t,r)=>{var n=r(6955);e.exports=function(e){var t=n(e);return"BigInt64Array"===t||"BigUint64Array"===t}},1148:(e,t,r)=>{var n=r(6518),i=r(9565),o=r(2652),s=r(9306),a=r(8551),c=r(1767),f=r(9539),u=r(4549)("every",TypeError);n({target:"Iterator",proto:!0,real:!0,forced:u},{every:function every(e){a(this);try{s(e)}catch(e){f(this,"throw",e)}if(u)return i(u,this,e);var t=c(this),r=0;return!o(t,(function(t,n){if(!e(t,r++))return n()}),{IS_RECORD:!0,INTERRUPTED:!0}).stopped}})},1181:(e,t,r)=>{var n,i,o,s=r(8622),a=r(4576),c=r(34),f=r(6699),u=r(9297),l=r(7629),h=r(6119),d=r(421),p="Object already initialized",m=a.TypeError,g=a.WeakMap;if(s||l.state){var y=l.state||(l.state=new g);y.get=y.get;y.has=y.has;y.set=y.set;n=function(e,t){if(y.has(e))throw new m(p);t.facade=e;y.set(e,t);return t};i=function(e){return y.get(e)||{}};o=function(e){return y.has(e)}}else{var b=h("state");d[b]=!0;n=function(e,t){if(u(e,b))throw new m(p);t.facade=e;f(e,b,t);return t};i=function(e){return u(e,b)?e[b]:{}};o=function(e){return u(e,b)}}e.exports={set:n,get:i,has:o,enforce:function(e){return o(e)?i(e):n(e,{})},getterFor:function(e){return function(t){var r;if(!c(t)||(r=i(t)).type!==e)throw new m("Incompatible receiver, "+e+" required");return r}}}},1291:(e,t,r)=>{var n=r(741);e.exports=function(e){var t=+e;return t!=t||0===t?0:n(t)}},1385:(e,t,r)=>{var n=r(9539);e.exports=function(e,t,r){for(var i=e.length-1;i>=0;i--)if(void 0!==e[i])try{r=n(e[i].iterator,t,r)}catch(e){t="throw";r=e}if("throw"===t)throw r;return r}},1548:(e,t,r)=>{var n=r(4576),i=r(9039),o=r(9519),s=r(4215),a=n.structuredClone;e.exports=!!a&&!i((function(){if("DENO"===s&&o>92||"NODE"===s&&o>94||"BROWSER"===s&&o>97)return!1;var e=new ArrayBuffer(8),t=a(e,{transfer:[e]});return 0!==e.byteLength||8!==t.byteLength}))},1549:(e,t,r)=>{var n=r(6518),i=r(4576),o=r(9143),s=r(4154),a=i.Uint8Array,c=!a||!a.prototype.setFromBase64||!function(){var e=new a([255,255,255,255,255]);try{e.setFromBase64("",null);return}catch(e){}try{e.setFromBase64("MjYyZg===")}catch(t){return 50===e[0]&&54===e[1]&&50===e[2]&&255===e[3]&&255===e[4]}}();a&&n({target:"Uint8Array",proto:!0,forced:c},{setFromBase64:function setFromBase64(e){s(this);var t=o(e,arguments.length>1?arguments[1]:void 0,this,this.length);return{read:t.read,written:t.written}}})},1625:(e,t,r)=>{var n=r(9504);e.exports=n({}.isPrototypeOf)},1689:(e,t,r)=>{var n=r(6518),i=r(4576),o=r(8745),s=r(7680),a=r(6043),c=r(9306),f=r(1103),u=i.Promise,l=!1;n({target:"Promise",stat:!0,forced:!u||!u.try||f((function(){u.try((function(e){l=8===e}),8)})).error||!l},{try:function(e){var t=arguments.length>1?s(arguments,1):[],r=a.f(this),n=f((function(){return o(c(e),void 0,t)}));(n.error?r.reject:r.resolve)(n.value);return r.promise}})},1698:(e,t,r)=>{var n=r(6518),i=r(4204),o=r(9835);n({target:"Set",proto:!0,real:!0,forced:!r(4916)("union")||!o("union")},{union:i})},1701:(e,t,r)=>{var n=r(6518),i=r(9565),o=r(9306),s=r(8551),a=r(1767),c=r(9462),f=r(6319),u=r(9539),l=r(684),h=r(4549),d=r(6395),p=!d&&!l("map",(function(){})),m=!d&&!p&&h("map",TypeError),g=d||p||m,y=c((function(){var e=this.iterator,t=s(i(this.next,e));if(!(this.done=!!t.done))return f(e,this.mapper,[t.value,this.counter++],!0)}));n({target:"Iterator",proto:!0,real:!0,forced:g},{map:function map(e){s(this);try{o(e)}catch(e){u(this,"throw",e)}return m?i(m,this,e):new y(a(this),{mapper:e})}})},1767:e=>{e.exports=function(e){return{iterator:e,next:e.next,done:!1}}},1828:(e,t,r)=>{var n=r(9504),i=r(9297),o=r(5397),s=r(9617).indexOf,a=r(421),c=n([].push);e.exports=function(e,t){var r,n=o(e),f=0,u=[];for(r in n)!i(a,r)&&i(n,r)&&c(u,r);for(;t.length>f;)i(n,r=t[f++])&&(~s(u,r)||c(u,r));return u}},2106:(e,t,r)=>{var n=r(283),i=r(4913);e.exports=function(e,t,r){r.get&&n(r.get,t,{getter:!0});r.set&&n(r.set,t,{setter:!0});return i.f(e,t,r)}},2140:(e,t,r)=>{var n={};n[r(8227)("toStringTag")]="z";e.exports="[object z]"===String(n)},2195:(e,t,r)=>{var n=r(9504),i=n({}.toString),o=n("".slice);e.exports=function(e){return o(i(e),8,-1)}},2211:(e,t,r)=>{var n=r(9039);e.exports=!n((function(){function F(){}F.prototype.constructor=null;return Object.getPrototypeOf(new F)!==F.prototype}))},2303:(e,t,r)=>{var n=r(4576),i=r(9504),o=n.Uint8Array,s=n.SyntaxError,a=n.parseInt,c=Math.min,f=/[^\da-f]/i,u=i(f.exec),l=i("".slice);e.exports=function(e,t){var r=e.length;if(r%2!=0)throw new s("String should be an even number of characters");for(var n=t?c(t.length,r/2):r/2,i=t||new o(n),h=0,d=0;d<n;){var p=l(e,h,h+=2);if(u(f,p))throw new s("String should only contain hex characters");i[d++]=a(p,16)}return{bytes:i,read:h}}},2360:(e,t,r)=>{var n,i=r(8551),o=r(6801),s=r(8727),a=r(421),c=r(397),f=r(4055),u=r(6119),l="prototype",h="script",d=u("IE_PROTO"),EmptyConstructor=function(){},scriptTag=function(e){return"<"+h+">"+e+"</"+h+">"},NullProtoObjectViaActiveX=function(e){e.write(scriptTag(""));e.close();var t=e.parentWindow.Object;e=null;return t},NullProtoObject=function(){try{n=new ActiveXObject("htmlfile")}catch(e){}NullProtoObject="undefined"!=typeof document?document.domain&&n?NullProtoObjectViaActiveX(n):function(){var e,t=f("iframe"),r="java"+h+":";t.style.display="none";c.appendChild(t);t.src=String(r);(e=t.contentWindow.document).open();e.write(scriptTag("document.F=Object"));e.close();return e.F}():NullProtoObjectViaActiveX(n);for(var e=s.length;e--;)delete NullProtoObject[l][s[e]];return NullProtoObject()};a[d]=!0;e.exports=Object.create||function create(e,t){var r;if(null!==e){EmptyConstructor[l]=i(e);r=new EmptyConstructor;EmptyConstructor[l]=null;r[d]=e}else r=NullProtoObject();return void 0===t?r:o.f(r,t)}},2475:(e,t,r)=>{var n=r(6518),i=r(8527);n({target:"Set",proto:!0,real:!0,forced:!r(4916)("isSupersetOf",(function(e){return!e}))},{isSupersetOf:i})},2529:e=>{e.exports=function(e,t){return{value:e,done:t}}},2603:(e,t,r)=>{var n=r(655);e.exports=function(e,t){return void 0===e?arguments.length<2?"":t:n(e)}},2652:(e,t,r)=>{var n=r(6080),i=r(9565),o=r(8551),s=r(6823),a=r(4209),c=r(6198),f=r(1625),u=r(81),l=r(851),h=r(9539),d=TypeError,Result=function(e,t){this.stopped=e;this.result=t},p=Result.prototype;e.exports=function(e,t,r){var m,g,y,b,w,v,_,x=r&&r.that,C=!(!r||!r.AS_ENTRIES),R=!(!r||!r.IS_RECORD),A=!(!r||!r.IS_ITERATOR),S=!(!r||!r.INTERRUPTED),B=n(t,x),stop=function(e){m&&h(m,"normal");return new Result(!0,e)},callFn=function(e){if(C){o(e);return S?B(e[0],e[1],stop):B(e[0],e[1])}return S?B(e,stop):B(e)};if(R)m=e.iterator;else if(A)m=e;else{if(!(g=l(e)))throw new d(s(e)+" is not iterable");if(a(g)){for(y=0,b=c(e);b>y;y++)if((w=callFn(e[y]))&&f(p,w))return w;return new Result(!1)}m=u(e,g)}v=R?e.next:m.next;for(;!(_=i(v,m)).done;){try{w=callFn(_.value)}catch(e){h(m,"throw",e)}if("object"==typeof w&&w&&f(p,w))return w}return new Result(!1)}},2777:(e,t,r)=>{var n=r(9565),i=r(34),o=r(757),s=r(5966),a=r(4270),c=r(8227),f=TypeError,u=c("toPrimitive");e.exports=function(e,t){if(!i(e)||o(e))return e;var r,c=s(e,u);if(c){void 0===t&&(t="default");r=n(c,e,t);if(!i(r)||o(r))return r;throw new f("Can't convert object to primitive value")}void 0===t&&(t="number");return a(e,t)}},2787:(e,t,r)=>{var n=r(9297),i=r(4901),o=r(8981),s=r(6119),a=r(2211),c=s("IE_PROTO"),f=Object,u=f.prototype;e.exports=a?f.getPrototypeOf:function(e){var t=o(e);if(n(t,c))return t[c];var r=t.constructor;return i(r)&&t instanceof r?r.prototype:t instanceof f?u:null}},2796:(e,t,r)=>{var n=r(9039),i=r(4901),o=/#|\.prototype\./,isForced=function(e,t){var r=a[s(e)];return r===f||r!==c&&(i(t)?n(t):!!t)},s=isForced.normalize=function(e){return String(e).replace(o,".").toLowerCase()},a=isForced.data={},c=isForced.NATIVE="N",f=isForced.POLYFILL="P";e.exports=isForced},2804:e=>{var t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",r=t+"+/",n=t+"-_",inverse=function(e){for(var t={},r=0;r<64;r++)t[e.charAt(r)]=r;return t};e.exports={i2c:r,c2i:inverse(r),i2cUrl:n,c2iUrl:inverse(n)}},2812:e=>{var t=TypeError;e.exports=function(e,r){if(e<r)throw new t("Not enough arguments");return e}},2839:(e,t,r)=>{var n=r(4576).navigator,i=n&&n.userAgent;e.exports=i?String(i):""},2967:(e,t,r)=>{var n=r(6706),i=r(34),o=r(7750),s=r(3506);e.exports=Object.setPrototypeOf||("__proto__"in{}?function(){var e,t=!1,r={};try{(e=n(Object.prototype,"__proto__","set"))(r,[]);t=r instanceof Array}catch(e){}return function setPrototypeOf(r,n){o(r);s(n);if(!i(r))return r;t?e(r,n):r.__proto__=n;return r}}():void 0)},3167:(e,t,r)=>{var n=r(4901),i=r(34),o=r(2967);e.exports=function(e,t,r){var s,a;o&&n(s=t.constructor)&&s!==r&&i(a=s.prototype)&&a!==r.prototype&&o(e,a);return e}},3238:(e,t,r)=>{var n=r(4576),i=r(7811),o=r(7394),s=n.DataView;e.exports=function(e){if(!i||0!==o(e))return!1;try{new s(e);return!1}catch(e){return!0}}},3392:(e,t,r)=>{var n=r(9504),i=0,o=Math.random(),s=n(1.1.toString);e.exports=function(e){return"Symbol("+(void 0===e?"":e)+")_"+s(++i+o,36)}},3440:(e,t,r)=>{var n=r(7080),i=r(4402),o=r(9286),s=r(5170),a=r(3789),c=r(8469),f=r(507),u=i.has,l=i.remove;e.exports=function difference(e){var t=n(this),r=a(e),i=o(t);s(t)<=r.size?c(t,(function(e){r.includes(e)&&l(i,e)})):f(r.getIterator(),(function(e){u(i,e)&&l(i,e)}));return i}},3463:e=>{var t=TypeError;e.exports=function(e){if("string"==typeof e)return e;throw new t("Argument is not a string")}},3506:(e,t,r)=>{var n=r(3925),i=String,o=TypeError;e.exports=function(e){if(n(e))return e;throw new o("Can't set "+i(e)+" as a prototype")}},3650:(e,t,r)=>{var n=r(7080),i=r(4402),o=r(9286),s=r(3789),a=r(507),c=i.add,f=i.has,u=i.remove;e.exports=function symmetricDifference(e){var t=n(this),r=s(e).getIterator(),i=o(t);a(r,(function(e){f(t,e)?u(i,e):c(i,e)}));return i}},3706:(e,t,r)=>{var n=r(9504),i=r(4901),o=r(7629),s=n(Function.toString);i(o.inspectSource)||(o.inspectSource=function(e){return s(e)});e.exports=o.inspectSource},3717:(e,t)=>{t.f=Object.getOwnPropertySymbols},3724:(e,t,r)=>{var n=r(9039);e.exports=!n((function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]}))},3789:(e,t,r)=>{var n=r(9306),i=r(8551),o=r(9565),s=r(1291),a=r(1767),c="Invalid size",f=RangeError,u=TypeError,l=Math.max,SetRecord=function(e,t){this.set=e;this.size=l(t,0);this.has=n(e.has);this.keys=n(e.keys)};SetRecord.prototype={getIterator:function(){return a(i(o(this.keys,this.set)))},includes:function(e){return o(this.has,this.set,e)}};e.exports=function(e){i(e);var t=+e.size;if(t!=t)throw new u(c);var r=s(t);if(r<0)throw new f(c);return new SetRecord(e,r)}},3838:(e,t,r)=>{var n=r(7080),i=r(5170),o=r(8469),s=r(3789);e.exports=function isSubsetOf(e){var t=n(this),r=s(e);return!(i(t)>r.size)&&!1!==o(t,(function(e){if(!r.includes(e))return!1}),!0)}},3853:(e,t,r)=>{var n=r(6518),i=r(4449);n({target:"Set",proto:!0,real:!0,forced:!r(4916)("isDisjointFrom",(function(e){return!e}))},{isDisjointFrom:i})},3925:(e,t,r)=>{var n=r(34);e.exports=function(e){return n(e)||null===e}},3972:(e,t,r)=>{var n=r(34),i=String,o=TypeError;e.exports=function(e){if(void 0===e||n(e))return e;throw new o(i(e)+" is not an object or undefined")}},4055:(e,t,r)=>{var n=r(4576),i=r(34),o=n.document,s=i(o)&&i(o.createElement);e.exports=function(e){return s?o.createElement(e):{}}},4114:(e,t,r)=>{var n=r(6518),i=r(8981),o=r(6198),s=r(4527),a=r(6837);n({target:"Array",proto:!0,arity:1,forced:r(9039)((function(){return 4294967297!==[].push.call({length:4294967296},1)}))||!function(){try{Object.defineProperty([],"length",{writable:!1}).push()}catch(e){return e instanceof TypeError}}()},{push:function push(e){var t=i(this),r=o(t),n=arguments.length;a(r+n);for(var c=0;c<n;c++){t[r]=arguments[c];r++}s(t,r);return r}})},4117:e=>{e.exports=function(e){return null==e}},4154:(e,t,r)=>{var n=r(6955),i=TypeError;e.exports=function(e){if("Uint8Array"===n(e))return e;throw new i("Argument is not an Uint8Array")}},4204:(e,t,r)=>{var n=r(7080),i=r(4402).add,o=r(9286),s=r(3789),a=r(507);e.exports=function union(e){var t=n(this),r=s(e).getIterator(),c=o(t);a(r,(function(e){i(c,e)}));return c}},4209:(e,t,r)=>{var n=r(8227),i=r(6269),o=n("iterator"),s=Array.prototype;e.exports=function(e){return void 0!==e&&(i.Array===e||s[o]===e)}},4215:(e,t,r)=>{var n=r(4576),i=r(2839),o=r(2195),userAgentStartsWith=function(e){return i.slice(0,e.length)===e};e.exports=userAgentStartsWith("Bun/")?"BUN":userAgentStartsWith("Cloudflare-Workers")?"CLOUDFLARE":userAgentStartsWith("Deno/")?"DENO":userAgentStartsWith("Node.js/")?"NODE":n.Bun&&"string"==typeof Bun.version?"BUN":n.Deno&&"object"==typeof Deno.version?"DENO":"process"===o(n.process)?"NODE":n.window&&n.document?"BROWSER":"REST"},4235:(e,t,r)=>{var n=r(6518),i=r(9504),o=r(2652),s=RangeError,a=TypeError,c=1/0,f=Math.abs,u=Math.pow,l=i([].push),h=u(2,1023),d=u(2,53)-1,p=Number.MAX_VALUE,m=u(2,971),g={},y={},b={},w={},v={},twosum=function(e,t){var r=e+t;return{hi:r,lo:t-(r-e)}};n({target:"Math",stat:!0},{sumPrecise:function sumPrecise(e){var t=[],r=0,n=w;o(e,(function(e){if(++r>=d)throw new s("Maximum allowed index exceeded");if("number"!=typeof e)throw new a("Value is not a number");if(n!==g)if(e!=e)n=g;else if(e===c)n=n===y?g:b;else if(e===-1/0)n=n===b?g:y;else if(!(0===e&&1/e!==c||n!==w&&n!==v)){n=v;l(t,e)}}));switch(n){case g:return NaN;case y:return-1/0;case b:return c;case w:return-0}for(var i,u,_,x,C,R,A=[],S=0,B=0;B<t.length;B++){i=t[B];for(var I=0,E=0;E<A.length;E++){u=A[E];if(f(i)<f(u)){R=i;i=u;u=R}x=(_=twosum(i,u)).hi;C=_.lo;if(f(x)===c){var k=x===c?1:-1;S+=k;if(f(i=i-k*h-k*h)<f(u)){R=i;i=u;u=R}x=(_=twosum(i,u)).hi;C=_.lo}0!==C&&(A[I++]=C);i=x}A.length=I;0!==i&&l(A,i)}var D=A.length-1;x=0;C=0;if(0!==S){var T=D>=0?A[D]:0;D--;if(f(S)>1||S>0&&T>0||S<0&&T<0)return S>0?c:-1/0;x=(_=twosum(S*h,T/2)).hi;C=_.lo;C*=2;if(f(2*x)===c)return x>0?x===h&&C===-m/2&&D>=0&&A[D]<0?p:c:x===-h&&C===m/2&&D>=0&&A[D]>0?-p:-1/0;if(0!==C){A[++D]=C;C=0}x*=2}for(;D>=0;){x=(_=twosum(x,A[D--])).hi;if(0!==(C=_.lo))break}D>=0&&(C<0&&A[D]<0||C>0&&A[D]>0)&&(u=2*C)===(i=x+u)-x&&(x=i);return x}})},4270:(e,t,r)=>{var n=r(9565),i=r(4901),o=r(34),s=TypeError;e.exports=function(e,t){var r,a;if("string"===t&&i(r=e.toString)&&!o(a=n(r,e)))return a;if(i(r=e.valueOf)&&!o(a=n(r,e)))return a;if("string"!==t&&i(r=e.toString)&&!o(a=n(r,e)))return a;throw new s("Can't convert object to primitive value")}},4376:(e,t,r)=>{var n=r(2195);e.exports=Array.isArray||function isArray(e){return"Array"===n(e)}},4402:(e,t,r)=>{var n=r(9504),i=Set.prototype;e.exports={Set,add:n(i.add),has:n(i.has),remove:n(i.delete),proto:i}},4449:(e,t,r)=>{var n=r(7080),i=r(4402).has,o=r(5170),s=r(3789),a=r(8469),c=r(507),f=r(9539);e.exports=function isDisjointFrom(e){var t=n(this),r=s(e);if(o(t)<=r.size)return!1!==a(t,(function(e){if(r.includes(e))return!1}),!0);var u=r.getIterator();return!1!==c(u,(function(e){if(i(t,e))return f(u,"normal",!1)}))}},4483:(e,t,r)=>{var n,i,o,s,a=r(4576),c=r(9429),f=r(1548),u=a.structuredClone,l=a.ArrayBuffer,h=a.MessageChannel,d=!1;if(f)d=function(e){u(e,{transfer:[e]})};else if(l)try{h||(n=c("worker_threads"))&&(h=n.MessageChannel);if(h){i=new h;o=new l(2);s=function(e){i.port1.postMessage(null,[e])};if(2===o.byteLength){s(o);0===o.byteLength&&(d=s)}}}catch(e){}e.exports=d},4495:(e,t,r)=>{var n=r(9519),i=r(9039),o=r(4576).String;e.exports=!!Object.getOwnPropertySymbols&&!i((function(){var e=Symbol("symbol detection");return!o(e)||!(Object(e)instanceof Symbol)||!Symbol.sham&&n&&n<41}))},4527:(e,t,r)=>{var n=r(3724),i=r(4376),o=TypeError,s=Object.getOwnPropertyDescriptor,a=n&&!function(){if(void 0!==this)return!0;try{Object.defineProperty([],"length",{writable:!1}).length=1}catch(e){return e instanceof TypeError}}();e.exports=a?function(e,t){if(i(e)&&!s(e,"length").writable)throw new o("Cannot set read only .length");return e.length=t}:function(e,t){return e.length=t}},4549:(e,t,r)=>{var n=r(4576);e.exports=function(e,t){var r=n.Iterator,i=r&&r.prototype,o=i&&i[e],s=!1;if(o)try{o.call({next:function(){return{done:!0}},return:function(){s=!0}},-1)}catch(e){e instanceof t||(s=!1)}if(!s)return o}},4576:function(e){var check=function(e){return e&&e.Math===Math&&e};e.exports=check("object"==typeof globalThis&&globalThis)||check("object"==typeof window&&window)||check("object"==typeof self&&self)||check("object"==typeof global&&global)||check("object"==typeof this&&this)||function(){return this}()||Function("return this")()},4603:(e,t,r)=>{var n=r(6840),i=r(9504),o=r(655),s=r(2812),a=URLSearchParams,c=a.prototype,f=i(c.append),u=i(c.delete),l=i(c.forEach),h=i([].push),d=new a("a=1&a=2&b=3");d.delete("a",1);d.delete("b",void 0);d+""!="a=2"&&n(c,"delete",(function(e){var t=arguments.length,r=t<2?void 0:arguments[1];if(t&&void 0===r)return u(this,e);var n=[];l(this,(function(e,t){h(n,{key:t,value:e})}));s(t,1);for(var i,a=o(e),c=o(r),d=0,p=0,m=!1,g=n.length;d<g;){i=n[d++];if(m||i.key===a){m=!0;u(this,i.key)}else p++}for(;p<g;)(i=n[p++]).key===a&&i.value===c||f(this,i.key,i.value)}),{enumerable:!0,unsafe:!0})},4628:(e,t,r)=>{var n=r(6518),i=r(6043);n({target:"Promise",stat:!0},{withResolvers:function withResolvers(){var e=i.f(this);return{promise:e.promise,resolve:e.resolve,reject:e.reject}}})},4644:(e,t,r)=>{var n,i,o,s=r(7811),a=r(3724),c=r(4576),f=r(4901),u=r(34),l=r(9297),h=r(6955),d=r(6823),p=r(6699),m=r(6840),g=r(2106),y=r(1625),b=r(2787),w=r(2967),v=r(8227),_=r(3392),x=r(1181),C=x.enforce,R=x.get,A=c.Int8Array,S=A&&A.prototype,B=c.Uint8ClampedArray,I=B&&B.prototype,E=A&&b(A),k=S&&b(S),D=Object.prototype,T=c.TypeError,M=v("toStringTag"),P=_("TYPED_ARRAY_TAG"),O="TypedArrayConstructor",U=s&&!!w&&"Opera"!==h(c.opera),L=!1,N={Int8Array:1,Uint8Array:1,Uint8ClampedArray:1,Int16Array:2,Uint16Array:2,Int32Array:4,Uint32Array:4,Float32Array:4,Float64Array:8},G={BigInt64Array:8,BigUint64Array:8},getTypedArrayConstructor=function(e){var t=b(e);if(u(t)){var r=R(t);return r&&l(r,O)?r[O]:getTypedArrayConstructor(t)}},isTypedArray=function(e){if(!u(e))return!1;var t=h(e);return l(N,t)||l(G,t)};for(n in N)(o=(i=c[n])&&i.prototype)?C(o)[O]=i:U=!1;for(n in G)(o=(i=c[n])&&i.prototype)&&(C(o)[O]=i);if(!U||!f(E)||E===Function.prototype){E=function TypedArray(){throw new T("Incorrect invocation")};if(U)for(n in N)c[n]&&w(c[n],E)}if(!U||!k||k===D){k=E.prototype;if(U)for(n in N)c[n]&&w(c[n].prototype,k)}U&&b(I)!==k&&w(I,k);if(a&&!l(k,M)){L=!0;g(k,M,{configurable:!0,get:function(){return u(this)?this[P]:void 0}});for(n in N)c[n]&&p(c[n],P,n)}e.exports={NATIVE_ARRAY_BUFFER_VIEWS:U,TYPED_ARRAY_TAG:L&&P,aTypedArray:function(e){if(isTypedArray(e))return e;throw new T("Target is not a typed array")},aTypedArrayConstructor:function(e){if(f(e)&&(!w||y(E,e)))return e;throw new T(d(e)+" is not a typed array constructor")},exportTypedArrayMethod:function(e,t,r,n){if(a){if(r)for(var i in N){var o=c[i];if(o&&l(o.prototype,e))try{delete o.prototype[e]}catch(r){try{o.prototype[e]=t}catch(e){}}}k[e]&&!r||m(k,e,r?t:U&&S[e]||t,n)}},exportTypedArrayStaticMethod:function(e,t,r){var n,i;if(a){if(w){if(r)for(n in N)if((i=c[n])&&l(i,e))try{delete i[e]}catch(e){}if(E[e]&&!r)return;try{return m(E,e,r?t:U&&E[e]||t)}catch(e){}}for(n in N)!(i=c[n])||i[e]&&!r||m(i,e,t)}},getTypedArrayConstructor,isView:function isView(e){if(!u(e))return!1;var t=h(e);return"DataView"===t||l(N,t)||l(G,t)},isTypedArray,TypedArray:E,TypedArrayPrototype:k}},4659:(e,t,r)=>{var n=r(3724),i=r(4913),o=r(6980);e.exports=function(e,t,r){n?i.f(e,t,o(0,r)):e[t]=r}},4901:e=>{var t="object"==typeof document&&document.all;e.exports=void 0===t&&void 0!==t?function(e){return"function"==typeof e||e===t}:function(e){return"function"==typeof e}},4913:(e,t,r)=>{var n=r(3724),i=r(5917),o=r(8686),s=r(8551),a=r(6969),c=TypeError,f=Object.defineProperty,u=Object.getOwnPropertyDescriptor,l="enumerable",h="configurable",d="writable";t.f=n?o?function defineProperty(e,t,r){s(e);t=a(t);s(r);if("function"==typeof e&&"prototype"===t&&"value"in r&&d in r&&!r[d]){var n=u(e,t);if(n&&n[d]){e[t]=r.value;r={configurable:h in r?r[h]:n[h],enumerable:l in r?r[l]:n[l],writable:!1}}}return f(e,t,r)}:f:function defineProperty(e,t,r){s(e);t=a(t);s(r);if(i)try{return f(e,t,r)}catch(e){}if("get"in r||"set"in r)throw new c("Accessors not supported");"value"in r&&(e[t]=r.value);return e}},4916:(e,t,r)=>{var n=r(7751),createSetLike=function(e){return{size:e,has:function(){return!1},keys:function(){return{next:function(){return{done:!0}}}}}},createSetLikeWithInfinitySize=function(e){return{size:e,has:function(){return!0},keys:function(){throw new Error("e")}}};e.exports=function(e,t){var r=n("Set");try{(new r)[e](createSetLike(0));try{(new r)[e](createSetLike(-1));return!1}catch(n){if(!t)return!0;try{(new r)[e](createSetLikeWithInfinitySize(-1/0));return!1}catch(n){var i=new r;i.add(1);i.add(2);return t(i[e](createSetLikeWithInfinitySize(1/0)))}}}catch(e){return!1}}},4979:(e,t,r)=>{var n=r(6518),i=r(4576),o=r(7751),s=r(6980),a=r(4913).f,c=r(9297),f=r(679),u=r(3167),l=r(2603),h=r(5002),d=r(8574),p=r(3724),m=r(6395),g="DOMException",y=o("Error"),b=o(g),w=function DOMException(){f(this,v);var e=arguments.length,t=l(e<1?void 0:arguments[0]),r=l(e<2?void 0:arguments[1],"Error"),n=new b(t,r),i=new y(t);i.name=g;a(n,"stack",s(1,d(i.stack,1)));u(n,this,w);return n},v=w.prototype=b.prototype,_="stack"in new y(g),x="stack"in new b(1,2),C=b&&p&&Object.getOwnPropertyDescriptor(i,g),R=!(!C||C.writable&&C.configurable),A=_&&!R&&!x;n({global:!0,constructor:!0,forced:m||A},{DOMException:A?w:b});var S=o(g),B=S.prototype;if(B.constructor!==S){m||a(B,"constructor",s(1,S));for(var I in h)if(c(h,I)){var E=h[I],k=E.s;c(S,k)||a(S,k,s(6,E.c))}}},5002:e=>{e.exports={IndexSizeError:{s:"INDEX_SIZE_ERR",c:1,m:1},DOMStringSizeError:{s:"DOMSTRING_SIZE_ERR",c:2,m:0},HierarchyRequestError:{s:"HIERARCHY_REQUEST_ERR",c:3,m:1},WrongDocumentError:{s:"WRONG_DOCUMENT_ERR",c:4,m:1},InvalidCharacterError:{s:"INVALID_CHARACTER_ERR",c:5,m:1},NoDataAllowedError:{s:"NO_DATA_ALLOWED_ERR",c:6,m:0},NoModificationAllowedError:{s:"NO_MODIFICATION_ALLOWED_ERR",c:7,m:1},NotFoundError:{s:"NOT_FOUND_ERR",c:8,m:1},NotSupportedError:{s:"NOT_SUPPORTED_ERR",c:9,m:1},InUseAttributeError:{s:"INUSE_ATTRIBUTE_ERR",c:10,m:1},InvalidStateError:{s:"INVALID_STATE_ERR",c:11,m:1},SyntaxError:{s:"SYNTAX_ERR",c:12,m:1},InvalidModificationError:{s:"INVALID_MODIFICATION_ERR",c:13,m:1},NamespaceError:{s:"NAMESPACE_ERR",c:14,m:1},InvalidAccessError:{s:"INVALID_ACCESS_ERR",c:15,m:1},ValidationError:{s:"VALIDATION_ERR",c:16,m:0},TypeMismatchError:{s:"TYPE_MISMATCH_ERR",c:17,m:1},SecurityError:{s:"SECURITY_ERR",c:18,m:1},NetworkError:{s:"NETWORK_ERR",c:19,m:1},AbortError:{s:"ABORT_ERR",c:20,m:1},URLMismatchError:{s:"URL_MISMATCH_ERR",c:21,m:1},QuotaExceededError:{s:"QUOTA_EXCEEDED_ERR",c:22,m:1},TimeoutError:{s:"TIMEOUT_ERR",c:23,m:1},InvalidNodeTypeError:{s:"INVALID_NODE_TYPE_ERR",c:24,m:1},DataCloneError:{s:"DATA_CLONE_ERR",c:25,m:1}}},5024:(e,t,r)=>{var n=r(6518),i=r(3650),o=r(9835);n({target:"Set",proto:!0,real:!0,forced:!r(4916)("symmetricDifference")||!o("symmetricDifference")},{symmetricDifference:i})},5031:(e,t,r)=>{var n=r(7751),i=r(9504),o=r(8480),s=r(3717),a=r(8551),c=i([].concat);e.exports=n("Reflect","ownKeys")||function ownKeys(e){var t=o.f(a(e)),r=s.f;return r?c(t,r(e)):t}},5169:(e,t,r)=>{var n=r(3238),i=TypeError;e.exports=function(e){if(n(e))throw new i("ArrayBuffer is detached");return e}},5170:(e,t,r)=>{var n=r(6706),i=r(4402);e.exports=n(i.proto,"size","get")||function(e){return e.size}},5370:(e,t,r)=>{var n=r(6198);e.exports=function(e,t,r){for(var i=0,o=arguments.length>2?r:n(t),s=new e(o);o>i;)s[i]=t[i++];return s}},5397:(e,t,r)=>{var n=r(7055),i=r(7750);e.exports=function(e){return n(i(e))}},5610:(e,t,r)=>{var n=r(1291),i=Math.max,o=Math.min;e.exports=function(e,t){var r=n(e);return r<0?i(r+t,0):o(r,t)}},5623:(e,t,r)=>{var n=r(6518),i=r(4576),o=r(9504),s=r(4154),a=r(5169),c=o(1.1.toString),f=i.Uint8Array,u=!f||!f.prototype.toHex||!function(){try{return"ffffffffffffffff"===new f([255,255,255,255,255,255,255,255]).toHex()}catch(e){return!1}}();f&&n({target:"Uint8Array",proto:!0,forced:u},{toHex:function toHex(){s(this);a(this.buffer);for(var e="",t=0,r=this.length;t<r;t++){var n=c(this[t],16);e+=1===n.length?"0"+n:n}return e}})},5636:(e,t,r)=>{var n=r(4576),i=r(9504),o=r(6706),s=r(7696),a=r(5169),c=r(7394),f=r(4483),u=r(1548),l=n.structuredClone,h=n.ArrayBuffer,d=n.DataView,p=Math.min,m=h.prototype,g=d.prototype,y=i(m.slice),b=o(m,"resizable","get"),w=o(m,"maxByteLength","get"),v=i(g.getInt8),_=i(g.setInt8);e.exports=(u||f)&&function(e,t,r){var n,i=c(e),o=void 0===t?i:s(t),m=!b||!b(e);a(e);if(u){e=l(e,{transfer:[e]});if(i===o&&(r||m))return e}if(i>=o&&(!r||m))n=y(e,0,o);else{var g=r&&!m&&w?{maxByteLength:w(e)}:void 0;n=new h(o,g);for(var x=new d(e),C=new d(n),R=p(o,i),A=0;A<R;A++)_(C,A,v(x,A))}u||f(e);return n}},5745:(e,t,r)=>{var n=r(7629);e.exports=function(e,t){return n[e]||(n[e]=t||{})}},5781:(e,t,r)=>{var n=r(6518),i=r(7751),o=r(2812),s=r(655),a=r(7416),c=i("URL");n({target:"URL",stat:!0,forced:!a},{parse:function parse(e){var t=o(arguments.length,1),r=s(e),n=t<2||void 0===arguments[1]?void 0:s(arguments[1]);try{return new c(r,n)}catch(e){return null}}})},5854:(e,t,r)=>{var n=r(2777),i=TypeError;e.exports=function(e){var t=n(e,"number");if("number"==typeof t)throw new i("Can't convert number to bigint");return BigInt(t)}},5876:(e,t,r)=>{var n=r(6518),i=r(3838);n({target:"Set",proto:!0,real:!0,forced:!r(4916)("isSubsetOf",(function(e){return e}))},{isSubsetOf:i})},5917:(e,t,r)=>{var n=r(3724),i=r(9039),o=r(4055);e.exports=!n&&!i((function(){return 7!==Object.defineProperty(o("div"),"a",{get:function(){return 7}}).a}))},5966:(e,t,r)=>{var n=r(9306),i=r(4117);e.exports=function(e,t){var r=e[t];return i(r)?void 0:n(r)}},6043:(e,t,r)=>{var n=r(9306),i=TypeError,PromiseCapability=function(e){var t,r;this.promise=new e((function(e,n){if(void 0!==t||void 0!==r)throw new i("Bad Promise constructor");t=e;r=n}));this.resolve=n(t);this.reject=n(r)};e.exports.f=function(e){return new PromiseCapability(e)}},6080:(e,t,r)=>{var n=r(7476),i=r(9306),o=r(616),s=n(n.bind);e.exports=function(e,t){i(e);return void 0===t?e:o?s(e,t):function(){return e.apply(t,arguments)}}},6119:(e,t,r)=>{var n=r(5745),i=r(3392),o=n("keys");e.exports=function(e){return o[e]||(o[e]=i(e))}},6193:(e,t,r)=>{var n=r(4215);e.exports="NODE"===n},6198:(e,t,r)=>{var n=r(8014);e.exports=function(e){return n(e.length)}},6269:e=>{e.exports={}},6279:(e,t,r)=>{var n=r(6840);e.exports=function(e,t,r){for(var i in t)n(e,i,t[i],r);return e}},6319:(e,t,r)=>{var n=r(8551),i=r(9539);e.exports=function(e,t,r,o){try{return o?t(n(r)[0],r[1]):t(r)}catch(t){i(e,"throw",t)}}},6395:e=>{e.exports=!1},6518:(e,t,r)=>{var n=r(4576),i=r(7347).f,o=r(6699),s=r(6840),a=r(9433),c=r(7740),f=r(2796);e.exports=function(e,t){var r,u,l,h,d,p=e.target,m=e.global,g=e.stat;if(r=m?n:g?n[p]||a(p,{}):n[p]&&n[p].prototype)for(u in t){h=t[u];l=e.dontCallGetSet?(d=i(r,u))&&d.value:r[u];if(!f(m?u:p+(g?".":"#")+u,e.forced)&&void 0!==l){if(typeof h==typeof l)continue;c(h,l)}(e.sham||l&&l.sham)&&o(h,"sham",!0);s(r,u,h,e)}}},6573:(e,t,r)=>{var n=r(3724),i=r(2106),o=r(3238),s=ArrayBuffer.prototype;n&&!("detached"in s)&&i(s,"detached",{configurable:!0,get:function detached(){return o(this)}})},6699:(e,t,r)=>{var n=r(3724),i=r(4913),o=r(6980);e.exports=n?function(e,t,r){return i.f(e,t,o(1,r))}:function(e,t,r){e[t]=r;return e}},6706:(e,t,r)=>{var n=r(9504),i=r(9306);e.exports=function(e,t,r){try{return n(i(Object.getOwnPropertyDescriptor(e,t)[r]))}catch(e){}}},6801:(e,t,r)=>{var n=r(3724),i=r(8686),o=r(4913),s=r(8551),a=r(5397),c=r(1072);t.f=n&&!i?Object.defineProperties:function defineProperties(e,t){s(e);for(var r,n=a(t),i=c(t),f=i.length,u=0;f>u;)o.f(e,r=i[u++],n[r]);return e}},6823:e=>{var t=String;e.exports=function(e){try{return t(e)}catch(e){return"Object"}}},6837:e=>{var t=TypeError;e.exports=function(e){if(e>9007199254740991)throw t("Maximum allowed index exceeded");return e}},6840:(e,t,r)=>{var n=r(4901),i=r(4913),o=r(283),s=r(9433);e.exports=function(e,t,r,a){a||(a={});var c=a.enumerable,f=void 0!==a.name?a.name:t;n(r)&&o(r,f,a);if(a.global)c?e[t]=r:s(t,r);else{try{a.unsafe?e[t]&&(c=!0):delete e[t]}catch(e){}c?e[t]=r:i.f(e,t,{value:r,enumerable:!1,configurable:!a.nonConfigurable,writable:!a.nonWritable})}return e}},6955:(e,t,r)=>{var n=r(2140),i=r(4901),o=r(2195),s=r(8227)("toStringTag"),a=Object,c="Arguments"===o(function(){return arguments}());e.exports=n?o:function(e){var t,r,n;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(r=function(e,t){try{return e[t]}catch(e){}}(t=a(e),s))?r:c?o(t):"Object"===(n=o(t))&&i(t.callee)?"Arguments":n}},6969:(e,t,r)=>{var n=r(2777),i=r(757);e.exports=function(e){var t=n(e,"string");return i(t)?t:t+""}},6980:e=>{e.exports=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}}},7040:(e,t,r)=>{var n=r(4495);e.exports=n&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},7055:(e,t,r)=>{var n=r(9504),i=r(9039),o=r(2195),s=Object,a=n("".split);e.exports=i((function(){return!s("z").propertyIsEnumerable(0)}))?function(e){return"String"===o(e)?a(e,""):s(e)}:s},7080:(e,t,r)=>{var n=r(4402).has;e.exports=function(e){n(e);return e}},7347:(e,t,r)=>{var n=r(3724),i=r(9565),o=r(8773),s=r(6980),a=r(5397),c=r(6969),f=r(9297),u=r(5917),l=Object.getOwnPropertyDescriptor;t.f=n?l:function getOwnPropertyDescriptor(e,t){e=a(e);t=c(t);if(u)try{return l(e,t)}catch(e){}if(f(e,t))return s(!i(o.f,e,t),e[t])}},7394:(e,t,r)=>{var n=r(4576),i=r(6706),o=r(2195),s=n.ArrayBuffer,a=n.TypeError;e.exports=s&&i(s.prototype,"byteLength","get")||function(e){if("ArrayBuffer"!==o(e))throw new a("ArrayBuffer expected");return e.byteLength}},7416:(e,t,r)=>{var n=r(9039),i=r(8227),o=r(3724),s=r(6395),a=i("iterator");e.exports=!n((function(){var e=new URL("b?a=1&b=2&c=3","https://a"),t=e.searchParams,r=new URLSearchParams("a=1&a=2&b=3"),n="";e.pathname="c%20d";t.forEach((function(e,r){t.delete("b");n+=r+e}));r.delete("a",2);r.delete("b",void 0);return s&&(!e.toJSON||!r.has("a",1)||r.has("a",2)||!r.has("a",void 0)||r.has("b"))||!t.size&&(s||!o)||!t.sort||"https://a/c%20d?a=1&c=3"!==e.href||"3"!==t.get("c")||"a=1"!==String(new URLSearchParams("?a=1"))||!t[a]||"a"!==new URL("https://a@b").username||"b"!==new URLSearchParams(new URLSearchParams("a=b")).get("a")||"xn--e1aybc"!==new URL("https://тест").host||"#%D0%B1"!==new URL("https://a#б").hash||"a1c3"!==n||"x"!==new URL("https://x",void 0).host}))},7476:(e,t,r)=>{var n=r(2195),i=r(9504);e.exports=function(e){if("Function"===n(e))return i(e)}},7566:(e,t,r)=>{var n=r(6840),i=r(9504),o=r(655),s=r(2812),a=URLSearchParams,c=a.prototype,f=i(c.getAll),u=i(c.has),l=new a("a=1");!l.has("a",2)&&l.has("a",void 0)||n(c,"has",(function has(e){var t=arguments.length,r=t<2?void 0:arguments[1];if(t&&void 0===r)return u(this,e);var n=f(this,e);s(t,1);for(var i=o(r),a=0;a<n.length;)if(n[a++]===i)return!0;return!1}),{enumerable:!0,unsafe:!0})},7629:(e,t,r)=>{var n=r(6395),i=r(4576),o=r(9433),s="__core-js_shared__",a=e.exports=i[s]||o(s,{});(a.versions||(a.versions=[])).push({version:"3.44.0",mode:n?"pure":"global",copyright:"© 2014-2025 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.44.0/LICENSE",source:"https://github.com/zloirock/core-js"})},7642:(e,t,r)=>{var n=r(6518),i=r(3440),o=r(9039);n({target:"Set",proto:!0,real:!0,forced:!r(4916)("difference",(function(e){return 0===e.size}))||o((function(){var e={size:1,has:function(){return!0},keys:function(){var e=0;return{next:function(){var r=e++>1;t.has(1)&&t.clear();return{done:r,value:2}}}}},t=new Set([1,2,3,4]);return 3!==t.difference(e).size}))},{difference:i})},7657:(e,t,r)=>{var n,i,o,s=r(9039),a=r(4901),c=r(34),f=r(2360),u=r(2787),l=r(6840),h=r(8227),d=r(6395),p=h("iterator"),m=!1;[].keys&&("next"in(o=[].keys())?(i=u(u(o)))!==Object.prototype&&(n=i):m=!0);!c(n)||s((function(){var e={};return n[p].call(e)!==e}))?n={}:d&&(n=f(n));a(n[p])||l(n,p,(function(){return this}));e.exports={IteratorPrototype:n,BUGGY_SAFARI_ITERATORS:m}},7680:(e,t,r)=>{var n=r(9504);e.exports=n([].slice)},7696:(e,t,r)=>{var n=r(1291),i=r(8014),o=RangeError;e.exports=function(e){if(void 0===e)return 0;var t=n(e),r=i(t);if(t!==r)throw new o("Wrong length or index");return r}},7740:(e,t,r)=>{var n=r(9297),i=r(5031),o=r(7347),s=r(4913);e.exports=function(e,t,r){for(var a=i(t),c=s.f,f=o.f,u=0;u<a.length;u++){var l=a[u];n(e,l)||r&&n(r,l)||c(e,l,f(t,l))}}},7750:(e,t,r)=>{var n=r(4117),i=TypeError;e.exports=function(e){if(n(e))throw new i("Can't call method on "+e);return e}},7751:(e,t,r)=>{var n=r(4576),i=r(4901);e.exports=function(e,t){return arguments.length<2?(r=n[e],i(r)?r:void 0):n[e]&&n[e][t];var r}},7811:e=>{e.exports="undefined"!=typeof ArrayBuffer&&"undefined"!=typeof DataView},7936:(e,t,r)=>{var n=r(6518),i=r(5636);i&&n({target:"ArrayBuffer",proto:!0},{transferToFixedLength:function transferToFixedLength(){return i(this,arguments.length?arguments[0]:void 0,!1)}})},8004:(e,t,r)=>{var n=r(6518),i=r(9039),o=r(8750);n({target:"Set",proto:!0,real:!0,forced:!r(4916)("intersection",(function(e){return 2===e.size&&e.has(1)&&e.has(2)}))||i((function(){return"3,2"!==String(Array.from(new Set([1,2,3]).intersection(new Set([3,2]))))}))},{intersection:o})},8014:(e,t,r)=>{var n=r(1291),i=Math.min;e.exports=function(e){var t=n(e);return t>0?i(t,9007199254740991):0}},8100:(e,t,r)=>{var n=r(6518),i=r(5636);i&&n({target:"ArrayBuffer",proto:!0},{transfer:function transfer(){return i(this,arguments.length?arguments[0]:void 0,!0)}})},8111:(e,t,r)=>{var n=r(6518),i=r(4576),o=r(679),s=r(8551),a=r(4901),c=r(2787),f=r(2106),u=r(4659),l=r(9039),h=r(9297),d=r(8227),p=r(7657).IteratorPrototype,m=r(3724),g=r(6395),y="constructor",b="Iterator",w=d("toStringTag"),v=TypeError,_=i[b],x=g||!a(_)||_.prototype!==p||!l((function(){_({})})),C=function Iterator(){o(this,p);if(c(this)===p)throw new v("Abstract class Iterator not directly constructable")},defineIteratorPrototypeAccessor=function(e,t){m?f(p,e,{configurable:!0,get:function(){return t},set:function(t){s(this);if(this===p)throw new v("You can't redefine this property");h(this,e)?this[e]=t:u(this,e,t)}}):p[e]=t};h(p,w)||defineIteratorPrototypeAccessor(w,b);!x&&h(p,y)&&p[y]!==Object||defineIteratorPrototypeAccessor(y,C);C.prototype=p;n({global:!0,constructor:!0,forced:x},{Iterator:C})},8227:(e,t,r)=>{var n=r(4576),i=r(5745),o=r(9297),s=r(3392),a=r(4495),c=r(7040),f=n.Symbol,u=i("wks"),l=c?f.for||f:f&&f.withoutSetter||s;e.exports=function(e){o(u,e)||(u[e]=a&&o(f,e)?f[e]:l("Symbol."+e));return u[e]}},8237:(e,t,r)=>{var n=r(6518),i=r(2652),o=r(9306),s=r(8551),a=r(1767),c=r(9539),f=r(4549),u=r(8745),l=r(9039),h=TypeError,d=l((function(){[].keys().reduce((function(){}),void 0)})),p=!d&&f("reduce",h);n({target:"Iterator",proto:!0,real:!0,forced:d||p},{reduce:function reduce(e){s(this);try{o(e)}catch(e){c(this,"throw",e)}var t=arguments.length<2,r=t?void 0:arguments[1];if(p)return u(p,this,t?[e]:[e,r]);var n=a(this),f=0;i(n,(function(n){if(t){t=!1;r=n}else r=e(r,n,f);f++}),{IS_RECORD:!0});if(t)throw new h("Reduce of empty iterator with no initial value");return r}})},8469:(e,t,r)=>{var n=r(9504),i=r(507),o=r(4402),s=o.Set,a=o.proto,c=n(a.forEach),f=n(a.keys),u=f(new s).next;e.exports=function(e,t,r){return r?i({iterator:f(e),next:u},t):c(e,t)}},8480:(e,t,r)=>{var n=r(1828),i=r(8727).concat("length","prototype");t.f=Object.getOwnPropertyNames||function getOwnPropertyNames(e){return n(e,i)}},8527:(e,t,r)=>{var n=r(7080),i=r(4402).has,o=r(5170),s=r(3789),a=r(507),c=r(9539);e.exports=function isSupersetOf(e){var t=n(this),r=s(e);if(o(t)<r.size)return!1;var f=r.getIterator();return!1!==a(f,(function(e){if(!i(t,e))return c(f,"normal",!1)}))}},8551:(e,t,r)=>{var n=r(34),i=String,o=TypeError;e.exports=function(e){if(n(e))return e;throw new o(i(e)+" is not an object")}},8574:(e,t,r)=>{var n=r(9504),i=Error,o=n("".replace),s=String(new i("zxcasd").stack),a=/\n\s*at [^:]*:[^\n]*/,c=a.test(s);e.exports=function(e,t){if(c&&"string"==typeof e&&!i.prepareStackTrace)for(;t--;)e=o(e,a,"");return e}},8622:(e,t,r)=>{var n=r(4576),i=r(4901),o=n.WeakMap;e.exports=i(o)&&/native code/.test(String(o))},8686:(e,t,r)=>{var n=r(3724),i=r(9039);e.exports=n&&i((function(){return 42!==Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype}))},8721:(e,t,r)=>{var n=r(3724),i=r(9504),o=r(2106),s=URLSearchParams.prototype,a=i(s.forEach);n&&!("size"in s)&&o(s,"size",{get:function size(){var e=0;a(this,(function(){e++}));return e},configurable:!0,enumerable:!0})},8727:e=>{e.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},8745:(e,t,r)=>{var n=r(616),i=Function.prototype,o=i.apply,s=i.call;e.exports="object"==typeof Reflect&&Reflect.apply||(n?s.bind(o):function(){return s.apply(o,arguments)})},8750:(e,t,r)=>{var n=r(7080),i=r(4402),o=r(5170),s=r(3789),a=r(8469),c=r(507),f=i.Set,u=i.add,l=i.has;e.exports=function intersection(e){var t=n(this),r=s(e),i=new f;o(t)>r.size?c(r.getIterator(),(function(e){l(t,e)&&u(i,e)})):a(t,(function(e){r.includes(e)&&u(i,e)}));return i}},8773:(e,t)=>{var r={}.propertyIsEnumerable,n=Object.getOwnPropertyDescriptor,i=n&&!r.call({1:2},1);t.f=i?function propertyIsEnumerable(e){var t=n(this,e);return!!t&&t.enumerable}:r},8981:(e,t,r)=>{var n=r(7750),i=Object;e.exports=function(e){return i(n(e))}},9039:e=>{e.exports=function(e){try{return!!e()}catch(e){return!0}}},9143:(e,t,r)=>{var n=r(4576),i=r(9504),o=r(3972),s=r(3463),a=r(9297),c=r(2804),f=r(944),u=r(5169),l=c.c2i,h=c.c2iUrl,d=n.SyntaxError,p=n.TypeError,m=i("".charAt),skipAsciiWhitespace=function(e,t){for(var r=e.length;t<r;t++){var n=m(e,t);if(" "!==n&&"\t"!==n&&"\n"!==n&&"\f"!==n&&"\r"!==n)break}return t},decodeBase64Chunk=function(e,t,r){var n=e.length;n<4&&(e+=2===n?"AA":"A");var i=(t[m(e,0)]<<18)+(t[m(e,1)]<<12)+(t[m(e,2)]<<6)+t[m(e,3)],o=[i>>16&255,i>>8&255,255&i];if(2===n){if(r&&0!==o[1])throw new d("Extra bits");return[o[0]]}if(3===n){if(r&&0!==o[2])throw new d("Extra bits");return[o[0],o[1]]}return o},writeBytes=function(e,t,r){for(var n=t.length,i=0;i<n;i++)e[r+i]=t[i];return r+n};e.exports=function(e,t,r,n){s(e);o(t);var i="base64"===f(t)?l:h,c=t?t.lastChunkHandling:void 0;void 0===c&&(c="loose");if("loose"!==c&&"strict"!==c&&"stop-before-partial"!==c)throw new p("Incorrect `lastChunkHandling` option");r&&u(r.buffer);var g=r||[],y=0,b=0,w="",v=0;if(n)for(;;){if((v=skipAsciiWhitespace(e,v))===e.length){if(w.length>0){if("stop-before-partial"===c)break;if("loose"!==c)throw new d("Missing padding");if(1===w.length)throw new d("Malformed padding: exactly one additional character");y=writeBytes(g,decodeBase64Chunk(w,i,!1),y)}b=e.length;break}var _=m(e,v);++v;if("="===_){if(w.length<2)throw new d("Padding is too early");v=skipAsciiWhitespace(e,v);if(2===w.length){if(v===e.length){if("stop-before-partial"===c)break;throw new d("Malformed padding: only one =")}if("="===m(e,v)){++v;v=skipAsciiWhitespace(e,v)}}if(v<e.length)throw new d("Unexpected character after padding");y=writeBytes(g,decodeBase64Chunk(w,i,"strict"===c),y);b=e.length;break}if(!a(i,_))throw new d("Unexpected character");var x=n-y;if(1===x&&2===w.length||2===x&&3===w.length)break;if(4===(w+=_).length){y=writeBytes(g,decodeBase64Chunk(w,i,!1),y);w="";b=v;if(y===n)break}}return{bytes:g,read:b,written:y}}},9286:(e,t,r)=>{var n=r(4402),i=r(8469),o=n.Set,s=n.add;e.exports=function(e){var t=new o;i(e,(function(e){s(t,e)}));return t}},9297:(e,t,r)=>{var n=r(9504),i=r(8981),o=n({}.hasOwnProperty);e.exports=Object.hasOwn||function hasOwn(e,t){return o(i(e),t)}},9306:(e,t,r)=>{var n=r(4901),i=r(6823),o=TypeError;e.exports=function(e){if(n(e))return e;throw new o(i(e)+" is not a function")}},9429:(e,t,r)=>{var n=r(4576),i=r(6193);e.exports=function(e){if(i){try{return n.process.getBuiltinModule(e)}catch(e){}try{return Function('return require("'+e+'")')()}catch(e){}}}},9432:(e,t,r)=>{var n=r(6518),i=r(4576),o=r(5370),s=r(9143),a=i.Uint8Array,c=!a||!a.fromBase64||!function(){try{a.fromBase64("",null)}catch(e){return!0}}();a&&n({target:"Uint8Array",stat:!0,forced:c},{fromBase64:function fromBase64(e){var t=s(e,arguments.length>1?arguments[1]:void 0,null,9007199254740991);return o(a,t.bytes)}})},9433:(e,t,r)=>{var n=r(4576),i=Object.defineProperty;e.exports=function(e,t){try{i(n,e,{value:t,configurable:!0,writable:!0})}catch(r){n[e]=t}return t}},9462:(e,t,r)=>{var n=r(9565),i=r(2360),o=r(6699),s=r(6279),a=r(8227),c=r(1181),f=r(5966),u=r(7657).IteratorPrototype,l=r(2529),h=r(9539),d=r(1385),p=a("toStringTag"),m="IteratorHelper",g="WrapForValidIterator",y="normal",b="throw",w=c.set,createIteratorProxyPrototype=function(e){var t=c.getterFor(e?g:m);return s(i(u),{next:function next(){var r=t(this);if(e)return r.nextHandler();if(r.done)return l(void 0,!0);try{var n=r.nextHandler();return r.returnHandlerResult?n:l(n,r.done)}catch(e){r.done=!0;throw e}},return:function(){var r=t(this),i=r.iterator;r.done=!0;if(e){var o=f(i,"return");return o?n(o,i):l(void 0,!0)}if(r.inner)try{h(r.inner.iterator,y)}catch(e){return h(i,b,e)}if(r.openIters)try{d(r.openIters,y)}catch(e){return h(i,b,e)}i&&h(i,y);return l(void 0,!0)}})},v=createIteratorProxyPrototype(!0),_=createIteratorProxyPrototype(!1);o(_,p,"Iterator Helper");e.exports=function(e,t,r){var n=function Iterator(n,i){if(i){i.iterator=n.iterator;i.next=n.next}else i=n;i.type=t?g:m;i.returnHandlerResult=!!r;i.nextHandler=e;i.counter=0;i.done=!1;w(this,i)};n.prototype=t?v:_;return n}},9504:(e,t,r)=>{var n=r(616),i=Function.prototype,o=i.call,s=n&&i.bind.bind(o,o);e.exports=n?s:function(e){return function(){return o.apply(e,arguments)}}},9519:(e,t,r)=>{var n,i,o=r(4576),s=r(2839),a=o.process,c=o.Deno,f=a&&a.versions||c&&c.version,u=f&&f.v8;u&&(i=(n=u.split("."))[0]>0&&n[0]<4?1:+(n[0]+n[1]));!i&&s&&(!(n=s.match(/Edge\/(\d+)/))||n[1]>=74)&&(n=s.match(/Chrome\/(\d+)/))&&(i=+n[1]);e.exports=i},9539:(e,t,r)=>{var n=r(9565),i=r(8551),o=r(5966);e.exports=function(e,t,r){var s,a;i(e);try{if(!(s=o(e,"return"))){if("throw"===t)throw r;return r}s=n(s,e)}catch(e){a=!0;s=e}if("throw"===t)throw r;if(a)throw s;i(s);return r}},9565:(e,t,r)=>{var n=r(616),i=Function.prototype.call;e.exports=n?i.bind(i):function(){return i.apply(i,arguments)}},9577:(e,t,r)=>{var n=r(9928),i=r(4644),o=r(1108),s=r(1291),a=r(5854),c=i.aTypedArray,f=i.getTypedArrayConstructor,u=i.exportTypedArrayMethod,l=function(){try{new Int8Array(1).with(2,{valueOf:function(){throw 8}})}catch(e){return 8===e}}(),h=l&&function(){try{new Int8Array(1).with(-.5,1)}catch(e){return!0}}();u("with",{with:function(e,t){var r=c(this),i=s(e),u=o(r)?a(t):+t;return n(r,f(r),i,u)}}.with,!l||h)},9617:(e,t,r)=>{var n=r(5397),i=r(5610),o=r(6198),createMethod=function(e){return function(t,r,s){var a=n(t),c=o(a);if(0===c)return!e&&-1;var f,u=i(s,c);if(e&&r!=r){for(;c>u;)if((f=a[u++])!=f)return!0}else for(;c>u;u++)if((e||u in a)&&a[u]===r)return e||u||0;return!e&&-1}};e.exports={includes:createMethod(!0),indexOf:createMethod(!1)}},9631:(e,t,r)=>{var n=r(6518),i=r(4576),o=r(9504),s=r(3972),a=r(4154),c=r(5169),f=r(2804),u=r(944),l=f.i2c,h=f.i2cUrl,d=o("".charAt),p=i.Uint8Array,m=!p||!p.prototype.toBase64||!function(){try{(new p).toBase64(null)}catch(e){return!0}}();p&&n({target:"Uint8Array",proto:!0,forced:m},{toBase64:function toBase64(){var e=a(this),t=arguments.length?s(arguments[0]):void 0,r="base64"===u(t)?l:h,n=!!t&&!!t.omitPadding;c(this.buffer);for(var i,o="",f=0,p=e.length,at=function(e){return d(r,i>>6*e&63)};f+2<p;f+=3){i=(e[f]<<16)+(e[f+1]<<8)+e[f+2];o+=at(3)+at(2)+at(1)+at(0)}if(f+2===p){i=(e[f]<<16)+(e[f+1]<<8);o+=at(3)+at(2)+at(1)+(n?"":"=")}else if(f+1===p){i=e[f]<<16;o+=at(3)+at(2)+(n?"":"==")}return o}})},9797:(e,t,r)=>{var n=r(6518),i=r(4576),o=r(3463),s=r(4154),a=r(5169),c=r(2303);i.Uint8Array&&n({target:"Uint8Array",proto:!0},{setFromHex:function setFromHex(e){s(this);o(e);a(this.buffer);var t=c(e,this).read;return{read:t,written:t/2}}})},9835:e=>{e.exports=function(e){try{var t=new Set,r={size:0,has:function(){return!0},keys:function(){return Object.defineProperty({},"next",{get:function(){t.clear();t.add(4);return function(){return{done:!0}}}})}},n=t[e](r);return 1===n.size&&4===n.values().next().value}catch(e){return!1}}},9928:(e,t,r)=>{var n=r(6198),i=r(1291),o=RangeError;e.exports=function(e,t,r,s){var a=n(e),c=i(r),f=c<0?a+c:c;if(f>=a||f<0)throw new o("Incorrect index");for(var u=new t(a),l=0;l<a;l++)u[l]=l===f?s:e[l];return u}}},t={};function __webpack_require__(r){var n=t[r];if(void 0!==n)return n.exports;var i=t[r]={exports:{}};e[r].call(i.exports,i,i.exports,__webpack_require__);return i.exports}__webpack_require__(4114),__webpack_require__(6573),__webpack_require__(8100),__webpack_require__(7936),__webpack_require__(8111),__webpack_require__(8237),__webpack_require__(1689),__webpack_require__(9577),__webpack_require__(4235),__webpack_require__(9432),__webpack_require__(1549),__webpack_require__(9797),__webpack_require__(9631),__webpack_require__(5623),__webpack_require__(4979),__webpack_require__(5781);"object"!=typeof process||process+""!="[object process]"||process.versions.nw||process.versions.electron&&process.type&&process.type;const r={ERRORS:0,WARNINGS:1,INFOS:5};let n=r.WARNINGS;function setVerbosityLevel(e){Number.isInteger(e)&&(n=e)}function getVerbosityLevel(){return n}function info(e){n>=r.INFOS&&console.log(`Info: ${e}`)}function util_warn(e){n>=r.WARNINGS&&console.log(`Warning: ${e}`)}function unreachable(e){throw new Error(e)}function shadow(e,t,r,n=!1){Object.defineProperty(e,t,{value:r,enumerable:!n,configurable:!0,writable:!1});return r}const i=function BaseExceptionClosure(){function BaseException(e,t){this.message=e;this.name=t}BaseException.prototype=new Error;BaseException.constructor=BaseException;return BaseException}();class FormatError extends i{constructor(e){super(e,"FormatError")}}function bytesToString(e){"object"==typeof e&&void 0!==e?.length||unreachable("Invalid argument for bytesToString");const t=e.length,r=8192;if(t<r)return String.fromCharCode.apply(null,e);const n=[];for(let i=0;i<t;i+=r){const o=Math.min(i+r,t),s=e.subarray(i,o);n.push(String.fromCharCode.apply(null,s))}return n.join("")}class util_FeatureTest{static get isLittleEndian(){return shadow(this,"isLittleEndian",function isLittleEndian(){const e=new Uint8Array(4);e[0]=1;return 1===new Uint32Array(e.buffer,0,1)[0]}())}static get isEvalSupported(){return shadow(this,"isEvalSupported",function isEvalSupported(){try{new Function("");return!0}catch{return!1}}())}static get isOffscreenCanvasSupported(){return shadow(this,"isOffscreenCanvasSupported","undefined"!=typeof OffscreenCanvas)}static get isImageDecoderSupported(){return shadow(this,"isImageDecoderSupported","undefined"!=typeof ImageDecoder)}static get platform(){const{platform:e,userAgent:t}=navigator;return shadow(this,"platform",{isAndroid:t.includes("Android"),isLinux:e.includes("Linux"),isMac:e.includes("Mac"),isWindows:e.includes("Win"),isFirefox:t.includes("Firefox")})}static get isCSSRoundSupported(){return shadow(this,"isCSSRoundSupported",globalThis.CSS?.supports?.("width: round(1.5px, 1px)"))}}const o=Array.from(Array(256).keys(),(e=>e.toString(16).padStart(2,"0")));class util_Util{static makeHexColor(e,t,r){return`#${o[e]}${o[t]}${o[r]}`}static scaleMinMax(e,t){let r;if(e[0]){if(e[0]<0){r=t[0];t[0]=t[2];t[2]=r}t[0]*=e[0];t[2]*=e[0];if(e[3]<0){r=t[1];t[1]=t[3];t[3]=r}t[1]*=e[3];t[3]*=e[3]}else{r=t[0];t[0]=t[1];t[1]=r;r=t[2];t[2]=t[3];t[3]=r;if(e[1]<0){r=t[1];t[1]=t[3];t[3]=r}t[1]*=e[1];t[3]*=e[1];if(e[2]<0){r=t[0];t[0]=t[2];t[2]=r}t[0]*=e[2];t[2]*=e[2]}t[0]+=e[4];t[1]+=e[5];t[2]+=e[4];t[3]+=e[5]}static transform(e,t){return[e[0]*t[0]+e[2]*t[1],e[1]*t[0]+e[3]*t[1],e[0]*t[2]+e[2]*t[3],e[1]*t[2]+e[3]*t[3],e[0]*t[4]+e[2]*t[5]+e[4],e[1]*t[4]+e[3]*t[5]+e[5]]}static applyTransform(e,t,r=0){const n=e[r],i=e[r+1];e[r]=n*t[0]+i*t[2]+t[4];e[r+1]=n*t[1]+i*t[3]+t[5]}static applyTransformToBezier(e,t,r=0){const n=t[0],i=t[1],o=t[2],s=t[3],a=t[4],c=t[5];for(let t=0;t<6;t+=2){const f=e[r+t],u=e[r+t+1];e[r+t]=f*n+u*o+a;e[r+t+1]=f*i+u*s+c}}static applyInverseTransform(e,t){const r=e[0],n=e[1],i=t[0]*t[3]-t[1]*t[2];e[0]=(r*t[3]-n*t[2]+t[2]*t[5]-t[4]*t[3])/i;e[1]=(-r*t[1]+n*t[0]+t[4]*t[1]-t[5]*t[0])/i}static axialAlignedBoundingBox(e,t,r){const n=t[0],i=t[1],o=t[2],s=t[3],a=t[4],c=t[5],f=e[0],u=e[1],l=e[2],h=e[3];let d=n*f+a,p=d,m=n*l+a,g=m,y=s*u+c,b=y,w=s*h+c,v=w;if(0!==i||0!==o){const e=i*f,t=i*l,r=o*u,n=o*h;d+=r;g+=r;m+=n;p+=n;y+=e;v+=e;w+=t;b+=t}r[0]=Math.min(r[0],d,m,p,g);r[1]=Math.min(r[1],y,w,b,v);r[2]=Math.max(r[2],d,m,p,g);r[3]=Math.max(r[3],y,w,b,v)}static inverseTransform(e){const t=e[0]*e[3]-e[1]*e[2];return[e[3]/t,-e[1]/t,-e[2]/t,e[0]/t,(e[2]*e[5]-e[4]*e[3])/t,(e[4]*e[1]-e[5]*e[0])/t]}static singularValueDecompose2dScale(e,t){const r=e[0],n=e[1],i=e[2],o=e[3],s=r**2+n**2,a=r*i+n*o,c=i**2+o**2,f=(s+c)/2,u=Math.sqrt(f**2-(s*c-a**2));t[0]=Math.sqrt(f+u||1);t[1]=Math.sqrt(f-u||1)}static normalizeRect(e){const t=e.slice(0);if(e[0]>e[2]){t[0]=e[2];t[2]=e[0]}if(e[1]>e[3]){t[1]=e[3];t[3]=e[1]}return t}static intersect(e,t){const r=Math.max(Math.min(e[0],e[2]),Math.min(t[0],t[2])),n=Math.min(Math.max(e[0],e[2]),Math.max(t[0],t[2]));if(r>n)return null;const i=Math.max(Math.min(e[1],e[3]),Math.min(t[1],t[3])),o=Math.min(Math.max(e[1],e[3]),Math.max(t[1],t[3]));return i>o?null:[r,i,n,o]}static pointBoundingBox(e,t,r){r[0]=Math.min(r[0],e);r[1]=Math.min(r[1],t);r[2]=Math.max(r[2],e);r[3]=Math.max(r[3],t)}static rectBoundingBox(e,t,r,n,i){i[0]=Math.min(i[0],e,r);i[1]=Math.min(i[1],t,n);i[2]=Math.max(i[2],e,r);i[3]=Math.max(i[3],t,n)}static#e(e,t,r,n,i,o,s,a,c,f){if(c<=0||c>=1)return;const u=1-c,l=c*c,h=l*c,d=u*(u*(u*e+3*c*t)+3*l*r)+h*n,p=u*(u*(u*i+3*c*o)+3*l*s)+h*a;f[0]=Math.min(f[0],d);f[1]=Math.min(f[1],p);f[2]=Math.max(f[2],d);f[3]=Math.max(f[3],p)}static#t(e,t,r,n,i,o,s,a,c,f,u,l){if(Math.abs(c)<1e-12){Math.abs(f)>=1e-12&&this.#e(e,t,r,n,i,o,s,a,-u/f,l);return}const h=f**2-4*u*c;if(h<0)return;const d=Math.sqrt(h),p=2*c;this.#e(e,t,r,n,i,o,s,a,(-f+d)/p,l);this.#e(e,t,r,n,i,o,s,a,(-f-d)/p,l)}static bezierBoundingBox(e,t,r,n,i,o,s,a,c){c[0]=Math.min(c[0],e,s);c[1]=Math.min(c[1],t,a);c[2]=Math.max(c[2],e,s);c[3]=Math.max(c[3],t,a);this.#t(e,r,i,s,t,n,o,a,3*(3*(r-i)-e+s),6*(e-2*r+i),3*(r-e),c);this.#t(e,r,i,s,t,n,o,a,3*(3*(n-o)-t+a),6*(t-2*n+o),3*(n-t),c)}}function MathClamp(e,t,r){return Math.min(Math.max(e,t),r)}"function"!=typeof Math.sumPrecise&&(Math.sumPrecise=function(e){return e.reduce(((e,t)=>e+t),0)});"function"!=typeof AbortSignal.any&&(AbortSignal.any=function(e){const t=new AbortController,{signal:r}=t;for(const n of e)if(n.aborted){t.abort(n.reason);return r}for(const n of e)n.addEventListener("abort",(()=>{t.abort(n.reason)}),{signal:r});return r});__webpack_require__(1148),__webpack_require__(1701),__webpack_require__(7642),__webpack_require__(8004),__webpack_require__(3853),__webpack_require__(5876),__webpack_require__(2475),__webpack_require__(5024),__webpack_require__(1698);Symbol("CIRCULAR_REF"),Symbol("EOF");Object.create(null);let s=Object.create(null),a=Object.create(null);class Name{constructor(e){this.name=e}static get(e){return s[e]||=new Name(e)}}const c=function nonSerializableClosure(){return c};class primitives_Dict{constructor(e=null){this._map=new Map;this.xref=e;this.objId=null;this.suppressEncryption=!1;this.__nonSerializable__=c}assignXref(e){this.xref=e}get size(){return this._map.size}get(e,t,r){let n=this._map.get(e);if(void 0===n&&void 0!==t){n=this._map.get(t);void 0===n&&void 0!==r&&(n=this._map.get(r))}return n instanceof primitives_Ref&&this.xref?this.xref.fetch(n,this.suppressEncryption):n}async getAsync(e,t,r){let n=this._map.get(e);if(void 0===n&&void 0!==t){n=this._map.get(t);void 0===n&&void 0!==r&&(n=this._map.get(r))}return n instanceof primitives_Ref&&this.xref?this.xref.fetchAsync(n,this.suppressEncryption):n}getArray(e,t,r){let n=this._map.get(e);if(void 0===n&&void 0!==t){n=this._map.get(t);void 0===n&&void 0!==r&&(n=this._map.get(r))}n instanceof primitives_Ref&&this.xref&&(n=this.xref.fetch(n,this.suppressEncryption));if(Array.isArray(n)){n=n.slice();for(let e=0,t=n.length;e<t;e++)n[e]instanceof primitives_Ref&&this.xref&&(n[e]=this.xref.fetch(n[e],this.suppressEncryption))}return n}getRaw(e){return this._map.get(e)}getKeys(){return[...this._map.keys()]}getRawValues(){return[...this._map.values()]}set(e,t){this._map.set(e,t)}setIfNotExists(e,t){this.has(e)||this.set(e,t)}setIfNumber(e,t){"number"==typeof t&&this.set(e,t)}setIfArray(e,t){(Array.isArray(t)||ArrayBuffer.isView(t))&&this.set(e,t)}setIfDefined(e,t){null!=t&&this.set(e,t)}setIfName(e,t){"string"==typeof t?this.set(e,Name.get(t)):t instanceof Name&&this.set(e,t)}has(e){return this._map.has(e)}*[Symbol.iterator](){for(const[e,t]of this._map)yield[e,t instanceof primitives_Ref&&this.xref?this.xref.fetch(t,this.suppressEncryption):t]}static get empty(){const e=new primitives_Dict(null);e.set=(e,t)=>{unreachable("Should not call `set` on the empty dictionary.")};return shadow(this,"empty",e)}static merge({xref:e,dictArray:t,mergeSubDicts:r=!1}){const n=new primitives_Dict(e),i=new Map;for(const e of t)if(e instanceof primitives_Dict)for(const[t,n]of e._map){let e=i.get(t);if(void 0===e){e=[];i.set(t,e)}else if(!(r&&n instanceof primitives_Dict))continue;e.push(n)}for(const[t,r]of i){if(1===r.length||!(r[0]instanceof primitives_Dict)){n._map.set(t,r[0]);continue}const i=new primitives_Dict(e);for(const e of r)for(const[t,r]of e._map)i._map.has(t)||i._map.set(t,r);i.size>0&&n._map.set(t,i)}i.clear();return n.size>0?n:primitives_Dict.empty}clone(){const e=new primitives_Dict(this.xref);for(const t of this.getKeys())e.set(t,this.getRaw(t));return e}delete(e){delete this._map[e]}}class primitives_Ref{constructor(e,t){this.num=e;this.gen=t}toString(){return 0===this.gen?`${this.num}R`:`${this.num}R${this.gen}`}static fromString(e){const t=a[e];if(t)return t;const r=/^(\d+)R(\d*)$/.exec(e);return r&&"0"!==r[1]?a[e]=new primitives_Ref(parseInt(r[1]),r[2]?parseInt(r[2]):0):null}static get(e,t){const r=0===t?`${e}R`:`${e}R${t}`;return a[r]||=new primitives_Ref(e,t)}}Symbol.iterator;Symbol.iterator;class base_stream_BaseStream{get length(){unreachable("Abstract getter `length` accessed")}get isEmpty(){unreachable("Abstract getter `isEmpty` accessed")}get isDataLoaded(){return shadow(this,"isDataLoaded",!0)}getByte(){unreachable("Abstract method `getByte` called")}getBytes(e){unreachable("Abstract method `getBytes` called")}async getImageData(e,t){return this.getBytes(e,t)}async asyncGetBytes(){unreachable("Abstract method `asyncGetBytes` called")}get isAsync(){return!1}get isAsyncDecoder(){return!1}get canAsyncDecodeImageFromBuffer(){return!1}async getTransferableImage(){return null}peekByte(){const e=this.getByte();-1!==e&&this.pos--;return e}peekBytes(e){const t=this.getBytes(e);this.pos-=t.length;return t}getUint16(){const e=this.getByte(),t=this.getByte();return-1===e||-1===t?-1:(e<<8)+t}getInt32(){return(this.getByte()<<24)+(this.getByte()<<16)+(this.getByte()<<8)+this.getByte()}getByteRange(e,t){unreachable("Abstract method `getByteRange` called")}getString(e){return bytesToString(this.getBytes(e))}skip(e){this.pos+=e||1}reset(){unreachable("Abstract method `reset` called")}moveStart(){unreachable("Abstract method `moveStart` called")}makeSubStream(e,t,r=null){unreachable("Abstract method `makeSubStream` called")}getBaseStreams(){return null}}class MissingDataException extends i{constructor(e,t){super(`Missing data [${e}, ${t})`,"MissingDataException");this.begin=e;this.end=t}}function log2(e){return e>0?Math.ceil(Math.log2(e)):0}function readInt8(e,t){return e[t]<<24>>24}function readUint16(e,t){return e[t]<<8|e[t+1]}function readUint32(e,t){return(e[t]<<24|e[t+1]<<16|e[t+2]<<8|e[t+3])>>>0}const f=[{qe:22017,nmps:1,nlps:1,switchFlag:1},{qe:13313,nmps:2,nlps:6,switchFlag:0},{qe:6145,nmps:3,nlps:9,switchFlag:0},{qe:2753,nmps:4,nlps:12,switchFlag:0},{qe:1313,nmps:5,nlps:29,switchFlag:0},{qe:545,nmps:38,nlps:33,switchFlag:0},{qe:22017,nmps:7,nlps:6,switchFlag:1},{qe:21505,nmps:8,nlps:14,switchFlag:0},{qe:18433,nmps:9,nlps:14,switchFlag:0},{qe:14337,nmps:10,nlps:14,switchFlag:0},{qe:12289,nmps:11,nlps:17,switchFlag:0},{qe:9217,nmps:12,nlps:18,switchFlag:0},{qe:7169,nmps:13,nlps:20,switchFlag:0},{qe:5633,nmps:29,nlps:21,switchFlag:0},{qe:22017,nmps:15,nlps:14,switchFlag:1},{qe:21505,nmps:16,nlps:14,switchFlag:0},{qe:20737,nmps:17,nlps:15,switchFlag:0},{qe:18433,nmps:18,nlps:16,switchFlag:0},{qe:14337,nmps:19,nlps:17,switchFlag:0},{qe:13313,nmps:20,nlps:18,switchFlag:0},{qe:12289,nmps:21,nlps:19,switchFlag:0},{qe:10241,nmps:22,nlps:19,switchFlag:0},{qe:9217,nmps:23,nlps:20,switchFlag:0},{qe:8705,nmps:24,nlps:21,switchFlag:0},{qe:7169,nmps:25,nlps:22,switchFlag:0},{qe:6145,nmps:26,nlps:23,switchFlag:0},{qe:5633,nmps:27,nlps:24,switchFlag:0},{qe:5121,nmps:28,nlps:25,switchFlag:0},{qe:4609,nmps:29,nlps:26,switchFlag:0},{qe:4353,nmps:30,nlps:27,switchFlag:0},{qe:2753,nmps:31,nlps:28,switchFlag:0},{qe:2497,nmps:32,nlps:29,switchFlag:0},{qe:2209,nmps:33,nlps:30,switchFlag:0},{qe:1313,nmps:34,nlps:31,switchFlag:0},{qe:1089,nmps:35,nlps:32,switchFlag:0},{qe:673,nmps:36,nlps:33,switchFlag:0},{qe:545,nmps:37,nlps:34,switchFlag:0},{qe:321,nmps:38,nlps:35,switchFlag:0},{qe:273,nmps:39,nlps:36,switchFlag:0},{qe:133,nmps:40,nlps:37,switchFlag:0},{qe:73,nmps:41,nlps:38,switchFlag:0},{qe:37,nmps:42,nlps:39,switchFlag:0},{qe:21,nmps:43,nlps:40,switchFlag:0},{qe:9,nmps:44,nlps:41,switchFlag:0},{qe:5,nmps:45,nlps:42,switchFlag:0},{qe:1,nmps:45,nlps:43,switchFlag:0},{qe:22017,nmps:46,nlps:46,switchFlag:0}];class ArithmeticDecoder{constructor(e,t,r){this.data=e;this.bp=t;this.dataEnd=r;this.chigh=e[t];this.clow=0;this.byteIn();this.chigh=this.chigh<<7&65535|this.clow>>9&127;this.clow=this.clow<<7&65535;this.ct-=7;this.a=32768}byteIn(){const e=this.data;let t=this.bp;if(255===e[t])if(e[t+1]>143){this.clow+=65280;this.ct=8}else{t++;this.clow+=e[t]<<9;this.ct=7;this.bp=t}else{t++;this.clow+=t<this.dataEnd?e[t]<<8:65280;this.ct=8;this.bp=t}if(this.clow>65535){this.chigh+=this.clow>>16;this.clow&=65535}}readBit(e,t){let r=e[t]>>1,n=1&e[t];const i=f[r],o=i.qe;let s,a=this.a-o;if(this.chigh<o)if(a<o){a=o;s=n;r=i.nmps}else{a=o;s=1^n;1===i.switchFlag&&(n=s);r=i.nlps}else{this.chigh-=o;if(32768&a){this.a=a;return n}if(a<o){s=1^n;1===i.switchFlag&&(n=s);r=i.nlps}else{s=n;r=i.nmps}}do{0===this.ct&&this.byteIn();a<<=1;this.chigh=this.chigh<<1&65535|this.clow>>15&1;this.clow=this.clow<<1&65535;this.ct--}while(!(32768&a));this.a=a;e[t]=r<<1|n;return s}}const u=-1,l=[[-1,-1],[-1,-1],[7,8],[7,7],[6,6],[6,6],[6,5],[6,5],[4,0],[4,0],[4,0],[4,0],[4,0],[4,0],[4,0],[4,0],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,4],[3,4],[3,4],[3,4],[3,4],[3,4],[3,4],[3,4],[3,4],[3,4],[3,4],[3,4],[3,4],[3,4],[3,4],[3,4],[3,3],[3,3],[3,3],[3,3],[3,3],[3,3],[3,3],[3,3],[3,3],[3,3],[3,3],[3,3],[3,3],[3,3],[3,3],[3,3],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2]],h=[[-1,-1],[12,-2],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[11,1792],[11,1792],[12,1984],[12,2048],[12,2112],[12,2176],[12,2240],[12,2304],[11,1856],[11,1856],[11,1920],[11,1920],[12,2368],[12,2432],[12,2496],[12,2560]],d=[[-1,-1],[-1,-1],[-1,-1],[-1,-1],[8,29],[8,29],[8,30],[8,30],[8,45],[8,45],[8,46],[8,46],[7,22],[7,22],[7,22],[7,22],[7,23],[7,23],[7,23],[7,23],[8,47],[8,47],[8,48],[8,48],[6,13],[6,13],[6,13],[6,13],[6,13],[6,13],[6,13],[6,13],[7,20],[7,20],[7,20],[7,20],[8,33],[8,33],[8,34],[8,34],[8,35],[8,35],[8,36],[8,36],[8,37],[8,37],[8,38],[8,38],[7,19],[7,19],[7,19],[7,19],[8,31],[8,31],[8,32],[8,32],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,12],[6,12],[6,12],[6,12],[6,12],[6,12],[6,12],[6,12],[8,53],[8,53],[8,54],[8,54],[7,26],[7,26],[7,26],[7,26],[8,39],[8,39],[8,40],[8,40],[8,41],[8,41],[8,42],[8,42],[8,43],[8,43],[8,44],[8,44],[7,21],[7,21],[7,21],[7,21],[7,28],[7,28],[7,28],[7,28],[8,61],[8,61],[8,62],[8,62],[8,63],[8,63],[8,0],[8,0],[8,320],[8,320],[8,384],[8,384],[5,10],[5,10],[5,10],[5,10],[5,10],[5,10],[5,10],[5,10],[5,10],[5,10],[5,10],[5,10],[5,10],[5,10],[5,10],[5,10],[5,11],[5,11],[5,11],[5,11],[5,11],[5,11],[5,11],[5,11],[5,11],[5,11],[5,11],[5,11],[5,11],[5,11],[5,11],[5,11],[7,27],[7,27],[7,27],[7,27],[8,59],[8,59],[8,60],[8,60],[9,1472],[9,1536],[9,1600],[9,1728],[7,18],[7,18],[7,18],[7,18],[7,24],[7,24],[7,24],[7,24],[8,49],[8,49],[8,50],[8,50],[8,51],[8,51],[8,52],[8,52],[7,25],[7,25],[7,25],[7,25],[8,55],[8,55],[8,56],[8,56],[8,57],[8,57],[8,58],[8,58],[6,192],[6,192],[6,192],[6,192],[6,192],[6,192],[6,192],[6,192],[6,1664],[6,1664],[6,1664],[6,1664],[6,1664],[6,1664],[6,1664],[6,1664],[8,448],[8,448],[8,512],[8,512],[9,704],[9,768],[8,640],[8,640],[8,576],[8,576],[9,832],[9,896],[9,960],[9,1024],[9,1088],[9,1152],[9,1216],[9,1280],[9,1344],[9,1408],[7,256],[7,256],[7,256],[7,256],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[5,128],[5,128],[5,128],[5,128],[5,128],[5,128],[5,128],[5,128],[5,128],[5,128],[5,128],[5,128],[5,128],[5,128],[5,128],[5,128],[5,8],[5,8],[5,8],[5,8],[5,8],[5,8],[5,8],[5,8],[5,8],[5,8],[5,8],[5,8],[5,8],[5,8],[5,8],[5,8],[5,9],[5,9],[5,9],[5,9],[5,9],[5,9],[5,9],[5,9],[5,9],[5,9],[5,9],[5,9],[5,9],[5,9],[5,9],[5,9],[6,16],[6,16],[6,16],[6,16],[6,16],[6,16],[6,16],[6,16],[6,17],[6,17],[6,17],[6,17],[6,17],[6,17],[6,17],[6,17],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[6,14],[6,14],[6,14],[6,14],[6,14],[6,14],[6,14],[6,14],[6,15],[6,15],[6,15],[6,15],[6,15],[6,15],[6,15],[6,15],[5,64],[5,64],[5,64],[5,64],[5,64],[5,64],[5,64],[5,64],[5,64],[5,64],[5,64],[5,64],[5,64],[5,64],[5,64],[5,64],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7]],p=[[-1,-1],[-1,-1],[12,-2],[12,-2],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[11,1792],[11,1792],[11,1792],[11,1792],[12,1984],[12,1984],[12,2048],[12,2048],[12,2112],[12,2112],[12,2176],[12,2176],[12,2240],[12,2240],[12,2304],[12,2304],[11,1856],[11,1856],[11,1856],[11,1856],[11,1920],[11,1920],[11,1920],[11,1920],[12,2368],[12,2368],[12,2432],[12,2432],[12,2496],[12,2496],[12,2560],[12,2560],[10,18],[10,18],[10,18],[10,18],[10,18],[10,18],[10,18],[10,18],[12,52],[12,52],[13,640],[13,704],[13,768],[13,832],[12,55],[12,55],[12,56],[12,56],[13,1280],[13,1344],[13,1408],[13,1472],[12,59],[12,59],[12,60],[12,60],[13,1536],[13,1600],[11,24],[11,24],[11,24],[11,24],[11,25],[11,25],[11,25],[11,25],[13,1664],[13,1728],[12,320],[12,320],[12,384],[12,384],[12,448],[12,448],[13,512],[13,576],[12,53],[12,53],[12,54],[12,54],[13,896],[13,960],[13,1024],[13,1088],[13,1152],[13,1216],[10,64],[10,64],[10,64],[10,64],[10,64],[10,64],[10,64],[10,64]],m=[[8,13],[8,13],[8,13],[8,13],[8,13],[8,13],[8,13],[8,13],[8,13],[8,13],[8,13],[8,13],[8,13],[8,13],[8,13],[8,13],[11,23],[11,23],[12,50],[12,51],[12,44],[12,45],[12,46],[12,47],[12,57],[12,58],[12,61],[12,256],[10,16],[10,16],[10,16],[10,16],[10,17],[10,17],[10,17],[10,17],[12,48],[12,49],[12,62],[12,63],[12,30],[12,31],[12,32],[12,33],[12,40],[12,41],[11,22],[11,22],[8,14],[8,14],[8,14],[8,14],[8,14],[8,14],[8,14],[8,14],[8,14],[8,14],[8,14],[8,14],[8,14],[8,14],[8,14],[8,14],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[9,15],[9,15],[9,15],[9,15],[9,15],[9,15],[9,15],[9,15],[12,128],[12,192],[12,26],[12,27],[12,28],[12,29],[11,19],[11,19],[11,20],[11,20],[12,34],[12,35],[12,36],[12,37],[12,38],[12,39],[11,21],[11,21],[12,42],[12,43],[10,0],[10,0],[10,0],[10,0],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12]],g=[[-1,-1],[-1,-1],[-1,-1],[-1,-1],[6,9],[6,8],[5,7],[5,7],[4,6],[4,6],[4,6],[4,6],[4,5],[4,5],[4,5],[4,5],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,4],[3,4],[3,4],[3,4],[3,4],[3,4],[3,4],[3,4],[2,3],[2,3],[2,3],[2,3],[2,3],[2,3],[2,3],[2,3],[2,3],[2,3],[2,3],[2,3],[2,3],[2,3],[2,3],[2,3],[2,2],[2,2],[2,2],[2,2],[2,2],[2,2],[2,2],[2,2],[2,2],[2,2],[2,2],[2,2],[2,2],[2,2],[2,2],[2,2]];class CCITTFaxDecoder{constructor(e,t={}){if("function"!=typeof e?.next)throw new Error('CCITTFaxDecoder - invalid "source" parameter.');this.source=e;this.eof=!1;this.encoding=t.K||0;this.eoline=t.EndOfLine||!1;this.byteAlign=t.EncodedByteAlign||!1;this.columns=t.Columns||1728;this.rows=t.Rows||0;this.eoblock=t.EndOfBlock??!0;this.black=t.BlackIs1||!1;this.codingLine=new Uint32Array(this.columns+1);this.refLine=new Uint32Array(this.columns+2);this.codingLine[0]=this.columns;this.codingPos=0;this.row=0;this.nextLine2D=this.encoding<0;this.inputBits=0;this.inputBuf=0;this.outputBits=0;this.rowsDone=!1;let r;for(;0===(r=this._lookBits(12));)this._eatBits(1);1===r&&this._eatBits(12);if(this.encoding>0){this.nextLine2D=!this._lookBits(1);this._eatBits(1)}}readNextChar(){if(this.eof)return-1;const e=this.refLine,t=this.codingLine,r=this.columns;let n,i,o,s,a;if(0===this.outputBits){this.rowsDone&&(this.eof=!0);if(this.eof)return-1;this.err=!1;let o,a,c;if(this.nextLine2D){for(s=0;t[s]<r;++s)e[s]=t[s];e[s++]=r;e[s]=r;t[0]=0;this.codingPos=0;n=0;i=0;for(;t[this.codingPos]<r;){o=this._getTwoDimCode();switch(o){case 0:this._addPixels(e[n+1],i);e[n+1]<r&&(n+=2);break;case 1:o=a=0;if(i){do{o+=c=this._getBlackCode()}while(c>=64);do{a+=c=this._getWhiteCode()}while(c>=64)}else{do{o+=c=this._getWhiteCode()}while(c>=64);do{a+=c=this._getBlackCode()}while(c>=64)}this._addPixels(t[this.codingPos]+o,i);t[this.codingPos]<r&&this._addPixels(t[this.codingPos]+a,1^i);for(;e[n]<=t[this.codingPos]&&e[n]<r;)n+=2;break;case 7:this._addPixels(e[n]+3,i);i^=1;if(t[this.codingPos]<r){++n;for(;e[n]<=t[this.codingPos]&&e[n]<r;)n+=2}break;case 5:this._addPixels(e[n]+2,i);i^=1;if(t[this.codingPos]<r){++n;for(;e[n]<=t[this.codingPos]&&e[n]<r;)n+=2}break;case 3:this._addPixels(e[n]+1,i);i^=1;if(t[this.codingPos]<r){++n;for(;e[n]<=t[this.codingPos]&&e[n]<r;)n+=2}break;case 2:this._addPixels(e[n],i);i^=1;if(t[this.codingPos]<r){++n;for(;e[n]<=t[this.codingPos]&&e[n]<r;)n+=2}break;case 8:this._addPixelsNeg(e[n]-3,i);i^=1;if(t[this.codingPos]<r){n>0?--n:++n;for(;e[n]<=t[this.codingPos]&&e[n]<r;)n+=2}break;case 6:this._addPixelsNeg(e[n]-2,i);i^=1;if(t[this.codingPos]<r){n>0?--n:++n;for(;e[n]<=t[this.codingPos]&&e[n]<r;)n+=2}break;case 4:this._addPixelsNeg(e[n]-1,i);i^=1;if(t[this.codingPos]<r){n>0?--n:++n;for(;e[n]<=t[this.codingPos]&&e[n]<r;)n+=2}break;case u:this._addPixels(r,0);this.eof=!0;break;default:info("bad 2d code");this._addPixels(r,0);this.err=!0}}}else{t[0]=0;this.codingPos=0;i=0;for(;t[this.codingPos]<r;){o=0;if(i)do{o+=c=this._getBlackCode()}while(c>=64);else do{o+=c=this._getWhiteCode()}while(c>=64);this._addPixels(t[this.codingPos]+o,i);i^=1}}let f=!1;this.byteAlign&&(this.inputBits&=-8);if(this.eoblock||this.row!==this.rows-1){o=this._lookBits(12);if(this.eoline)for(;o!==u&&1!==o;){this._eatBits(1);o=this._lookBits(12)}else for(;0===o;){this._eatBits(1);o=this._lookBits(12)}if(1===o){this._eatBits(12);f=!0}else o===u&&(this.eof=!0)}else this.rowsDone=!0;if(!this.eof&&this.encoding>0&&!this.rowsDone){this.nextLine2D=!this._lookBits(1);this._eatBits(1)}if(this.eoblock&&f&&this.byteAlign){o=this._lookBits(12);if(1===o){this._eatBits(12);if(this.encoding>0){this._lookBits(1);this._eatBits(1)}if(this.encoding>=0)for(s=0;s<4;++s){o=this._lookBits(12);1!==o&&info("bad rtc code: "+o);this._eatBits(12);if(this.encoding>0){this._lookBits(1);this._eatBits(1)}}this.eof=!0}}else if(this.err&&this.eoline){for(;;){o=this._lookBits(13);if(o===u){this.eof=!0;return-1}if(o>>1==1)break;this._eatBits(1)}this._eatBits(12);if(this.encoding>0){this._eatBits(1);this.nextLine2D=!(1&o)}}this.outputBits=t[0]>0?t[this.codingPos=0]:t[this.codingPos=1];this.row++}if(this.outputBits>=8){a=1&this.codingPos?0:255;this.outputBits-=8;if(0===this.outputBits&&t[this.codingPos]<r){this.codingPos++;this.outputBits=t[this.codingPos]-t[this.codingPos-1]}}else{o=8;a=0;do{if("number"!=typeof this.outputBits)throw new FormatError('Invalid /CCITTFaxDecode data, "outputBits" must be a number.');if(this.outputBits>o){a<<=o;1&this.codingPos||(a|=255>>8-o);this.outputBits-=o;o=0}else{a<<=this.outputBits;1&this.codingPos||(a|=255>>8-this.outputBits);o-=this.outputBits;this.outputBits=0;if(t[this.codingPos]<r){this.codingPos++;this.outputBits=t[this.codingPos]-t[this.codingPos-1]}else if(o>0){a<<=o;o=0}}}while(o)}this.black&&(a^=255);return a}_addPixels(e,t){const r=this.codingLine;let n=this.codingPos;if(e>r[n]){if(e>this.columns){info("row is wrong length");this.err=!0;e=this.columns}1&n^t&&++n;r[n]=e}this.codingPos=n}_addPixelsNeg(e,t){const r=this.codingLine;let n=this.codingPos;if(e>r[n]){if(e>this.columns){info("row is wrong length");this.err=!0;e=this.columns}1&n^t&&++n;r[n]=e}else if(e<r[n]){if(e<0){info("invalid code");this.err=!0;e=0}for(;n>0&&e<r[n-1];)--n;r[n]=e}this.codingPos=n}_findTableCode(e,t,r,n){const i=n||0;for(let n=e;n<=t;++n){let e=this._lookBits(n);if(e===u)return[!0,1,!1];n<t&&(e<<=t-n);if(!i||e>=i){const t=r[e-i];if(t[0]===n){this._eatBits(n);return[!0,t[1],!0]}}}return[!1,0,!1]}_getTwoDimCode(){let e,t=0;if(this.eoblock){t=this._lookBits(7);e=l[t];if(e?.[0]>0){this._eatBits(e[0]);return e[1]}}else{const e=this._findTableCode(1,7,l);if(e[0]&&e[2])return e[1]}info("Bad two dim code");return u}_getWhiteCode(){let e,t=0;if(this.eoblock){t=this._lookBits(12);if(t===u)return 1;e=t>>5?d[t>>3]:h[t];if(e[0]>0){this._eatBits(e[0]);return e[1]}}else{let e=this._findTableCode(1,9,d);if(e[0])return e[1];e=this._findTableCode(11,12,h);if(e[0])return e[1]}info("bad white code");this._eatBits(1);return 1}_getBlackCode(){let e,t;if(this.eoblock){e=this._lookBits(13);if(e===u)return 1;t=e>>7?!(e>>9)&&e>>7?m[(e>>1)-64]:g[e>>7]:p[e];if(t[0]>0){this._eatBits(t[0]);return t[1]}}else{let e=this._findTableCode(2,6,g);if(e[0])return e[1];e=this._findTableCode(7,12,m,64);if(e[0])return e[1];e=this._findTableCode(10,13,p);if(e[0])return e[1]}info("bad black code");this._eatBits(1);return 1}_lookBits(e){let t;for(;this.inputBits<e;){if(-1===(t=this.source.next()))return 0===this.inputBits?u:this.inputBuf<<e-this.inputBits&65535>>16-e;this.inputBuf=this.inputBuf<<8|t;this.inputBits+=8}return this.inputBuf>>this.inputBits-e&65535>>16-e}_eatBits(e){(this.inputBits-=e)<0&&(this.inputBits=0)}}class Jbig2Error extends i{constructor(e){super(e,"Jbig2Error")}}class ContextCache{getContexts(e){return e in this?this[e]:this[e]=new Int8Array(65536)}}class DecodingContext{constructor(e,t,r){this.data=e;this.start=t;this.end=r}get decoder(){return shadow(this,"decoder",new ArithmeticDecoder(this.data,this.start,this.end))}get contextCache(){return shadow(this,"contextCache",new ContextCache)}}function decodeInteger(e,t,r){const n=e.getContexts(t);let i=1;function readBits(e){let t=0;for(let o=0;o<e;o++){const e=r.readBit(n,i);i=i<256?i<<1|e:511&(i<<1|e)|256;t=t<<1|e}return t>>>0}const o=readBits(1),s=readBits(1)?readBits(1)?readBits(1)?readBits(1)?readBits(1)?readBits(32)+4436:readBits(12)+340:readBits(8)+84:readBits(6)+20:readBits(4)+4:readBits(2);let a;0===o?a=s:s>0&&(a=-s);return a>=-2147483648&&a<=2147483647?a:null}function decodeIAID(e,t,r){const n=e.getContexts("IAID");let i=1;for(let e=0;e<r;e++){i=i<<1|t.readBit(n,i)}return r<31?i&(1<<r)-1:2147483647&i}const y=["SymbolDictionary",null,null,null,"IntermediateTextRegion",null,"ImmediateTextRegion","ImmediateLosslessTextRegion",null,null,null,null,null,null,null,null,"PatternDictionary",null,null,null,"IntermediateHalftoneRegion",null,"ImmediateHalftoneRegion","ImmediateLosslessHalftoneRegion",null,null,null,null,null,null,null,null,null,null,null,null,"IntermediateGenericRegion",null,"ImmediateGenericRegion","ImmediateLosslessGenericRegion","IntermediateGenericRefinementRegion",null,"ImmediateGenericRefinementRegion","ImmediateLosslessGenericRefinementRegion",null,null,null,null,"PageInformation","EndOfPage","EndOfStripe","EndOfFile","Profiles","Tables",null,null,null,null,null,null,null,null,"Extension"],b=[[{x:-1,y:-2},{x:0,y:-2},{x:1,y:-2},{x:-2,y:-1},{x:-1,y:-1},{x:0,y:-1},{x:1,y:-1},{x:2,y:-1},{x:-4,y:0},{x:-3,y:0},{x:-2,y:0},{x:-1,y:0}],[{x:-1,y:-2},{x:0,y:-2},{x:1,y:-2},{x:2,y:-2},{x:-2,y:-1},{x:-1,y:-1},{x:0,y:-1},{x:1,y:-1},{x:2,y:-1},{x:-3,y:0},{x:-2,y:0},{x:-1,y:0}],[{x:-1,y:-2},{x:0,y:-2},{x:1,y:-2},{x:-2,y:-1},{x:-1,y:-1},{x:0,y:-1},{x:1,y:-1},{x:-2,y:0},{x:-1,y:0}],[{x:-3,y:-1},{x:-2,y:-1},{x:-1,y:-1},{x:0,y:-1},{x:1,y:-1},{x:-4,y:0},{x:-3,y:0},{x:-2,y:0},{x:-1,y:0}]],w=[{coding:[{x:0,y:-1},{x:1,y:-1},{x:-1,y:0}],reference:[{x:0,y:-1},{x:1,y:-1},{x:-1,y:0},{x:0,y:0},{x:1,y:0},{x:-1,y:1},{x:0,y:1},{x:1,y:1}]},{coding:[{x:-1,y:-1},{x:0,y:-1},{x:1,y:-1},{x:-1,y:0}],reference:[{x:0,y:-1},{x:-1,y:0},{x:0,y:0},{x:1,y:0},{x:0,y:1},{x:1,y:1}]}],v=[39717,1941,229,405],_=[32,8];function decodeBitmap(e,t,r,n,i,o,s,a){if(e){return decodeMMRBitmap(new Reader(a.data,a.start,a.end),t,r,!1)}if(0===n&&!o&&!i&&4===s.length&&3===s[0].x&&-1===s[0].y&&-3===s[1].x&&-1===s[1].y&&2===s[2].x&&-2===s[2].y&&-2===s[3].x&&-2===s[3].y)return function decodeBitmapTemplate0(e,t,r){const n=r.decoder,i=r.contextCache.getContexts("GB"),o=[];let s,a,c,f,u,l,h;for(a=0;a<t;a++){u=o[a]=new Uint8Array(e);l=a<1?u:o[a-1];h=a<2?u:o[a-2];s=h[0]<<13|h[1]<<12|h[2]<<11|l[0]<<7|l[1]<<6|l[2]<<5|l[3]<<4;for(c=0;c<e;c++){u[c]=f=n.readBit(i,s);s=(31735&s)<<1|(c+3<e?h[c+3]<<11:0)|(c+4<e?l[c+4]<<4:0)|f}}return o}(t,r,a);const c=!!o,f=b[n].concat(s);f.sort(((e,t)=>e.y-t.y||e.x-t.x));const u=f.length,l=new Int8Array(u),h=new Int8Array(u),d=[];let p,m,g=0,y=0,w=0,_=0;for(m=0;m<u;m++){l[m]=f[m].x;h[m]=f[m].y;y=Math.min(y,f[m].x);w=Math.max(w,f[m].x);_=Math.min(_,f[m].y);m<u-1&&f[m].y===f[m+1].y&&f[m].x===f[m+1].x-1?g|=1<<u-1-m:d.push(m)}const x=d.length,C=new Int8Array(x),R=new Int8Array(x),A=new Uint16Array(x);for(p=0;p<x;p++){m=d[p];C[p]=f[m].x;R[p]=f[m].y;A[p]=1<<u-1-m}const S=-y,B=-_,I=t-w,E=v[n];let k=new Uint8Array(t);const D=[],T=a.decoder,M=a.contextCache.getContexts("GB");let P,O,U,L,N,G=0,q=0;for(let e=0;e<r;e++){if(i){G^=T.readBit(M,E);if(G){D.push(k);continue}}k=new Uint8Array(k);D.push(k);for(P=0;P<t;P++){if(c&&o[e][P]){k[P]=0;continue}if(P>=S&&P<I&&e>=B){q=q<<1&g;for(m=0;m<x;m++){O=e+R[m];U=P+C[m];L=D[O][U];if(L){L=A[m];q|=L}}}else{q=0;N=u-1;for(m=0;m<u;m++,N--){U=P+l[m];if(U>=0&&U<t){O=e+h[m];if(O>=0){L=D[O][U];L&&(q|=L<<N)}}}}const r=T.readBit(M,q);k[P]=r}}return D}function decodeRefinement(e,t,r,n,i,o,s,a,c){let f=w[r].coding;0===r&&(f=f.concat([a[0]]));const u=f.length,l=new Int32Array(u),h=new Int32Array(u);let d;for(d=0;d<u;d++){l[d]=f[d].x;h[d]=f[d].y}let p=w[r].reference;0===r&&(p=p.concat([a[1]]));const m=p.length,g=new Int32Array(m),y=new Int32Array(m);for(d=0;d<m;d++){g[d]=p[d].x;y[d]=p[d].y}const b=n[0].length,v=n.length,x=_[r],C=[],R=c.decoder,A=c.contextCache.getContexts("GR");let S=0;for(let r=0;r<t;r++){if(s){S^=R.readBit(A,x);if(S)throw new Jbig2Error("prediction is not supported")}const t=new Uint8Array(e);C.push(t);for(let s=0;s<e;s++){let a,c,f=0;for(d=0;d<u;d++){a=r+h[d];c=s+l[d];a<0||c<0||c>=e?f<<=1:f=f<<1|C[a][c]}for(d=0;d<m;d++){a=r+y[d]-o;c=s+g[d]-i;a<0||a>=v||c<0||c>=b?f<<=1:f=f<<1|n[a][c]}const p=R.readBit(A,f);t[s]=p}}return C}function decodeTextRegion(e,t,r,n,i,o,s,a,c,f,u,l,h,d,p,m,g,y,b){if(e&&t)throw new Jbig2Error("refinement with Huffman is not supported");const w=[];let v,_;for(v=0;v<n;v++){_=new Uint8Array(r);i&&_.fill(i);w.push(_)}const x=g.decoder,C=g.contextCache;let R=e?-d.tableDeltaT.decode(b):-decodeInteger(C,"IADT",x),A=0;v=0;for(;v<o;){R+=e?d.tableDeltaT.decode(b):decodeInteger(C,"IADT",x);A+=e?d.tableFirstS.decode(b):decodeInteger(C,"IAFS",x);let n=A;for(;;){let i=0;s>1&&(i=e?b.readBits(y):decodeInteger(C,"IAIT",x));const o=s*R+i,A=e?d.symbolIDTable.decode(b):decodeIAID(C,x,c),S=t&&(e?b.readBit():decodeInteger(C,"IARI",x));let B=a[A],I=B[0].length,E=B.length;if(S){const e=decodeInteger(C,"IARDW",x),t=decodeInteger(C,"IARDH",x);I+=e;E+=t;B=decodeRefinement(I,E,p,B,(e>>1)+decodeInteger(C,"IARDX",x),(t>>1)+decodeInteger(C,"IARDY",x),!1,m,g)}let k=0;f?1&l?k=E-1:n+=E-1:l>1?n+=I-1:k=I-1;const D=o-(1&l?0:E-1),T=n-(2&l?I-1:0);let M,P,O;if(f)for(M=0;M<E;M++){_=w[T+M];if(!_)continue;O=B[M];const e=Math.min(r-D,I);switch(h){case 0:for(P=0;P<e;P++)_[D+P]|=O[P];break;case 2:for(P=0;P<e;P++)_[D+P]^=O[P];break;default:throw new Jbig2Error(`operator ${h} is not supported`)}}else for(P=0;P<E;P++){_=w[D+P];if(_){O=B[P];switch(h){case 0:for(M=0;M<I;M++)_[T+M]|=O[M];break;case 2:for(M=0;M<I;M++)_[T+M]^=O[M];break;default:throw new Jbig2Error(`operator ${h} is not supported`)}}}v++;const U=e?d.tableDeltaS.decode(b):decodeInteger(C,"IADS",x);if(null===U)break;n+=k+U+u}}return w}function readSegmentHeader(e,t){const r={};r.number=readUint32(e,t);const n=e[t+4],i=63&n;if(!y[i])throw new Jbig2Error("invalid segment type: "+i);r.type=i;r.typeName=y[i];r.deferredNonRetain=!!(128&n);const o=!!(64&n),s=e[t+5];let a=s>>5&7;const c=[31&s];let f=t+6;if(7===s){a=536870911&readUint32(e,f-1);f+=3;let t=a+7>>3;c[0]=e[f++];for(;--t>0;)c.push(e[f++])}else if(5===s||6===s)throw new Jbig2Error("invalid referred-to flags");r.retainBits=c;let u=4;r.number<=256?u=1:r.number<=65536&&(u=2);const l=[];let h,d;for(h=0;h<a;h++){let t;t=1===u?e[f]:2===u?readUint16(e,f):readUint32(e,f);l.push(t);f+=u}r.referredTo=l;if(o){r.pageAssociation=readUint32(e,f);f+=4}else r.pageAssociation=e[f++];r.length=readUint32(e,f);f+=4;if(4294967295===r.length){if(38!==i)throw new Jbig2Error("invalid unknown segment length");{const t=readRegionSegmentInformation(e,f),n=!!(1&e[f+x]),i=6,o=new Uint8Array(i);if(!n){o[0]=255;o[1]=172}o[2]=t.height>>>24&255;o[3]=t.height>>16&255;o[4]=t.height>>8&255;o[5]=255&t.height;for(h=f,d=e.length;h<d;h++){let t=0;for(;t<i&&o[t]===e[h+t];)t++;if(t===i){r.length=h+i;break}}if(4294967295===r.length)throw new Jbig2Error("segment end was not found")}}r.headerEnd=f;return r}function readSegments(e,t,r,n){const i=[];let o=r;for(;o<n;){const r=readSegmentHeader(t,o);o=r.headerEnd;const n={header:r,data:t};if(!e.randomAccess){n.start=o;o+=r.length;n.end=o}i.push(n);if(51===r.type)break}if(e.randomAccess)for(let e=0,t=i.length;e<t;e++){i[e].start=o;o+=i[e].header.length;i[e].end=o}return i}function readRegionSegmentInformation(e,t){return{width:readUint32(e,t),height:readUint32(e,t+4),x:readUint32(e,t+8),y:readUint32(e,t+12),combinationOperator:7&e[t+16]}}const x=17;function processSegment(e,t){const r=e.header,n=e.data,i=e.end;let o,s,a,c,f=e.start;switch(r.type){case 0:const e={},t=readUint16(n,f);e.huffman=!!(1&t);e.refinement=!!(2&t);e.huffmanDHSelector=t>>2&3;e.huffmanDWSelector=t>>4&3;e.bitmapSizeSelector=t>>6&1;e.aggregationInstancesSelector=t>>7&1;e.bitmapCodingContextUsed=!!(256&t);e.bitmapCodingContextRetained=!!(512&t);e.template=t>>10&3;e.refinementTemplate=t>>12&1;f+=2;if(!e.huffman){c=0===e.template?4:1;s=[];for(a=0;a<c;a++){s.push({x:readInt8(n,f),y:readInt8(n,f+1)});f+=2}e.at=s}if(e.refinement&&!e.refinementTemplate){s=[];for(a=0;a<2;a++){s.push({x:readInt8(n,f),y:readInt8(n,f+1)});f+=2}e.refinementAt=s}e.numberOfExportedSymbols=readUint32(n,f);f+=4;e.numberOfNewSymbols=readUint32(n,f);f+=4;o=[e,r.number,r.referredTo,n,f,i];break;case 6:case 7:const u={};u.info=readRegionSegmentInformation(n,f);f+=x;const l=readUint16(n,f);f+=2;u.huffman=!!(1&l);u.refinement=!!(2&l);u.logStripSize=l>>2&3;u.stripSize=1<<u.logStripSize;u.referenceCorner=l>>4&3;u.transposed=!!(64&l);u.combinationOperator=l>>7&3;u.defaultPixelValue=l>>9&1;u.dsOffset=l<<17>>27;u.refinementTemplate=l>>15&1;if(u.huffman){const e=readUint16(n,f);f+=2;u.huffmanFS=3&e;u.huffmanDS=e>>2&3;u.huffmanDT=e>>4&3;u.huffmanRefinementDW=e>>6&3;u.huffmanRefinementDH=e>>8&3;u.huffmanRefinementDX=e>>10&3;u.huffmanRefinementDY=e>>12&3;u.huffmanRefinementSizeSelector=!!(16384&e)}if(u.refinement&&!u.refinementTemplate){s=[];for(a=0;a<2;a++){s.push({x:readInt8(n,f),y:readInt8(n,f+1)});f+=2}u.refinementAt=s}u.numberOfSymbolInstances=readUint32(n,f);f+=4;o=[u,r.referredTo,n,f,i];break;case 16:const h={},d=n[f++];h.mmr=!!(1&d);h.template=d>>1&3;h.patternWidth=n[f++];h.patternHeight=n[f++];h.maxPatternIndex=readUint32(n,f);f+=4;o=[h,r.number,n,f,i];break;case 22:case 23:const p={};p.info=readRegionSegmentInformation(n,f);f+=x;const m=n[f++];p.mmr=!!(1&m);p.template=m>>1&3;p.enableSkip=!!(8&m);p.combinationOperator=m>>4&7;p.defaultPixelValue=m>>7&1;p.gridWidth=readUint32(n,f);f+=4;p.gridHeight=readUint32(n,f);f+=4;p.gridOffsetX=4294967295&readUint32(n,f);f+=4;p.gridOffsetY=4294967295&readUint32(n,f);f+=4;p.gridVectorX=readUint16(n,f);f+=2;p.gridVectorY=readUint16(n,f);f+=2;o=[p,r.referredTo,n,f,i];break;case 38:case 39:const g={};g.info=readRegionSegmentInformation(n,f);f+=x;const y=n[f++];g.mmr=!!(1&y);g.template=y>>1&3;g.prediction=!!(8&y);if(!g.mmr){c=0===g.template?4:1;s=[];for(a=0;a<c;a++){s.push({x:readInt8(n,f),y:readInt8(n,f+1)});f+=2}g.at=s}o=[g,n,f,i];break;case 48:const b={width:readUint32(n,f),height:readUint32(n,f+4),resolutionX:readUint32(n,f+8),resolutionY:readUint32(n,f+12)};4294967295===b.height&&delete b.height;const w=n[f+16];readUint16(n,f+17);b.lossless=!!(1&w);b.refinement=!!(2&w);b.defaultPixelValue=w>>2&1;b.combinationOperator=w>>3&3;b.requiresBuffer=!!(32&w);b.combinationOperatorOverride=!!(64&w);o=[b];break;case 49:case 50:case 51:case 62:break;case 53:o=[r.number,n,f,i];break;default:throw new Jbig2Error(`segment type ${r.typeName}(${r.type}) is not implemented`)}const u="on"+r.typeName;u in t&&t[u].apply(t,o)}function processSegments(e,t){for(let r=0,n=e.length;r<n;r++)processSegment(e[r],t)}class SimpleSegmentVisitor{onPageInformation(e){this.currentPageInfo=e;const t=e.width+7>>3,r=new Uint8ClampedArray(t*e.height);e.defaultPixelValue&&r.fill(255);this.buffer=r}drawBitmap(e,t){const r=this.currentPageInfo,n=e.width,i=e.height,o=r.width+7>>3,s=r.combinationOperatorOverride?e.combinationOperator:r.combinationOperator,a=this.buffer,c=128>>(7&e.x);let f,u,l,h,d=e.y*o+(e.x>>3);switch(s){case 0:for(f=0;f<i;f++){l=c;h=d;for(u=0;u<n;u++){t[f][u]&&(a[h]|=l);l>>=1;if(!l){l=128;h++}}d+=o}break;case 2:for(f=0;f<i;f++){l=c;h=d;for(u=0;u<n;u++){t[f][u]&&(a[h]^=l);l>>=1;if(!l){l=128;h++}}d+=o}break;default:throw new Jbig2Error(`operator ${s} is not supported`)}}onImmediateGenericRegion(e,t,r,n){const i=e.info,o=new DecodingContext(t,r,n),s=decodeBitmap(e.mmr,i.width,i.height,e.template,e.prediction,null,e.at,o);this.drawBitmap(i,s)}onImmediateLosslessGenericRegion(){this.onImmediateGenericRegion(...arguments)}onSymbolDictionary(e,t,r,n,i,o){let s,a;if(e.huffman){s=function getSymbolDictionaryHuffmanTables(e,t,r){let n,i,o,s,a=0;switch(e.huffmanDHSelector){case 0:case 1:n=getStandardTable(e.huffmanDHSelector+4);break;case 3:n=getCustomHuffmanTable(a,t,r);a++;break;default:throw new Jbig2Error("invalid Huffman DH selector")}switch(e.huffmanDWSelector){case 0:case 1:i=getStandardTable(e.huffmanDWSelector+2);break;case 3:i=getCustomHuffmanTable(a,t,r);a++;break;default:throw new Jbig2Error("invalid Huffman DW selector")}if(e.bitmapSizeSelector){o=getCustomHuffmanTable(a,t,r);a++}else o=getStandardTable(1);s=e.aggregationInstancesSelector?getCustomHuffmanTable(a,t,r):getStandardTable(1);return{tableDeltaHeight:n,tableDeltaWidth:i,tableBitmapSize:o,tableAggregateInstances:s}}(e,r,this.customTables);a=new Reader(n,i,o)}let c=this.symbols;c||(this.symbols=c={});const f=[];for(const e of r){const t=c[e];t&&f.push(...t)}const u=new DecodingContext(n,i,o);c[t]=function decodeSymbolDictionary(e,t,r,n,i,o,s,a,c,f,u,l){if(e&&t)throw new Jbig2Error("symbol refinement with Huffman is not supported");const h=[];let d=0,p=log2(r.length+n);const m=u.decoder,g=u.contextCache;let y,b;if(e){y=getStandardTable(1);b=[];p=Math.max(p,1)}for(;h.length<n;){d+=e?o.tableDeltaHeight.decode(l):decodeInteger(g,"IADH",m);let n=0,i=0;const y=e?b.length:0;for(;;){const y=e?o.tableDeltaWidth.decode(l):decodeInteger(g,"IADW",m);if(null===y)break;n+=y;i+=n;let w;if(t){const i=decodeInteger(g,"IAAI",m);if(i>1)w=decodeTextRegion(e,t,n,d,0,i,1,r.concat(h),p,0,0,1,0,o,c,f,u,0,l);else{const e=decodeIAID(g,m,p),t=decodeInteger(g,"IARDX",m),i=decodeInteger(g,"IARDY",m);w=decodeRefinement(n,d,c,e<r.length?r[e]:h[e-r.length],t,i,!1,f,u)}h.push(w)}else if(e)b.push(n);else{w=decodeBitmap(!1,n,d,s,!1,null,a,u);h.push(w)}}if(e&&!t){const e=o.tableBitmapSize.decode(l);l.byteAlign();let t;if(0===e)t=readUncompressedBitmap(l,i,d);else{const r=l.end,n=l.position+e;l.end=n;t=decodeMMRBitmap(l,i,d,!1);l.end=r;l.position=n}const r=b.length;if(y===r-1)h.push(t);else{let e,n,i,o,s,a=0;for(e=y;e<r;e++){o=b[e];i=a+o;s=[];for(n=0;n<d;n++)s.push(t[n].subarray(a,i));h.push(s);a=i}}}}const w=[],v=[];let _,x,C=!1;const R=r.length+n;for(;v.length<R;){let t=e?y.decode(l):decodeInteger(g,"IAEX",m);for(;t--;)v.push(C);C=!C}for(_=0,x=r.length;_<x;_++)v[_]&&w.push(r[_]);for(let e=0;e<n;_++,e++)v[_]&&w.push(h[e]);return w}(e.huffman,e.refinement,f,e.numberOfNewSymbols,e.numberOfExportedSymbols,s,e.template,e.at,e.refinementTemplate,e.refinementAt,u,a)}onImmediateTextRegion(e,t,r,n,i){const o=e.info;let s,a;const c=this.symbols,f=[];for(const e of t){const t=c[e];t&&f.push(...t)}const u=log2(f.length);if(e.huffman){a=new Reader(r,n,i);s=function getTextRegionHuffmanTables(e,t,r,n,i){const o=[];for(let e=0;e<=34;e++){const t=i.readBits(4);o.push(new HuffmanLine([e,t,0,0]))}const s=new HuffmanTable(o,!1);o.length=0;for(let e=0;e<n;){const t=s.decode(i);if(t>=32){let r,n,s;switch(t){case 32:if(0===e)throw new Jbig2Error("no previous value in symbol ID table");n=i.readBits(2)+3;r=o[e-1].prefixLength;break;case 33:n=i.readBits(3)+3;r=0;break;case 34:n=i.readBits(7)+11;r=0;break;default:throw new Jbig2Error("invalid code length in symbol ID table")}for(s=0;s<n;s++){o.push(new HuffmanLine([e,r,0,0]));e++}}else{o.push(new HuffmanLine([e,t,0,0]));e++}}i.byteAlign();const a=new HuffmanTable(o,!1);let c,f,u,l=0;switch(e.huffmanFS){case 0:case 1:c=getStandardTable(e.huffmanFS+6);break;case 3:c=getCustomHuffmanTable(l,t,r);l++;break;default:throw new Jbig2Error("invalid Huffman FS selector")}switch(e.huffmanDS){case 0:case 1:case 2:f=getStandardTable(e.huffmanDS+8);break;case 3:f=getCustomHuffmanTable(l,t,r);l++;break;default:throw new Jbig2Error("invalid Huffman DS selector")}switch(e.huffmanDT){case 0:case 1:case 2:u=getStandardTable(e.huffmanDT+11);break;case 3:u=getCustomHuffmanTable(l,t,r);l++;break;default:throw new Jbig2Error("invalid Huffman DT selector")}if(e.refinement)throw new Jbig2Error("refinement with Huffman is not supported");return{symbolIDTable:a,tableFirstS:c,tableDeltaS:f,tableDeltaT:u}}(e,t,this.customTables,f.length,a)}const l=new DecodingContext(r,n,i),h=decodeTextRegion(e.huffman,e.refinement,o.width,o.height,e.defaultPixelValue,e.numberOfSymbolInstances,e.stripSize,f,u,e.transposed,e.dsOffset,e.referenceCorner,e.combinationOperator,s,e.refinementTemplate,e.refinementAt,l,e.logStripSize,a);this.drawBitmap(o,h)}onImmediateLosslessTextRegion(){this.onImmediateTextRegion(...arguments)}onPatternDictionary(e,t,r,n,i){let o=this.patterns;o||(this.patterns=o={});const s=new DecodingContext(r,n,i);o[t]=function decodePatternDictionary(e,t,r,n,i,o){const s=[];if(!e){s.push({x:-t,y:0});0===i&&s.push({x:-3,y:-1},{x:2,y:-2},{x:-2,y:-2})}const a=decodeBitmap(e,(n+1)*t,r,i,!1,null,s,o),c=[];for(let e=0;e<=n;e++){const n=[],i=t*e,o=i+t;for(let e=0;e<r;e++)n.push(a[e].subarray(i,o));c.push(n)}return c}(e.mmr,e.patternWidth,e.patternHeight,e.maxPatternIndex,e.template,s)}onImmediateHalftoneRegion(e,t,r,n,i){const o=this.patterns[t[0]],s=e.info,a=new DecodingContext(r,n,i),c=function decodeHalftoneRegion(e,t,r,n,i,o,s,a,c,f,u,l,h,d,p){if(s)throw new Jbig2Error("skip is not supported");if(0!==a)throw new Jbig2Error(`operator "${a}" is not supported in halftone region`);const m=[];let g,y,b;for(g=0;g<i;g++){b=new Uint8Array(n);o&&b.fill(o);m.push(b)}const w=t.length,v=t[0],_=v[0].length,x=v.length,C=log2(w),R=[];if(!e){R.push({x:r<=1?3:2,y:-1});0===r&&R.push({x:-3,y:-1},{x:2,y:-2},{x:-2,y:-2})}const A=[];let S,B,I,E,k,D,T,M,P,O,U;e&&(S=new Reader(p.data,p.start,p.end));for(g=C-1;g>=0;g--){B=e?decodeMMRBitmap(S,c,f,!0):decodeBitmap(!1,c,f,r,!1,null,R,p);A[g]=B}for(I=0;I<f;I++)for(E=0;E<c;E++){k=0;D=0;for(y=C-1;y>=0;y--){k^=A[y][I][E];D|=k<<y}T=t[D];M=u+I*d+E*h>>8;P=l+I*h-E*d>>8;if(M>=0&&M+_<=n&&P>=0&&P+x<=i)for(g=0;g<x;g++){U=m[P+g];O=T[g];for(y=0;y<_;y++)U[M+y]|=O[y]}else{let e,t;for(g=0;g<x;g++){t=P+g;if(!(t<0||t>=i)){U=m[t];O=T[g];for(y=0;y<_;y++){e=M+y;e>=0&&e<n&&(U[e]|=O[y])}}}}}return m}(e.mmr,o,e.template,s.width,s.height,e.defaultPixelValue,e.enableSkip,e.combinationOperator,e.gridWidth,e.gridHeight,e.gridOffsetX,e.gridOffsetY,e.gridVectorX,e.gridVectorY,a);this.drawBitmap(s,c)}onImmediateLosslessHalftoneRegion(){this.onImmediateHalftoneRegion(...arguments)}onTables(e,t,r,n){let i=this.customTables;i||(this.customTables=i={});i[e]=function decodeTablesSegment(e,t,r){const n=e[t],i=4294967295&readUint32(e,t+1),o=4294967295&readUint32(e,t+5),s=new Reader(e,t+9,r),a=1+(n>>1&7),c=1+(n>>4&7),f=[];let u,l,h=i;do{u=s.readBits(a);l=s.readBits(c);f.push(new HuffmanLine([h,u,l,0]));h+=1<<l}while(h<o);u=s.readBits(a);f.push(new HuffmanLine([i-1,u,32,0,"lower"]));u=s.readBits(a);f.push(new HuffmanLine([o,u,32,0]));if(1&n){u=s.readBits(a);f.push(new HuffmanLine([u,0]))}return new HuffmanTable(f,!1)}(t,r,n)}}class HuffmanLine{constructor(e){if(2===e.length){this.isOOB=!0;this.rangeLow=0;this.prefixLength=e[0];this.rangeLength=0;this.prefixCode=e[1];this.isLowerRange=!1}else{this.isOOB=!1;this.rangeLow=e[0];this.prefixLength=e[1];this.rangeLength=e[2];this.prefixCode=e[3];this.isLowerRange="lower"===e[4]}}}class HuffmanTreeNode{constructor(e){this.children=[];if(e){this.isLeaf=!0;this.rangeLength=e.rangeLength;this.rangeLow=e.rangeLow;this.isLowerRange=e.isLowerRange;this.isOOB=e.isOOB}else this.isLeaf=!1}buildTree(e,t){const r=e.prefixCode>>t&1;if(t<=0)this.children[r]=new HuffmanTreeNode(e);else{let n=this.children[r];n||(this.children[r]=n=new HuffmanTreeNode(null));n.buildTree(e,t-1)}}decodeNode(e){if(this.isLeaf){if(this.isOOB)return null;const t=e.readBits(this.rangeLength);return this.rangeLow+(this.isLowerRange?-t:t)}const t=this.children[e.readBit()];if(!t)throw new Jbig2Error("invalid Huffman data");return t.decodeNode(e)}}class HuffmanTable{constructor(e,t){t||this.assignPrefixCodes(e);this.rootNode=new HuffmanTreeNode(null);for(let t=0,r=e.length;t<r;t++){const r=e[t];r.prefixLength>0&&this.rootNode.buildTree(r,r.prefixLength-1)}}decode(e){return this.rootNode.decodeNode(e)}assignPrefixCodes(e){const t=e.length;let r=0;for(let n=0;n<t;n++)r=Math.max(r,e[n].prefixLength);const n=new Uint32Array(r+1);for(let r=0;r<t;r++)n[e[r].prefixLength]++;let i,o,s,a=1,c=0;n[0]=0;for(;a<=r;){c=c+n[a-1]<<1;i=c;o=0;for(;o<t;){s=e[o];if(s.prefixLength===a){s.prefixCode=i;i++}o++}a++}}}const C={};function getStandardTable(e){let t,r=C[e];if(r)return r;switch(e){case 1:t=[[0,1,4,0],[16,2,8,2],[272,3,16,6],[65808,3,32,7]];break;case 2:t=[[0,1,0,0],[1,2,0,2],[2,3,0,6],[3,4,3,14],[11,5,6,30],[75,6,32,62],[6,63]];break;case 3:t=[[-256,8,8,254],[0,1,0,0],[1,2,0,2],[2,3,0,6],[3,4,3,14],[11,5,6,30],[-257,8,32,255,"lower"],[75,7,32,126],[6,62]];break;case 4:t=[[1,1,0,0],[2,2,0,2],[3,3,0,6],[4,4,3,14],[12,5,6,30],[76,5,32,31]];break;case 5:t=[[-255,7,8,126],[1,1,0,0],[2,2,0,2],[3,3,0,6],[4,4,3,14],[12,5,6,30],[-256,7,32,127,"lower"],[76,6,32,62]];break;case 6:t=[[-2048,5,10,28],[-1024,4,9,8],[-512,4,8,9],[-256,4,7,10],[-128,5,6,29],[-64,5,5,30],[-32,4,5,11],[0,2,7,0],[128,3,7,2],[256,3,8,3],[512,4,9,12],[1024,4,10,13],[-2049,6,32,62,"lower"],[2048,6,32,63]];break;case 7:t=[[-1024,4,9,8],[-512,3,8,0],[-256,4,7,9],[-128,5,6,26],[-64,5,5,27],[-32,4,5,10],[0,4,5,11],[32,5,5,28],[64,5,6,29],[128,4,7,12],[256,3,8,1],[512,3,9,2],[1024,3,10,3],[-1025,5,32,30,"lower"],[2048,5,32,31]];break;case 8:t=[[-15,8,3,252],[-7,9,1,508],[-5,8,1,253],[-3,9,0,509],[-2,7,0,124],[-1,4,0,10],[0,2,1,0],[2,5,0,26],[3,6,0,58],[4,3,4,4],[20,6,1,59],[22,4,4,11],[38,4,5,12],[70,5,6,27],[134,5,7,28],[262,6,7,60],[390,7,8,125],[646,6,10,61],[-16,9,32,510,"lower"],[1670,9,32,511],[2,1]];break;case 9:t=[[-31,8,4,252],[-15,9,2,508],[-11,8,2,253],[-7,9,1,509],[-5,7,1,124],[-3,4,1,10],[-1,3,1,2],[1,3,1,3],[3,5,1,26],[5,6,1,58],[7,3,5,4],[39,6,2,59],[43,4,5,11],[75,4,6,12],[139,5,7,27],[267,5,8,28],[523,6,8,60],[779,7,9,125],[1291,6,11,61],[-32,9,32,510,"lower"],[3339,9,32,511],[2,0]];break;case 10:t=[[-21,7,4,122],[-5,8,0,252],[-4,7,0,123],[-3,5,0,24],[-2,2,2,0],[2,5,0,25],[3,6,0,54],[4,7,0,124],[5,8,0,253],[6,2,6,1],[70,5,5,26],[102,6,5,55],[134,6,6,56],[198,6,7,57],[326,6,8,58],[582,6,9,59],[1094,6,10,60],[2118,7,11,125],[-22,8,32,254,"lower"],[4166,8,32,255],[2,2]];break;case 11:t=[[1,1,0,0],[2,2,1,2],[4,4,0,12],[5,4,1,13],[7,5,1,28],[9,5,2,29],[13,6,2,60],[17,7,2,122],[21,7,3,123],[29,7,4,124],[45,7,5,125],[77,7,6,126],[141,7,32,127]];break;case 12:t=[[1,1,0,0],[2,2,0,2],[3,3,1,6],[5,5,0,28],[6,5,1,29],[8,6,1,60],[10,7,0,122],[11,7,1,123],[13,7,2,124],[17,7,3,125],[25,7,4,126],[41,8,5,254],[73,8,32,255]];break;case 13:t=[[1,1,0,0],[2,3,0,4],[3,4,0,12],[4,5,0,28],[5,4,1,13],[7,3,3,5],[15,6,1,58],[17,6,2,59],[21,6,3,60],[29,6,4,61],[45,6,5,62],[77,7,6,126],[141,7,32,127]];break;case 14:t=[[-2,3,0,4],[-1,3,0,5],[0,1,0,0],[1,3,0,6],[2,3,0,7]];break;case 15:t=[[-24,7,4,124],[-8,6,2,60],[-4,5,1,28],[-2,4,0,12],[-1,3,0,4],[0,1,0,0],[1,3,0,5],[2,4,0,13],[3,5,1,29],[5,6,2,61],[9,7,4,125],[-25,7,32,126,"lower"],[25,7,32,127]];break;default:throw new Jbig2Error(`standard table B.${e} does not exist`)}for(let e=0,r=t.length;e<r;e++)t[e]=new HuffmanLine(t[e]);r=new HuffmanTable(t,!0);C[e]=r;return r}class Reader{constructor(e,t,r){this.data=e;this.start=t;this.end=r;this.position=t;this.shift=-1;this.currentByte=0}readBit(){if(this.shift<0){if(this.position>=this.end)throw new Jbig2Error("end of data while reading bit");this.currentByte=this.data[this.position++];this.shift=7}const e=this.currentByte>>this.shift&1;this.shift--;return e}readBits(e){let t,r=0;for(t=e-1;t>=0;t--)r|=this.readBit()<<t;return r}byteAlign(){this.shift=-1}next(){return this.position>=this.end?-1:this.data[this.position++]}}function getCustomHuffmanTable(e,t,r){let n=0;for(let i=0,o=t.length;i<o;i++){const o=r[t[i]];if(o){if(e===n)return o;n++}}throw new Jbig2Error("can't find custom Huffman table")}function readUncompressedBitmap(e,t,r){const n=[];for(let i=0;i<r;i++){const r=new Uint8Array(t);n.push(r);for(let n=0;n<t;n++)r[n]=e.readBit();e.byteAlign()}return n}function decodeMMRBitmap(e,t,r,n){const i=new CCITTFaxDecoder(e,{K:-1,Columns:t,Rows:r,BlackIs1:!0,EndOfBlock:n}),o=[];let s,a=!1;for(let e=0;e<r;e++){const e=new Uint8Array(t);o.push(e);let r=-1;for(let n=0;n<t;n++){if(r<0){s=i.readNextChar();if(-1===s){s=0;a=!0}r=7}e[n]=s>>r&1;r--}}if(n&&!a){const e=5;for(let t=0;t<e&&-1!==i.readNextChar();t++);}return o}class Jbig2Image{parseChunks(e){return function parseJbig2Chunks(e){const t=new SimpleSegmentVisitor;for(let r=0,n=e.length;r<n;r++){const n=e[r];processSegments(readSegments({},n.data,n.start,n.end),t)}return t.buffer}(e)}parse(e){const{imgData:t,width:r,height:n}=function parseJbig2(e){const t=e.length;let r=0;if(151!==e[r]||74!==e[r+1]||66!==e[r+2]||50!==e[r+3]||13!==e[r+4]||10!==e[r+5]||26!==e[r+6]||10!==e[r+7])throw new Jbig2Error("parseJbig2 - invalid header.");const n=Object.create(null);r+=8;const i=e[r++];n.randomAccess=!(1&i);if(!(2&i)){n.numberOfPages=readUint32(e,r);r+=4}const o=readSegments(n,e,r,t),s=new SimpleSegmentVisitor;processSegments(o,s);const{width:a,height:c}=s.currentPageInfo,f=s.buffer,u=new Uint8ClampedArray(a*c);let l=0,h=0;for(let e=0;e<c;e++){let e,t=0;for(let r=0;r<a;r++){if(!t){t=128;e=f[h++]}u[l++]=e&t?0:255;t>>=1}}return{imgData:u,width:a,height:c}}(e);this.width=r;this.height=n;return t}}class ColorSpace{static#r=new Uint8ClampedArray(3);constructor(e,t){this.name=e;this.numComps=t}getRgb(e,t,r=new Uint8ClampedArray(3)){this.getRgbItem(e,t,r,0);return r}getRgbHex(e,t){const r=this.getRgb(e,t,ColorSpace.#r);return util_Util.makeHexColor(r[0],r[1],r[2])}getRgbItem(e,t,r,n){unreachable("Should not call ColorSpace.getRgbItem")}getRgbBuffer(e,t,r,n,i,o,s){unreachable("Should not call ColorSpace.getRgbBuffer")}getOutputLength(e,t){unreachable("Should not call ColorSpace.getOutputLength")}isPassthrough(e){return!1}isDefaultDecode(e,t){return ColorSpace.isDefaultDecode(e,this.numComps)}fillRgb(e,t,r,n,i,o,s,a,c){const f=t*r;let u=null;const l=1<<s,h=r!==i||t!==n;if(this.isPassthrough(s))u=a;else if(1===this.numComps&&f>l&&"DeviceGray"!==this.name&&"DeviceRGB"!==this.name){const t=s<=8?new Uint8Array(l):new Uint16Array(l);for(let e=0;e<l;e++)t[e]=e;const r=new Uint8ClampedArray(3*l);this.getRgbBuffer(t,0,l,r,0,s,0);if(h){u=new Uint8Array(3*f);let e=0;for(let t=0;t<f;++t){const n=3*a[t];u[e++]=r[n];u[e++]=r[n+1];u[e++]=r[n+2]}}else{let t=0;for(let n=0;n<f;++n){const i=3*a[n];e[t++]=r[i];e[t++]=r[i+1];e[t++]=r[i+2];t+=c}}}else if(h){u=new Uint8ClampedArray(3*f);this.getRgbBuffer(a,0,f,u,0,s,0)}else this.getRgbBuffer(a,0,n*o,e,0,s,c);if(u)if(h)!function resizeRgbImage(e,t,r,n,i,o,s){s=1!==s?0:s;const a=r/i,c=n/o;let f,u=0;const l=new Uint16Array(i),h=3*r;for(let e=0;e<i;e++)l[e]=3*Math.floor(e*a);for(let r=0;r<o;r++){const n=Math.floor(r*c)*h;for(let r=0;r<i;r++){f=n+l[r];t[u++]=e[f++];t[u++]=e[f++];t[u++]=e[f++];u+=s}}}(u,e,t,r,n,i,c);else{let t=0,r=0;for(let i=0,s=n*o;i<s;i++){e[t++]=u[r++];e[t++]=u[r++];e[t++]=u[r++];t+=c}}}get usesZeroToOneRange(){return shadow(this,"usesZeroToOneRange",!0)}static isDefaultDecode(e,t){if(!Array.isArray(e))return!0;if(2*t!==e.length){util_warn("The decode map is not the correct length");return!0}for(let t=0,r=e.length;t<r;t+=2)if(0!==e[t]||1!==e[t+1])return!1;return!0}}class AlternateCS extends ColorSpace{constructor(e,t,r){super("Alternate",e);this.base=t;this.tintFn=r;this.tmpBuf=new Float32Array(t.numComps)}getRgbItem(e,t,r,n){const i=this.tmpBuf;this.tintFn(e,t,i,0);this.base.getRgbItem(i,0,r,n)}getRgbBuffer(e,t,r,n,i,o,s){const a=this.tintFn,c=this.base,f=1/((1<<o)-1),u=c.numComps,l=c.usesZeroToOneRange,h=(c.isPassthrough(8)||!l)&&0===s;let d=h?i:0;const p=h?n:new Uint8ClampedArray(u*r),m=this.numComps,g=new Float32Array(m),y=new Float32Array(u);let b,w;for(b=0;b<r;b++){for(w=0;w<m;w++)g[w]=e[t++]*f;a(g,0,y,0);if(l)for(w=0;w<u;w++)p[d++]=255*y[w];else{c.getRgbItem(y,0,p,d);d+=u}}h||c.getRgbBuffer(p,0,r,n,i,8,s)}getOutputLength(e,t){return this.base.getOutputLength(e*this.base.numComps/this.numComps,t)}}class PatternCS extends ColorSpace{constructor(e){super("Pattern",null);this.base=e}isDefaultDecode(e,t){unreachable("Should not call PatternCS.isDefaultDecode")}}class IndexedCS extends ColorSpace{constructor(e,t,r){super("Indexed",1);this.base=e;this.highVal=t;const n=e.numComps*(t+1);this.lookup=new Uint8Array(n);if(r instanceof base_stream_BaseStream){const e=r.getBytes(n);this.lookup.set(e)}else{if("string"!=typeof r)throw new FormatError(`IndexedCS - unrecognized lookup table: ${r}`);for(let e=0;e<n;++e)this.lookup[e]=255&r.charCodeAt(e)}}getRgbItem(e,t,r,n){const{base:i,highVal:o,lookup:s}=this,a=MathClamp(Math.round(e[t]),0,o)*i.numComps;i.getRgbBuffer(s,a,1,r,n,8,0)}getRgbBuffer(e,t,r,n,i,o,s){const{base:a,highVal:c,lookup:f}=this,{numComps:u}=a,l=a.getOutputLength(u,s);for(let o=0;o<r;++o){const r=MathClamp(Math.round(e[t++]),0,c)*u;a.getRgbBuffer(f,r,1,n,i,8,s);i+=l}}getOutputLength(e,t){return this.base.getOutputLength(e*this.base.numComps,t)}isDefaultDecode(e,t){if(!Array.isArray(e))return!0;if(2!==e.length){util_warn("Decode map length is not correct");return!0}if(!Number.isInteger(t)||t<1){util_warn("Bits per component is not correct");return!0}return 0===e[0]&&e[1]===(1<<t)-1}}class DeviceGrayCS extends ColorSpace{constructor(){super("DeviceGray",1)}getRgbItem(e,t,r,n){const i=255*e[t];r[n]=r[n+1]=r[n+2]=i}getRgbBuffer(e,t,r,n,i,o,s){const a=255/((1<<o)-1);let c=t,f=i;for(let t=0;t<r;++t){const t=a*e[c++];n[f++]=t;n[f++]=t;n[f++]=t;f+=s}}getOutputLength(e,t){return e*(3+t)}}class DeviceRgbCS extends ColorSpace{constructor(){super("DeviceRGB",3)}getRgbItem(e,t,r,n){r[n]=255*e[t];r[n+1]=255*e[t+1];r[n+2]=255*e[t+2]}getRgbBuffer(e,t,r,n,i,o,s){if(8===o&&0===s){n.set(e.subarray(t,t+3*r),i);return}const a=255/((1<<o)-1);let c=t,f=i;for(let t=0;t<r;++t){n[f++]=a*e[c++];n[f++]=a*e[c++];n[f++]=a*e[c++];f+=s}}getOutputLength(e,t){return e*(3+t)/3|0}isPassthrough(e){return 8===e}}class DeviceRgbaCS extends ColorSpace{constructor(){super("DeviceRGBA",4)}getOutputLength(e,t){return 4*e}isPassthrough(e){return 8===e}fillRgb(e,t,r,n,i,o,s,a,c){r!==i||t!==n?function resizeRgbaImage(e,t,r,n,i,o,s){const a=r/i,c=n/o;let f=0;const u=new Uint16Array(i);if(1===s){for(let e=0;e<i;e++)u[e]=Math.floor(e*a);const n=new Uint32Array(e.buffer),s=new Uint32Array(t.buffer),l=util_FeatureTest.isLittleEndian?16777215:4294967040;for(let e=0;e<o;e++){const t=n.subarray(Math.floor(e*c)*r);for(let e=0;e<i;e++)s[f++]|=t[u[e]]&l}}else{const n=4,s=r*n;for(let e=0;e<i;e++)u[e]=Math.floor(e*a)*n;for(let r=0;r<o;r++){const n=e.subarray(Math.floor(r*c)*s);for(let e=0;e<i;e++){const r=u[e];t[f++]=n[r];t[f++]=n[r+1];t[f++]=n[r+2]}}}}(a,e,t,r,n,i,c):function copyRgbaImage(e,t,r){if(1===r){const r=new Uint32Array(e.buffer),n=new Uint32Array(t.buffer),i=util_FeatureTest.isLittleEndian?16777215:4294967040;for(let e=0,t=r.length;e<t;e++)n[e]|=r[e]&i}else{let r=0;for(let n=0,i=e.length;n<i;n+=4){t[r++]=e[n];t[r++]=e[n+1];t[r++]=e[n+2]}}}(a,e,c)}}class DeviceCmykCS extends ColorSpace{constructor(){super("DeviceCMYK",4)}#n(e,t,r,n,i){const o=e[t]*r,s=e[t+1]*r,a=e[t+2]*r,c=e[t+3]*r;n[i]=255+o*(-4.387332384609988*o+54.48615194189176*s+18.82290502165302*a+212.25662451639585*c-285.2331026137004)+s*(1.7149763477362134*s-5.6096736904047315*a+-17.873870861415444*c-5.497006427196366)+a*(-2.5217340131683033*a-21.248923337353073*c+17.5119270841813)+c*(-21.86122147463605*c-189.48180835922747);n[i+1]=255+o*(8.841041422036149*o+60.118027045597366*s+6.871425592049007*a+31.159100130055922*c-79.2970844816548)+s*(-15.310361306967817*s+17.575251261109482*a+131.35250912493976*c-190.9453302588951)+a*(4.444339102852739*a+9.8632861493405*c-24.86741582555878)+c*(-20.737325471181034*c-187.80453709719578);n[i+2]=255+o*(.8842522430003296*o+8.078677503112928*s+30.89978309703729*a-.23883238689178934*c-14.183576799673286)+s*(10.49593273432072*s+63.02378494754052*a+50.606957656360734*c-112.23884253719248)+a*(.03296041114873217*a+115.60384449646641*c-193.58209356861505)+c*(-22.33816807309886*c-180.12613974708367)}getRgbItem(e,t,r,n){this.#n(e,t,1,r,n)}getRgbBuffer(e,t,r,n,i,o,s){const a=1/((1<<o)-1);for(let o=0;o<r;o++){this.#n(e,t,a,n,i);t+=4;i+=3+s}}getOutputLength(e,t){return e/4*(3+t)|0}}class CalGrayCS extends ColorSpace{constructor(e,t,r){super("CalGray",1);if(!e)throw new FormatError("WhitePoint missing - required for color space CalGray");[this.XW,this.YW,this.ZW]=e;[this.XB,this.YB,this.ZB]=t||[0,0,0];this.G=r||1;if(this.XW<0||this.ZW<0||1!==this.YW)throw new FormatError(`Invalid WhitePoint components for ${this.name}, no fallback available`);if(this.XB<0||this.YB<0||this.ZB<0){info(`Invalid BlackPoint for ${this.name}, falling back to default.`);this.XB=this.YB=this.ZB=0}0===this.XB&&0===this.YB&&0===this.ZB||util_warn(`${this.name}, BlackPoint: XB: ${this.XB}, YB: ${this.YB}, ZB: ${this.ZB}, only default values are supported.`);if(this.G<1){info(`Invalid Gamma: ${this.G} for ${this.name}, falling back to default.`);this.G=1}}#n(e,t,r,n,i){const o=(e[t]*i)**this.G,s=this.YW*o,a=Math.max(295.8*s**.3333333333333333-40.8,0);r[n]=a;r[n+1]=a;r[n+2]=a}getRgbItem(e,t,r,n){this.#n(e,t,r,n,1)}getRgbBuffer(e,t,r,n,i,o,s){const a=1/((1<<o)-1);for(let o=0;o<r;++o){this.#n(e,t,n,i,a);t+=1;i+=3+s}}getOutputLength(e,t){return e*(3+t)}}class CalRGBCS extends ColorSpace{static#i=new Float32Array([.8951,.2664,-.1614,-.7502,1.7135,.0367,.0389,-.0685,1.0296]);static#o=new Float32Array([.9869929,-.1470543,.1599627,.4323053,.5183603,.0492912,-.0085287,.0400428,.9684867]);static#s=new Float32Array([3.2404542,-1.5371385,-.4985314,-.969266,1.8760108,.041556,.0556434,-.2040259,1.0572252]);static#a=new Float32Array([1,1,1]);static#c=new Float32Array(3);static#f=new Float32Array(3);static#u=new Float32Array(3);static#l=(24/116)**3/8;constructor(e,t,r,n){super("CalRGB",3);if(!e)throw new FormatError("WhitePoint missing - required for color space CalRGB");const[i,o,s]=this.whitePoint=e,[a,c,f]=this.blackPoint=t||new Float32Array(3);[this.GR,this.GG,this.GB]=r||new Float32Array([1,1,1]);[this.MXA,this.MYA,this.MZA,this.MXB,this.MYB,this.MZB,this.MXC,this.MYC,this.MZC]=n||new Float32Array([1,0,0,0,1,0,0,0,1]);if(i<0||s<0||1!==o)throw new FormatError(`Invalid WhitePoint components for ${this.name}, no fallback available`);if(a<0||c<0||f<0){info(`Invalid BlackPoint for ${this.name} [${a}, ${c}, ${f}], falling back to default.`);this.blackPoint=new Float32Array(3)}if(this.GR<0||this.GG<0||this.GB<0){info(`Invalid Gamma [${this.GR}, ${this.GG}, ${this.GB}] for ${this.name}, falling back to default.`);this.GR=this.GG=this.GB=1}}#h(e,t,r){r[0]=e[0]*t[0]+e[1]*t[1]+e[2]*t[2];r[1]=e[3]*t[0]+e[4]*t[1]+e[5]*t[2];r[2]=e[6]*t[0]+e[7]*t[1]+e[8]*t[2]}#d(e,t,r){r[0]=1*t[0]/e[0];r[1]=1*t[1]/e[1];r[2]=1*t[2]/e[2]}#p(e,t,r){r[0]=.95047*t[0]/e[0];r[1]=1*t[1]/e[1];r[2]=1.08883*t[2]/e[2]}#m(e){return e<=.0031308?MathClamp(12.92*e,0,1):e>=.99554525?1:MathClamp(1.055*e**(1/2.4)-.055,0,1)}#g(e){return e<0?-this.#g(-e):e>8?((e+16)/116)**3:e*CalRGBCS.#l}#y(e,t,r){if(0===e[0]&&0===e[1]&&0===e[2]){r[0]=t[0];r[1]=t[1];r[2]=t[2];return}const n=this.#g(0),i=(1-n)/(1-this.#g(e[0])),o=1-i,s=(1-n)/(1-this.#g(e[1])),a=1-s,c=(1-n)/(1-this.#g(e[2])),f=1-c;r[0]=t[0]*i+o;r[1]=t[1]*s+a;r[2]=t[2]*c+f}#b(e,t,r){if(1===e[0]&&1===e[2]){r[0]=t[0];r[1]=t[1];r[2]=t[2];return}const n=r;this.#h(CalRGBCS.#i,t,n);const i=CalRGBCS.#c;this.#d(e,n,i);this.#h(CalRGBCS.#o,i,r)}#w(e,t,r){const n=r;this.#h(CalRGBCS.#i,t,n);const i=CalRGBCS.#c;this.#p(e,n,i);this.#h(CalRGBCS.#o,i,r)}#n(e,t,r,n,i){const o=MathClamp(e[t]*i,0,1),s=MathClamp(e[t+1]*i,0,1),a=MathClamp(e[t+2]*i,0,1),c=1===o?1:o**this.GR,f=1===s?1:s**this.GG,u=1===a?1:a**this.GB,l=this.MXA*c+this.MXB*f+this.MXC*u,h=this.MYA*c+this.MYB*f+this.MYC*u,d=this.MZA*c+this.MZB*f+this.MZC*u,p=CalRGBCS.#f;p[0]=l;p[1]=h;p[2]=d;const m=CalRGBCS.#u;this.#b(this.whitePoint,p,m);const g=CalRGBCS.#f;this.#y(this.blackPoint,m,g);const y=CalRGBCS.#u;this.#w(CalRGBCS.#a,g,y);const b=CalRGBCS.#f;this.#h(CalRGBCS.#s,y,b);r[n]=255*this.#m(b[0]);r[n+1]=255*this.#m(b[1]);r[n+2]=255*this.#m(b[2])}getRgbItem(e,t,r,n){this.#n(e,t,r,n,1)}getRgbBuffer(e,t,r,n,i,o,s){const a=1/((1<<o)-1);for(let o=0;o<r;++o){this.#n(e,t,n,i,a);t+=3;i+=3+s}}getOutputLength(e,t){return e*(3+t)/3|0}}class LabCS extends ColorSpace{constructor(e,t,r){super("Lab",3);if(!e)throw new FormatError("WhitePoint missing - required for color space Lab");[this.XW,this.YW,this.ZW]=e;[this.amin,this.amax,this.bmin,this.bmax]=r||[-100,100,-100,100];[this.XB,this.YB,this.ZB]=t||[0,0,0];if(this.XW<0||this.ZW<0||1!==this.YW)throw new FormatError("Invalid WhitePoint components, no fallback available");if(this.XB<0||this.YB<0||this.ZB<0){info("Invalid BlackPoint, falling back to default");this.XB=this.YB=this.ZB=0}if(this.amin>this.amax||this.bmin>this.bmax){info("Invalid Range, falling back to defaults");this.amin=-100;this.amax=100;this.bmin=-100;this.bmax=100}}#v(e){return e>=6/29?e**3:108/841*(e-4/29)}#_(e,t,r,n){return r+e*(n-r)/t}#n(e,t,r,n,i){let o=e[t],s=e[t+1],a=e[t+2];if(!1!==r){o=this.#_(o,r,0,100);s=this.#_(s,r,this.amin,this.amax);a=this.#_(a,r,this.bmin,this.bmax)}s>this.amax?s=this.amax:s<this.amin&&(s=this.amin);a>this.bmax?a=this.bmax:a<this.bmin&&(a=this.bmin);const c=(o+16)/116,f=c+s/500,u=c-a/200,l=this.XW*this.#v(f),h=this.YW*this.#v(c),d=this.ZW*this.#v(u);let p,m,g;if(this.ZW<1){p=3.1339*l+-1.617*h+-.4906*d;m=-.9785*l+1.916*h+.0333*d;g=.072*l+-.229*h+1.4057*d}else{p=3.2406*l+-1.5372*h+-.4986*d;m=-.9689*l+1.8758*h+.0415*d;g=.0557*l+-.204*h+1.057*d}n[i]=255*Math.sqrt(p);n[i+1]=255*Math.sqrt(m);n[i+2]=255*Math.sqrt(g)}getRgbItem(e,t,r,n){this.#n(e,t,!1,r,n)}getRgbBuffer(e,t,r,n,i,o,s){const a=(1<<o)-1;for(let o=0;o<r;o++){this.#n(e,t,a,n,i);t+=3;i+=3+s}}getOutputLength(e,t){return e*(3+t)/3|0}isDefaultDecode(e,t){return!0}get usesZeroToOneRange(){return shadow(this,"usesZeroToOneRange",!1)}}__webpack_require__(4603),__webpack_require__(7566),__webpack_require__(8721);class QCMS{static#x=null;static _memory=null;static _mustAddAlpha=!1;static _destBuffer=null;static _destOffset=0;static _destLength=0;static _cssColor="";static _makeHexColor=null;static get _memoryArray(){const e=this.#x;return e?.byteLength?e:this.#x=new Uint8Array(this._memory.buffer)}}let R;const A="undefined"!=typeof TextDecoder?new TextDecoder("utf-8",{ignoreBOM:!0,fatal:!0}):{decode:()=>{throw Error("TextDecoder not available")}};"undefined"!=typeof TextDecoder&&A.decode();let S=null;function getUint8ArrayMemory0(){null!==S&&0!==S.byteLength||(S=new Uint8Array(R.memory.buffer));return S}let B=0;function passArray8ToWasm0(e,t){const r=t(1*e.length,1)>>>0;getUint8ArrayMemory0().set(e,r/1);B=e.length;return r}const I=Object.freeze({RGB8:0,0:"RGB8",RGBA8:1,1:"RGBA8",BGRA8:2,2:"BGRA8",Gray8:3,3:"Gray8",GrayA8:4,4:"GrayA8",CMYK:5,5:"CMYK"}),E=Object.freeze({Perceptual:0,0:"Perceptual",RelativeColorimetric:1,1:"RelativeColorimetric",Saturation:2,2:"Saturation",AbsoluteColorimetric:3,3:"AbsoluteColorimetric"});function __wbg_get_imports(){const e={wbg:{}};e.wbg.__wbg_copyresult_b08ee7d273f295dd=function(e,t){!function copy_result(e,t){const{_mustAddAlpha:r,_destBuffer:n,_destOffset:i,_destLength:o,_memoryArray:s}=QCMS;if(t!==o)if(r)for(let r=e,o=e+t,a=i;r<o;r+=3,a+=4){n[a]=s[r];n[a+1]=s[r+1];n[a+2]=s[r+2];n[a+3]=255}else for(let r=e,o=e+t,a=i;r<o;r+=3,a+=4){n[a]=s[r];n[a+1]=s[r+1];n[a+2]=s[r+2]}else n.set(s.subarray(e,e+t),i)}(e>>>0,t>>>0)};e.wbg.__wbg_copyrgb_d60ce17bb05d9b67=function(e){!function copy_rgb(e){const{_destBuffer:t,_destOffset:r,_memoryArray:n}=QCMS;t[r]=n[e];t[r+1]=n[e+1];t[r+2]=n[e+2]}(e>>>0)};e.wbg.__wbg_makecssRGB_893bf0cd9fdb302d=function(e){!function make_cssRGB(e){const{_memoryArray:t}=QCMS;QCMS._cssColor=QCMS._makeHexColor(t[e],t[e+1],t[e+2])}(e>>>0)};e.wbg.__wbindgen_init_externref_table=function(){const e=R.__wbindgen_export_0,t=e.grow(4);e.set(0,void 0);e.set(t+0,void 0);e.set(t+1,null);e.set(t+2,!0);e.set(t+3,!1)};e.wbg.__wbindgen_throw=function(e,t){throw new Error(function getStringFromWasm0(e,t){e>>>=0;return A.decode(getUint8ArrayMemory0().subarray(e,e+t))}(e,t))};return e}function __wbg_finalize_init(e,t){R=e.exports;__wbg_init.__wbindgen_wasm_module=t;S=null;R.__wbindgen_start();return R}async function __wbg_init(e){if(void 0!==R)return R;void 0!==e&&(Object.getPrototypeOf(e)===Object.prototype?({module_or_path:e}=e):console.warn("using deprecated parameters for the initialization function; pass a single object instead"));const t=__wbg_get_imports();("string"==typeof e||"function"==typeof Request&&e instanceof Request||"function"==typeof URL&&e instanceof URL)&&(e=fetch(e));const{instance:r,module:n}=await async function __wbg_load(e,t){if("function"==typeof Response&&e instanceof Response){if("function"==typeof WebAssembly.instantiateStreaming)try{return await WebAssembly.instantiateStreaming(e,t)}catch(t){if("application/wasm"==e.headers.get("Content-Type"))throw t;console.warn("`WebAssembly.instantiateStreaming` failed because your server does not serve Wasm with `application/wasm` MIME type. Falling back to `WebAssembly.instantiate` which is slower. Original error:\n",t)}const r=await e.arrayBuffer();return await WebAssembly.instantiate(r,t)}{const r=await WebAssembly.instantiate(e,t);return r instanceof WebAssembly.Instance?{instance:r,module:e}:r}}(await e,t);return __wbg_finalize_init(r,n)}function fetchSync(e){const t=new XMLHttpRequest;t.open("GET",e,!1);t.responseType="arraybuffer";t.send(null);return t.response}class IccColorSpace extends ColorSpace{#C;#R;static#A=!0;static#S=null;static#B=new FinalizationRegistry((e=>{!function qcms_drop_transformer(e){R.qcms_drop_transformer(e)}(e)}));constructor(e,t,r){if(!IccColorSpace.isUsable)throw new Error("No ICC color space support");super(t,r);let n;switch(r){case 1:n=I.Gray8;this.#R=(e,t,r)=>function qcms_convert_one(e,t,r){R.qcms_convert_one(e,t,r)}(this.#C,255*e[t],r);break;case 3:n=I.RGB8;this.#R=(e,t,r)=>function qcms_convert_three(e,t,r,n,i){R.qcms_convert_three(e,t,r,n,i)}(this.#C,255*e[t],255*e[t+1],255*e[t+2],r);break;case 4:n=I.CMYK;this.#R=(e,t,r)=>function qcms_convert_four(e,t,r,n,i,o){R.qcms_convert_four(e,t,r,n,i,o)}(this.#C,255*e[t],255*e[t+1],255*e[t+2],255*e[t+3],r);break;default:throw new Error(`Unsupported number of components: ${r}`)}this.#C=function qcms_transformer_from_memory(e,t,r){const n=passArray8ToWasm0(e,R.__wbindgen_malloc),i=B;return R.qcms_transformer_from_memory(n,i,t,r)>>>0}(e,n,E.Perceptual);if(!this.#C)throw new Error("Failed to create ICC color space");IccColorSpace.#B.register(this,this.#C)}getRgbHex(e,t){this.#R(e,t,!0);return QCMS._cssColor}getRgbItem(e,t,r,n){QCMS._destBuffer=r;QCMS._destOffset=n;QCMS._destLength=3;this.#R(e,t,!1);QCMS._destBuffer=null}getRgbBuffer(e,t,r,n,i,o,s){e=e.subarray(t,t+r*this.numComps);if(8!==o){const t=255/((1<<o)-1);for(let r=0,n=e.length;r<n;r++)e[r]*=t}QCMS._mustAddAlpha=s&&n.buffer===e.buffer;QCMS._destBuffer=n;QCMS._destOffset=i;QCMS._destLength=r*(3+s);!function qcms_convert_array(e,t){const r=passArray8ToWasm0(t,R.__wbindgen_malloc),n=B;R.qcms_convert_array(e,r,n)}(this.#C,e);QCMS._mustAddAlpha=!1;QCMS._destBuffer=null}getOutputLength(e,t){return e/this.numComps*(3+t)|0}static setOptions({useWasm:e,useWorkerFetch:t,wasmUrl:r}){if(t){this.#A=e;this.#S=r}else this.#A=!1}static get isUsable(){let e=!1;if(this.#A)if(this.#S)try{this._module=function initSync(e){if(void 0!==R)return R;void 0!==e&&(Object.getPrototypeOf(e)===Object.prototype?({module:e}=e):console.warn("using deprecated parameters for `initSync()`; pass a single object instead"));const t=__wbg_get_imports();e instanceof WebAssembly.Module||(e=new WebAssembly.Module(e));return __wbg_finalize_init(new WebAssembly.Instance(e,t),e)}({module:fetchSync(`${this.#S}qcms_bg.wasm`)});e=!!this._module;QCMS._memory=this._module.memory;QCMS._makeHexColor=util_Util.makeHexColor}catch(e){util_warn(`ICCBased color space: "${e}".`)}else util_warn("No ICC color space support due to missing `wasmUrl` API option");return shadow(this,"isUsable",e)}}class CmykICCBasedCS extends IccColorSpace{static#I;constructor(){super(new Uint8Array(fetchSync(`${CmykICCBasedCS.#I}CGATS001Compat-v2-micro.icc`)),"DeviceCMYK",4)}static setOptions({iccUrl:e}){this.#I=e}static get isUsable(){let e=!1;IccColorSpace.isUsable&&(this.#I?e=!0:util_warn("No CMYK ICC profile support due to missing `iccUrl` API option"));return shadow(this,"isUsable",e)}}class ColorSpaceUtils{static parse({cs:e,xref:t,resources:r=null,pdfFunctionFactory:n,globalColorSpaceCache:i,localColorSpaceCache:o,asyncIfNotCached:s=!1}){const a={xref:t,resources:r,pdfFunctionFactory:n,globalColorSpaceCache:i,localColorSpaceCache:o};let c,f,u;if(e instanceof primitives_Ref){f=e;const r=i.getByRef(f)||o.getByRef(f);if(r)return r;e=t.fetch(e)}if(e instanceof Name){c=e.name;const t=o.getByName(c);if(t)return t}try{u=this.#E(e,a)}catch(e){if(s&&!(e instanceof MissingDataException))return Promise.reject(e);throw e}if(c||f){o.set(c,f,u);f&&i.set(null,f,u)}return s?Promise.resolve(u):u}static#k(e,t){const{globalColorSpaceCache:r}=t;let n;if(e instanceof primitives_Ref){n=e;const t=r.getByRef(n);if(t)return t}const i=this.#E(e,t);n&&r.set(null,n,i);return i}static#E(e,t){const{xref:r,resources:n,pdfFunctionFactory:i,globalColorSpaceCache:o}=t;if((e=r.fetchIfRef(e))instanceof Name)switch(e.name){case"G":case"DeviceGray":return this.gray;case"RGB":case"DeviceRGB":return this.rgb;case"DeviceRGBA":return this.rgba;case"CMYK":case"DeviceCMYK":return this.cmyk;case"Pattern":return new PatternCS(null);default:if(n instanceof primitives_Dict){const r=n.get("ColorSpace");if(r instanceof primitives_Dict){const n=r.get(e.name);if(n){if(n instanceof Name)return this.#E(n,t);e=n;break}}}util_warn(`Unrecognized ColorSpace: ${e.name}`);return this.gray}if(Array.isArray(e)){const n=r.fetchIfRef(e[0]).name;let s,a,c,f,u,l;switch(n){case"G":case"DeviceGray":return this.gray;case"RGB":case"DeviceRGB":return this.rgb;case"CMYK":case"DeviceCMYK":return this.cmyk;case"CalGray":s=r.fetchIfRef(e[1]);f=s.getArray("WhitePoint");u=s.getArray("BlackPoint");l=s.get("Gamma");return new CalGrayCS(f,u,l);case"CalRGB":s=r.fetchIfRef(e[1]);f=s.getArray("WhitePoint");u=s.getArray("BlackPoint");l=s.getArray("Gamma");const h=s.getArray("Matrix");return new CalRGBCS(f,u,l,h);case"ICCBased":const d=e[1]instanceof primitives_Ref;if(d){const t=o.getByRef(e[1]);if(t)return t}const p=r.fetchIfRef(e[1]),m=p.dict;a=m.get("N");if(IccColorSpace.isUsable)try{const t=new IccColorSpace(p.getBytes(),"ICCBased",a);d&&o.set(null,e[1],t);return t}catch(t){if(t instanceof MissingDataException)throw t;util_warn(`ICCBased color space (${e[1]}): "${t}".`)}const g=m.getRaw("Alternate");if(g){const e=this.#k(g,t);if(e.numComps===a)return e;util_warn("ICCBased color space: Ignoring incorrect /Alternate entry.")}if(1===a)return this.gray;if(3===a)return this.rgb;if(4===a)return this.cmyk;break;case"Pattern":c=e[1]||null;c&&(c=this.#k(c,t));return new PatternCS(c);case"I":case"Indexed":c=this.#k(e[1],t);const y=MathClamp(r.fetchIfRef(e[2]),0,255),b=r.fetchIfRef(e[3]);return new IndexedCS(c,y,b);case"Separation":case"DeviceN":const w=r.fetchIfRef(e[1]);a=Array.isArray(w)?w.length:1;c=this.#k(e[2],t);const v=i.create(e[3]);return new AlternateCS(a,c,v);case"Lab":s=r.fetchIfRef(e[1]);f=s.getArray("WhitePoint");u=s.getArray("BlackPoint");const _=s.getArray("Range");return new LabCS(f,u,_);default:util_warn(`Unimplemented ColorSpace object: ${n}`);return this.gray}}util_warn(`Unrecognized ColorSpace object: ${e}`);return this.gray}static get gray(){return shadow(this,"gray",new DeviceGrayCS)}static get rgb(){return shadow(this,"rgb",new DeviceRgbCS)}static get rgba(){return shadow(this,"rgba",new DeviceRgbaCS)}static get cmyk(){if(CmykICCBasedCS.isUsable)try{return shadow(this,"cmyk",new CmykICCBasedCS)}catch{util_warn("CMYK fallback: DeviceCMYK")}return shadow(this,"cmyk",new DeviceCmykCS)}}class JpegError extends i{constructor(e){super(e,"JpegError")}}class DNLMarkerError extends i{constructor(e,t){super(e,"DNLMarkerError");this.scanLines=t}}class EOIMarkerError extends i{constructor(e){super(e,"EOIMarkerError")}}const k=new Uint8Array([0,1,8,16,9,2,3,10,17,24,32,25,18,11,4,5,12,19,26,33,40,48,41,34,27,20,13,6,7,14,21,28,35,42,49,56,57,50,43,36,29,22,15,23,30,37,44,51,58,59,52,45,38,31,39,46,53,60,61,54,47,55,62,63]),D=4017,T=799,M=3406,P=2276,O=1567,U=3784,L=5793,N=2896;function buildHuffmanTable(e,t){let r,n,i=0,o=16;for(;o>0&&!e[o-1];)o--;const s=[{children:[],index:0}];let a,c=s[0];for(r=0;r<o;r++){for(n=0;n<e[r];n++){c=s.pop();c.children[c.index]=t[i];for(;c.index>0;)c=s.pop();c.index++;s.push(c);for(;s.length<=r;){s.push(a={children:[],index:0});c.children[c.index]=a.children;c=a}i++}if(r+1<o){s.push(a={children:[],index:0});c.children[c.index]=a.children;c=a}}return s[0].children}function getBlockBufferOffset(e,t,r){return 64*((e.blocksPerLine+1)*t+r)}function decodeScan(e,t,r,n,i,o,s,a,c,f=!1){const u=r.mcusPerLine,l=r.progressive,h=t;let d=0,p=0;function readBit(){if(p>0){p--;return d>>p&1}d=e[t++];if(255===d){const n=e[t++];if(n){if(220===n&&f){const n=readUint16(e,t+=2);t+=2;if(n>0&&n!==r.scanLines)throw new DNLMarkerError("Found DNL marker (0xFFDC) while parsing scan data",n)}else if(217===n){if(f){const e=b*(8===r.precision?8:0);if(e>0&&Math.round(r.scanLines/e)>=5)throw new DNLMarkerError("Found EOI marker (0xFFD9) while parsing scan data, possibly caused by incorrect `scanLines` parameter",e)}throw new EOIMarkerError("Found EOI marker (0xFFD9) while parsing scan data")}throw new JpegError(`unexpected marker ${(d<<8|n).toString(16)}`)}}p=7;return d>>>7}function decodeHuffman(e){let t=e;for(;;){t=t[readBit()];switch(typeof t){case"number":return t;case"object":continue}throw new JpegError("invalid huffman sequence")}}function receive(e){let t=0;for(;e>0;){t=t<<1|readBit();e--}return t}function receiveAndExtend(e){if(1===e)return 1===readBit()?1:-1;const t=receive(e);return t>=1<<e-1?t:t+(-1<<e)+1}let m=0;let g,y=0;let b=0;function decodeMcu(e,t,r,n,i){const o=r%u;b=(r/u|0)*e.v+n;const s=o*e.h+i;t(e,getBlockBufferOffset(e,b,s))}function decodeBlock(e,t,r){b=r/e.blocksPerLine|0;const n=r%e.blocksPerLine;t(e,getBlockBufferOffset(e,b,n))}const w=n.length;let v,_,x,C,R,A;A=l?0===o?0===a?function decodeDCFirst(e,t){const r=decodeHuffman(e.huffmanTableDC),n=0===r?0:receiveAndExtend(r)<<c;e.blockData[t]=e.pred+=n}:function decodeDCSuccessive(e,t){e.blockData[t]|=readBit()<<c}:0===a?function decodeACFirst(e,t){if(m>0){m--;return}let r=o;const n=s;for(;r<=n;){const n=decodeHuffman(e.huffmanTableAC),i=15&n,o=n>>4;if(0===i){if(o<15){m=receive(o)+(1<<o)-1;break}r+=16;continue}r+=o;const s=k[r];e.blockData[t+s]=receiveAndExtend(i)*(1<<c);r++}}:function decodeACSuccessive(e,t){let r=o;const n=s;let i,a,f=0;for(;r<=n;){const n=t+k[r],o=e.blockData[n]<0?-1:1;switch(y){case 0:a=decodeHuffman(e.huffmanTableAC);i=15&a;f=a>>4;if(0===i)if(f<15){m=receive(f)+(1<<f);y=4}else{f=16;y=1}else{if(1!==i)throw new JpegError("invalid ACn encoding");g=receiveAndExtend(i);y=f?2:3}continue;case 1:case 2:if(e.blockData[n])e.blockData[n]+=o*(readBit()<<c);else{f--;0===f&&(y=2===y?3:0)}break;case 3:if(e.blockData[n])e.blockData[n]+=o*(readBit()<<c);else{e.blockData[n]=g<<c;y=0}break;case 4:e.blockData[n]&&(e.blockData[n]+=o*(readBit()<<c))}r++}if(4===y){m--;0===m&&(y=0)}}:function decodeBaseline(e,t){const r=decodeHuffman(e.huffmanTableDC),n=0===r?0:receiveAndExtend(r);e.blockData[t]=e.pred+=n;let i=1;for(;i<64;){const r=decodeHuffman(e.huffmanTableAC),n=15&r,o=r>>4;if(0===n){if(o<15)break;i+=16;continue}i+=o;const s=k[i];e.blockData[t+s]=receiveAndExtend(n);i++}};let S,B=0;const I=1===w?n[0].blocksPerLine*n[0].blocksPerColumn:u*r.mcusPerColumn;let E,D;for(;B<=I;){const r=i?Math.min(I-B,i):I;if(r>0){for(_=0;_<w;_++)n[_].pred=0;m=0;if(1===w){v=n[0];for(R=0;R<r;R++){decodeBlock(v,A,B);B++}}else for(R=0;R<r;R++){for(_=0;_<w;_++){v=n[_];E=v.h;D=v.v;for(x=0;x<D;x++)for(C=0;C<E;C++)decodeMcu(v,A,B,x,C)}B++}}p=0;S=findNextFileMarker(e,t);if(!S)break;if(S.invalid){util_warn(`decodeScan - ${r>0?"unexpected":"excessive"} MCU data, current marker is: ${S.invalid}`);t=S.offset}if(!(S.marker>=65488&&S.marker<=65495))break;t+=2}return t-h}function quantizeAndInverse(e,t,r){const n=e.quantizationTable,i=e.blockData;let o,s,a,c,f,u,l,h,d,p,m,g,y,b,w,v,_;if(!n)throw new JpegError("missing required Quantization Table.");for(let e=0;e<64;e+=8){d=i[t+e];p=i[t+e+1];m=i[t+e+2];g=i[t+e+3];y=i[t+e+4];b=i[t+e+5];w=i[t+e+6];v=i[t+e+7];d*=n[e];if(p|m|g|y|b|w|v){p*=n[e+1];m*=n[e+2];g*=n[e+3];y*=n[e+4];b*=n[e+5];w*=n[e+6];v*=n[e+7];o=L*d+128>>8;s=L*y+128>>8;a=m;c=w;f=N*(p-v)+128>>8;h=N*(p+v)+128>>8;u=g<<4;l=b<<4;o=o+s+1>>1;s=o-s;_=a*U+c*O+128>>8;a=a*O-c*U+128>>8;c=_;f=f+l+1>>1;l=f-l;h=h+u+1>>1;u=h-u;o=o+c+1>>1;c=o-c;s=s+a+1>>1;a=s-a;_=f*P+h*M+2048>>12;f=f*M-h*P+2048>>12;h=_;_=u*T+l*D+2048>>12;u=u*D-l*T+2048>>12;l=_;r[e]=o+h;r[e+7]=o-h;r[e+1]=s+l;r[e+6]=s-l;r[e+2]=a+u;r[e+5]=a-u;r[e+3]=c+f;r[e+4]=c-f}else{_=L*d+512>>10;r[e]=_;r[e+1]=_;r[e+2]=_;r[e+3]=_;r[e+4]=_;r[e+5]=_;r[e+6]=_;r[e+7]=_}}for(let e=0;e<8;++e){d=r[e];p=r[e+8];m=r[e+16];g=r[e+24];y=r[e+32];b=r[e+40];w=r[e+48];v=r[e+56];if(p|m|g|y|b|w|v){o=L*d+2048>>12;s=L*y+2048>>12;a=m;c=w;f=N*(p-v)+2048>>12;h=N*(p+v)+2048>>12;u=g;l=b;o=4112+(o+s+1>>1);s=o-s;_=a*U+c*O+2048>>12;a=a*O-c*U+2048>>12;c=_;f=f+l+1>>1;l=f-l;h=h+u+1>>1;u=h-u;o=o+c+1>>1;c=o-c;s=s+a+1>>1;a=s-a;_=f*P+h*M+2048>>12;f=f*M-h*P+2048>>12;h=_;_=u*T+l*D+2048>>12;u=u*D-l*T+2048>>12;l=_;d=o+h;v=o-h;p=s+l;w=s-l;m=a+u;b=a-u;g=c+f;y=c-f;d<16?d=0:d>=4080?d=255:d>>=4;p<16?p=0:p>=4080?p=255:p>>=4;m<16?m=0:m>=4080?m=255:m>>=4;g<16?g=0:g>=4080?g=255:g>>=4;y<16?y=0:y>=4080?y=255:y>>=4;b<16?b=0:b>=4080?b=255:b>>=4;w<16?w=0:w>=4080?w=255:w>>=4;v<16?v=0:v>=4080?v=255:v>>=4;i[t+e]=d;i[t+e+8]=p;i[t+e+16]=m;i[t+e+24]=g;i[t+e+32]=y;i[t+e+40]=b;i[t+e+48]=w;i[t+e+56]=v}else{_=L*d+8192>>14;_=_<-2040?0:_>=2024?255:_+2056>>4;i[t+e]=_;i[t+e+8]=_;i[t+e+16]=_;i[t+e+24]=_;i[t+e+32]=_;i[t+e+40]=_;i[t+e+48]=_;i[t+e+56]=_}}}function buildComponentData(e,t){const r=t.blocksPerLine,n=t.blocksPerColumn,i=new Int16Array(64);for(let e=0;e<n;e++)for(let n=0;n<r;n++){quantizeAndInverse(t,getBlockBufferOffset(t,e,n),i)}return t.blockData}function findNextFileMarker(e,t,r=t){const n=e.length-1;let i=r<t?r:t;if(t>=n)return null;const o=readUint16(e,t);if(o>=65472&&o<=65534)return{invalid:null,marker:o,offset:t};let s=readUint16(e,i);for(;!(s>=65472&&s<=65534);){if(++i>=n)return null;s=readUint16(e,i)}return{invalid:o.toString(16),marker:s,offset:i}}function prepareComponents(e){const t=Math.ceil(e.samplesPerLine/8/e.maxH),r=Math.ceil(e.scanLines/8/e.maxV);for(const n of e.components){const i=Math.ceil(Math.ceil(e.samplesPerLine/8)*n.h/e.maxH),o=Math.ceil(Math.ceil(e.scanLines/8)*n.v/e.maxV),s=t*n.h,a=64*(r*n.v)*(s+1);n.blockData=new Int16Array(a);n.blocksPerLine=i;n.blocksPerColumn=o}e.mcusPerLine=t;e.mcusPerColumn=r}function readDataBlock(e,t){const r=readUint16(e,t);let n=(t+=2)+r-2;const i=findNextFileMarker(e,n,t);if(i?.invalid){util_warn("readDataBlock - incorrect length, current marker is: "+i.invalid);n=i.offset}const o=e.subarray(t,n);return{appData:o,oldOffset:t,newOffset:t+o.length}}function skipData(e,t){const r=readUint16(e,t),n=(t+=2)+r-2,i=findNextFileMarker(e,n,t);return i?.invalid?i.offset:n}class JpegImage{constructor({decodeTransform:e=null,colorTransform:t=-1}={}){this._decodeTransform=e;this._colorTransform=t}static canUseImageDecoder(e,t=-1){let r=null,n=0,i=null,o=readUint16(e,n);n+=2;if(65496!==o)throw new JpegError("SOI not found");o=readUint16(e,n);n+=2;e:for(;65497!==o;){switch(o){case 65505:const{appData:t,oldOffset:s,newOffset:a}=readDataBlock(e,n);n=a;if(69===t[0]&&120===t[1]&&105===t[2]&&102===t[3]&&0===t[4]&&0===t[5]){if(r)throw new JpegError("Duplicate EXIF-blocks found.");r={exifStart:s+6,exifEnd:a}}o=readUint16(e,n);n+=2;continue;case 65472:case 65473:case 65474:i=e[n+7];break e;case 65535:255!==e[n]&&n--}n=skipData(e,n);o=readUint16(e,n);n+=2}return 4===i||3===i&&0===t?null:r||{}}parse(e,{dnlScanLines:t=null}={}){let r,n,i=0,o=null,s=null,a=0;const c=[],f=[],u=[];let l=readUint16(e,i);i+=2;if(65496!==l)throw new JpegError("SOI not found");l=readUint16(e,i);i+=2;e:for(;65497!==l;){let h,d,p;switch(l){case 65504:case 65505:case 65506:case 65507:case 65508:case 65509:case 65510:case 65511:case 65512:case 65513:case 65514:case 65515:case 65516:case 65517:case 65518:case 65519:case 65534:const{appData:m,newOffset:g}=readDataBlock(e,i);i=g;65504===l&&74===m[0]&&70===m[1]&&73===m[2]&&70===m[3]&&0===m[4]&&(o={version:{major:m[5],minor:m[6]},densityUnits:m[7],xDensity:m[8]<<8|m[9],yDensity:m[10]<<8|m[11],thumbWidth:m[12],thumbHeight:m[13],thumbData:m.subarray(14,14+3*m[12]*m[13])});65518===l&&65===m[0]&&100===m[1]&&111===m[2]&&98===m[3]&&101===m[4]&&(s={version:m[5]<<8|m[6],flags0:m[7]<<8|m[8],flags1:m[9]<<8|m[10],transformCode:m[11]});break;case 65499:const y=readUint16(e,i);i+=2;const b=y+i-2;let w;for(;i<b;){const t=e[i++],r=new Uint16Array(64);if(t>>4){if(t>>4!=1)throw new JpegError("DQT - invalid table spec");for(d=0;d<64;d++){w=k[d];r[w]=readUint16(e,i);i+=2}}else for(d=0;d<64;d++){w=k[d];r[w]=e[i++]}c[15&t]=r}break;case 65472:case 65473:case 65474:if(r)throw new JpegError("Only single frame JPEGs supported");i+=2;r={};r.extended=65473===l;r.progressive=65474===l;r.precision=e[i++];const v=readUint16(e,i);i+=2;r.scanLines=t||v;r.samplesPerLine=readUint16(e,i);i+=2;r.components=[];r.componentIds={};const _=e[i++];let x=0,C=0;for(h=0;h<_;h++){const t=e[i],n=e[i+1]>>4,o=15&e[i+1];x<n&&(x=n);C<o&&(C=o);const s=e[i+2];p=r.components.push({h:n,v:o,quantizationId:s,quantizationTable:null});r.componentIds[t]=p-1;i+=3}r.maxH=x;r.maxV=C;prepareComponents(r);break;case 65476:const R=readUint16(e,i);i+=2;for(h=2;h<R;){const t=e[i++],r=new Uint8Array(16);let n=0;for(d=0;d<16;d++,i++)n+=r[d]=e[i];const o=new Uint8Array(n);for(d=0;d<n;d++,i++)o[d]=e[i];h+=17+n;(t>>4?f:u)[15&t]=buildHuffmanTable(r,o)}break;case 65501:i+=2;n=readUint16(e,i);i+=2;break;case 65498:const A=1==++a&&!t;i+=2;const S=e[i++],B=[];for(h=0;h<S;h++){const t=e[i++],n=r.componentIds[t],o=r.components[n];o.index=t;const s=e[i++];o.huffmanTableDC=u[s>>4];o.huffmanTableAC=f[15&s];B.push(o)}const I=e[i++],E=e[i++],D=e[i++];try{i+=decodeScan(e,i,r,B,n,I,E,D>>4,15&D,A)}catch(t){if(t instanceof DNLMarkerError){util_warn(`${t.message} -- attempting to re-parse the JPEG image.`);return this.parse(e,{dnlScanLines:t.scanLines})}if(t instanceof EOIMarkerError){util_warn(`${t.message} -- ignoring the rest of the image data.`);break e}throw t}break;case 65500:i+=4;break;case 65535:255!==e[i]&&i--;break;default:const T=findNextFileMarker(e,i-2,i-3);if(T?.invalid){util_warn("JpegImage.parse - unexpected data, current marker is: "+T.invalid);i=T.offset;break}if(!T||i>=e.length-1){util_warn("JpegImage.parse - reached the end of the image data without finding an EOI marker (0xFFD9).");break e}throw new JpegError("JpegImage.parse - unknown marker: "+l.toString(16))}l=readUint16(e,i);i+=2}if(!r)throw new JpegError("JpegImage.parse - no frame data found.");this.width=r.samplesPerLine;this.height=r.scanLines;this.jfif=o;this.adobe=s;this.components=[];for(const e of r.components){const t=c[e.quantizationId];t&&(e.quantizationTable=t);this.components.push({index:e.index,output:buildComponentData(0,e),scaleX:e.h/r.maxH,scaleY:e.v/r.maxV,blocksPerLine:e.blocksPerLine,blocksPerColumn:e.blocksPerColumn})}this.numComponents=this.components.length}_getLinearizedBlockData(e,t,r=!1){const n=this.width/e,i=this.height/t;let o,s,a,c,f,u,l,h,d,p,m,g=0;const y=this.components.length,b=e*t*y,w=new Uint8ClampedArray(b),v=new Uint32Array(e),_=4294967288;let x;for(l=0;l<y;l++){o=this.components[l];s=o.scaleX*n;a=o.scaleY*i;g=l;m=o.output;c=o.blocksPerLine+1<<3;if(s!==x){for(f=0;f<e;f++){h=0|f*s;v[f]=(h&_)<<3|7&h}x=s}for(u=0;u<t;u++){h=0|u*a;p=c*(h&_)|(7&h)<<3;for(f=0;f<e;f++){w[g]=m[p+v[f]];g+=y}}}let C=this._decodeTransform;r||4!==y||C||(C=new Int32Array([-256,255,-256,255,-256,255,-256,255]));if(C)for(l=0;l<b;)for(h=0,d=0;h<y;h++,l++,d+=2)w[l]=(w[l]*C[d]>>8)+C[d+1];return w}get _isColorConversionNeeded(){return this.adobe?!!this.adobe.transformCode:3===this.numComponents?0!==this._colorTransform&&(82!==this.components[0].index||71!==this.components[1].index||66!==this.components[2].index):1===this._colorTransform}_convertYccToRgb(e){let t,r,n;for(let i=0,o=e.length;i<o;i+=3){t=e[i];r=e[i+1];n=e[i+2];e[i]=t-179.456+1.402*n;e[i+1]=t+135.459-.344*r-.714*n;e[i+2]=t-226.816+1.772*r}return e}_convertYccToRgba(e,t){for(let r=0,n=0,i=e.length;r<i;r+=3,n+=4){const i=e[r],o=e[r+1],s=e[r+2];t[n]=i-179.456+1.402*s;t[n+1]=i+135.459-.344*o-.714*s;t[n+2]=i-226.816+1.772*o;t[n+3]=255}return t}_convertYcckToRgb(e){this._convertYcckToCmyk(e);return this._convertCmykToRgb(e)}_convertYcckToRgba(e){this._convertYcckToCmyk(e);return this._convertCmykToRgba(e)}_convertYcckToCmyk(e){let t,r,n;for(let i=0,o=e.length;i<o;i+=4){t=e[i];r=e[i+1];n=e[i+2];e[i]=434.456-t-1.402*n;e[i+1]=119.541-t+.344*r+.714*n;e[i+2]=481.816-t-1.772*r}return e}_convertCmykToRgb(e){const t=e.length/4;ColorSpaceUtils.cmyk.getRgbBuffer(e,0,t,e,0,8,0);return e.subarray(0,3*t)}_convertCmykToRgba(e){ColorSpaceUtils.cmyk.getRgbBuffer(e,0,e.length/4,e,0,8,1);if(ColorSpaceUtils.cmyk instanceof DeviceCmykCS)for(let t=3,r=e.length;t<r;t+=4)e[t]=255;return e}getData({width:e,height:t,forceRGBA:r=!1,forceRGB:n=!1,isSourcePDF:i=!1}){if(this.numComponents>4)throw new JpegError("Unsupported color mode");const o=this._getLinearizedBlockData(e,t,i);if(1===this.numComponents&&(r||n)){const e=o.length*(r?4:3),t=new Uint8ClampedArray(e);let n=0;if(r)!function grayToRGBA(e,t){if(util_FeatureTest.isLittleEndian)for(let r=0,n=e.length;r<n;r++)t[r]=65793*e[r]|4278190080;else for(let r=0,n=e.length;r<n;r++)t[r]=16843008*e[r]|255}(o,new Uint32Array(t.buffer));else for(const e of o){t[n++]=e;t[n++]=e;t[n++]=e}return t}if(3===this.numComponents&&this._isColorConversionNeeded){if(r){const e=new Uint8ClampedArray(o.length/3*4);return this._convertYccToRgba(o,e)}return this._convertYccToRgb(o)}if(4===this.numComponents){if(this._isColorConversionNeeded)return r?this._convertYcckToRgba(o):n?this._convertYcckToRgb(o):this._convertYcckToCmyk(o);if(r)return this._convertCmykToRgba(o);if(n)return this._convertCmykToRgb(o)}return o}}__webpack_require__(4628);var OpenJPEG=async function(e={}){var t,r,n=e,i=new Promise(((e,n)=>{t=e;r=n})),o="./this.program",quit_=(e,t)=>{throw t},s=import.meta.url;try{new URL(".",s).href}catch{}var a,c,f,u,l,h,d=console.log.bind(console),p=console.error.bind(console),m=!1;function updateMemoryViews(){var e=a.buffer;f=new Int8Array(e);new Int16Array(e);u=new Uint8Array(e);new Uint16Array(e);l=new Int32Array(e);h=new Uint32Array(e);new Float32Array(e);new Float64Array(e);new BigInt64Array(e);new BigUint64Array(e)}var g=0,y=null;class ExitStatus{name="ExitStatus";constructor(e){this.message=`Program terminated with exit(${e})`;this.status=e}}var callRuntimeCallbacks=e=>{for(;e.length>0;)e.shift()(n)},b=[],addOnPostRun=e=>b.push(e),w=[],addOnPreRun=e=>w.push(e),v=!0,_=0,x={},handleException=e=>{if(e instanceof ExitStatus||"unwind"==e)return c;quit_(0,e)},keepRuntimeAlive=()=>v||_>0,_proc_exit=e=>{c=e;if(!keepRuntimeAlive()){n.onExit?.(e);m=!0}quit_(0,new ExitStatus(e))},_exit=(e,t)=>{c=e;_proc_exit(e)},callUserCallback=e=>{if(!m)try{e();(()=>{if(!keepRuntimeAlive())try{_exit(c)}catch(e){handleException(e)}})()}catch(e){handleException(e)}},growMemory=e=>{var t=(e-a.buffer.byteLength+65535)/65536|0;try{a.grow(t);updateMemoryViews();return 1}catch(e){}},C={},getEnvStrings=()=>{if(!getEnvStrings.strings){var e={USER:"web_user",LOGNAME:"web_user",PATH:"/",PWD:"/",HOME:"/home/<USER>",LANG:("object"==typeof navigator&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8",_:o||"./this.program"};for(var t in C)void 0===C[t]?delete e[t]:e[t]=C[t];var r=[];for(var t in e)r.push(`${t}=${e[t]}`);getEnvStrings.strings=r}return getEnvStrings.strings},lengthBytesUTF8=e=>{for(var t=0,r=0;r<e.length;++r){var n=e.charCodeAt(r);if(n<=127)t++;else if(n<=2047)t+=2;else if(n>=55296&&n<=57343){t+=4;++r}else t+=3}return t},R=[null,[],[]],A="undefined"!=typeof TextDecoder?new TextDecoder:void 0,UTF8ArrayToString=(e,t=0,r=NaN)=>{for(var n=t+r,i=t;e[i]&&!(i>=n);)++i;if(i-t>16&&e.buffer&&A)return A.decode(e.subarray(t,i));for(var o="";t<i;){var s=e[t++];if(128&s){var a=63&e[t++];if(192!=(224&s)){var c=63&e[t++];if((s=224==(240&s)?(15&s)<<12|a<<6|c:(7&s)<<18|a<<12|c<<6|63&e[t++])<65536)o+=String.fromCharCode(s);else{var f=s-65536;o+=String.fromCharCode(55296|f>>10,56320|1023&f)}}else o+=String.fromCharCode((31&s)<<6|a)}else o+=String.fromCharCode(s)}return o},printChar=(e,t)=>{var r=R[e];if(0===t||10===t){(1===e?d:p)(UTF8ArrayToString(r));r.length=0}else r.push(t)},UTF8ToString=(e,t)=>e?UTF8ArrayToString(u,e,t):"";n.noExitRuntime&&(v=n.noExitRuntime);n.print&&(d=n.print);n.printErr&&(p=n.printErr);n.wasmBinary&&n.wasmBinary;n.arguments&&n.arguments;n.thisProgram&&(o=n.thisProgram);n.writeArrayToMemory=(e,t)=>{f.set(e,t)};var S={l:()=>function abort(e){n.onAbort?.(e);p(e="Aborted("+e+")");m=!0;e+=". Build with -sASSERTIONS for more info.";var t=new WebAssembly.RuntimeError(e);r(t);throw t}(""),k:()=>{v=!1;_=0},m:(e,t)=>{if(x[e]){clearTimeout(x[e].id);delete x[e]}if(!t)return 0;var r=setTimeout((()=>{delete x[e];callUserCallback((()=>I(e,performance.now())))}),t);x[e]={id:r,timeout_ms:t};return 0},g:function _copy_pixels_1(e,t){e>>=2;const r=n.imageData=new Uint8ClampedArray(t),i=l.subarray(e,e+t);r.set(i)},f:function _copy_pixels_3(e,t,r,i){e>>=2;t>>=2;r>>=2;const o=n.imageData=new Uint8ClampedArray(3*i),s=l.subarray(e,e+i),a=l.subarray(t,t+i),c=l.subarray(r,r+i);for(let e=0;e<i;e++){o[3*e]=s[e];o[3*e+1]=a[e];o[3*e+2]=c[e]}},e:function _copy_pixels_4(e,t,r,i,o){e>>=2;t>>=2;r>>=2;i>>=2;const s=n.imageData=new Uint8ClampedArray(4*o),a=l.subarray(e,e+o),c=l.subarray(t,t+o),f=l.subarray(r,r+o),u=l.subarray(i,i+o);for(let e=0;e<o;e++){s[4*e]=a[e];s[4*e+1]=c[e];s[4*e+2]=f[e];s[4*e+3]=u[e]}},n:e=>{var t,r,n=u.length,i=2147483648;if((e>>>=0)>i)return!1;for(var o=1;o<=4;o*=2){var s=n*(1+.2/o);s=Math.min(s,e+100663296);var a=Math.min(i,(t=Math.max(e,s),r=65536,Math.ceil(t/r)*r));if(growMemory(a))return!0}return!1},p:(e,t)=>{var r=0,n=0;for(var i of getEnvStrings()){var o=t+r;h[e+n>>2]=o;r+=((e,t,r,n)=>{if(!(n>0))return 0;for(var i=r,o=r+n-1,s=0;s<e.length;++s){var a=e.charCodeAt(s);a>=55296&&a<=57343&&(a=65536+((1023&a)<<10)|1023&e.charCodeAt(++s));if(a<=127){if(r>=o)break;t[r++]=a}else if(a<=2047){if(r+1>=o)break;t[r++]=192|a>>6;t[r++]=128|63&a}else if(a<=65535){if(r+2>=o)break;t[r++]=224|a>>12;t[r++]=128|a>>6&63;t[r++]=128|63&a}else{if(r+3>=o)break;t[r++]=240|a>>18;t[r++]=128|a>>12&63;t[r++]=128|a>>6&63;t[r++]=128|63&a}}t[r]=0;return r-i})(i,u,o,1/0)+1;n+=4}return 0},q:(e,t)=>{var r=getEnvStrings();h[e>>2]=r.length;var n=0;for(var i of r)n+=lengthBytesUTF8(i)+1;h[t>>2]=n;return 0},b:e=>52,o:function _fd_seek(e,t,r,n){t=(i=t)<-9007199254740992||i>9007199254740992?NaN:Number(i);var i;return 70},c:(e,t,r,n)=>{for(var i=0,o=0;o<r;o++){var s=h[t>>2],a=h[t+4>>2];t+=8;for(var c=0;c<a;c++)printChar(e,u[s+c]);i+=a}h[n>>2]=i;return 0},r:function _gray_to_rgba(e,t){e>>=2;const r=n.imageData=new Uint8ClampedArray(4*t),i=l.subarray(e,e+t);for(let e=0;e<t;e++){r[4*e]=r[4*e+1]=r[4*e+2]=i[e];r[4*e+3]=255}},i:function _graya_to_rgba(e,t,r){e>>=2;t>>=2;const i=n.imageData=new Uint8ClampedArray(4*r),o=l.subarray(e,e+r),s=l.subarray(t,t+r);for(let e=0;e<r;e++){i[4*e]=i[4*e+1]=i[4*e+2]=o[e];i[4*e+3]=s[e]}},d:function _jsPrintWarning(e){const t=UTF8ToString(e);(n.warn||console.warn)(`OpenJPEG: ${t}`)},j:_proc_exit,h:function _rgb_to_rgba(e,t,r,i){e>>=2;t>>=2;r>>=2;const o=n.imageData=new Uint8ClampedArray(4*i),s=l.subarray(e,e+i),a=l.subarray(t,t+i),c=l.subarray(r,r+i);for(let e=0;e<i;e++){o[4*e]=s[e];o[4*e+1]=a[e];o[4*e+2]=c[e];o[4*e+3]=255}},a:function _storeErrorMessage(e){const t=UTF8ToString(e);n.errorMessages?n.errorMessages+="\n"+t:n.errorMessages=t}},B=await async function createWasm(){function receiveInstance(e,t){B=e.exports;a=B.s;updateMemoryViews();!function removeRunDependency(e){g--;n.monitorRunDependencies?.(g);if(0==g&&y){var t=y;y=null;t()}}();return B}!function addRunDependency(e){g++;n.monitorRunDependencies?.(g)}();var e=function getWasmImports(){return{a:S}}();return new Promise(((t,r)=>{n.instantiateWasm(e,((e,r)=>{t(receiveInstance(e))}))}))}(),I=(B.t,n._malloc=B.u,n._free=B.v,n._jp2_decode=B.w,B.x);!function preInit(){if(n.preInit){"function"==typeof n.preInit&&(n.preInit=[n.preInit]);for(;n.preInit.length>0;)n.preInit.shift()()}}();!function run(){if(g>0)y=run;else{!function preRun(){if(n.preRun){"function"==typeof n.preRun&&(n.preRun=[n.preRun]);for(;n.preRun.length;)addOnPreRun(n.preRun.shift())}callRuntimeCallbacks(w)}();if(g>0)y=run;else if(n.setStatus){n.setStatus("Running...");setTimeout((()=>{setTimeout((()=>n.setStatus("")),1);doRun()}),1)}else doRun()}function doRun(){n.calledRun=!0;if(!m){!function initRuntime(){B.t()}();t(n);n.onRuntimeInitialized?.();!function postRun(){if(n.postRun){"function"==typeof n.postRun&&(n.postRun=[n.postRun]);for(;n.postRun.length;)addOnPostRun(n.postRun.shift())}callRuntimeCallbacks(b)}()}}}();return i};const G=OpenJPEG;class Stream extends base_stream_BaseStream{constructor(e,t,r,n){super();this.bytes=e instanceof Uint8Array?e:new Uint8Array(e);this.start=t||0;this.pos=this.start;this.end=t+r||this.bytes.length;this.dict=n}get length(){return this.end-this.start}get isEmpty(){return 0===this.length}getByte(){return this.pos>=this.end?-1:this.bytes[this.pos++]}getBytes(e){const t=this.bytes,r=this.pos,n=this.end;if(!e)return t.subarray(r,n);let i=r+e;i>n&&(i=n);this.pos=i;return t.subarray(r,i)}getByteRange(e,t){e<0&&(e=0);t>this.end&&(t=this.end);return this.bytes.subarray(e,t)}reset(){this.pos=this.start}moveStart(){this.start=this.pos}makeSubStream(e,t,r=null){return new Stream(this.bytes.buffer,e,t,r)}}class JpxError extends i{constructor(e){super(e,"JpxError")}}class JpxImage{static#D=null;static#T=null;static#M=null;static#A=!0;static#P=!0;static#S=null;static setOptions({handler:e,useWasm:t,useWorkerFetch:r,wasmUrl:n}){this.#A=t;this.#P=r;this.#S=n;r||(this.#T=e)}static async#O(e){const t=`${this.#S}openjpeg_nowasm_fallback.js`;let r=null;try{r=(await import(
/*webpackIgnore: true*/
/*@vite-ignore*/
t)).default()}catch(e){util_warn(`JpxImage#getJsModule: ${e}`)}e(r)}static async#U(e,t,r){const n="openjpeg.wasm";try{this.#D||(this.#P?this.#D=await async function fetchBinaryData(e){const t=await fetch(e);if(!t.ok)throw new Error(`Failed to fetch file "${e}" with "${t.statusText}".`);return new Uint8Array(await t.arrayBuffer())}(`${this.#S}${n}`):this.#D=await this.#T.sendWithPromise("FetchBinaryData",{type:"wasmFactory",filename:n}));return r((await WebAssembly.instantiate(this.#D,t)).instance)}catch(t){util_warn(`JpxImage#instantiateWasm: ${t}`);this.#O(e);return null}finally{this.#T=null}}static async decode(e,{numComponents:t=4,isIndexedColormap:r=!1,smaskInData:n=!1,reducePower:i=0}={}){if(!this.#M){const{promise:e,resolve:t}=Promise.withResolvers(),r=[e];this.#A?r.push(G({warn:util_warn,instantiateWasm:this.#U.bind(this,t)})):this.#O(t);this.#M=Promise.race(r)}const o=await this.#M;if(!o)throw new JpxError("OpenJPEG failed to initialize");let s;try{const a=e.length;s=o._malloc(a);o.writeArrayToMemory(e,s);if(o._jp2_decode(s,a,t>0?t:0,!!r,!!n,i)){const{errorMessages:e}=o;if(e){delete o.errorMessages;throw new JpxError(e)}throw new JpxError("Unknown error")}const{imageData:c}=o;o.imageData=null;return c}finally{s&&o._free(s)}}static cleanup(){this.#M=null}static parseImageProperties(e){if(!(e instanceof ArrayBuffer||ArrayBuffer.isView(e)))throw new JpxError("Invalid data format, must be a TypedArray.");let t=(e=new Stream(e)).getByte();for(;t>=0;){const r=t;t=e.getByte();if(65361===(r<<8|t)){e.skip(4);const t=e.getInt32()>>>0,r=e.getInt32()>>>0,n=e.getInt32()>>>0,i=e.getInt32()>>>0;e.skip(16);return{width:t-n,height:r-i,bitsPerComponent:8,componentsCount:e.getUint16()}}}throw new JpxError("No size marker found in JPX stream")}}globalThis.pdfjsImageDecoders={getVerbosityLevel,Jbig2Error,Jbig2Image,JpegError,JpegImage,JpxError,JpxImage,setVerbosityLevel,VerbosityLevel:r};export{Jbig2Error,Jbig2Image,JpegError,JpegImage,JpxError,JpxImage,r as VerbosityLevel,getVerbosityLevel,setVerbosityLevel};