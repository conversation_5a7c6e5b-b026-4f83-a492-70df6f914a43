{"version": 3, "sources": ["src/buffer.js", "src/lowlevel.js", "src/shared.js", "src/support.js", "src/o-encoder.js", "src/xhr.js", "src/o-decoder.js", "src/polyfill.js"], "sourcesContent": ["\n/**\n * @param {Uint8Array} bytes\n * @param {string} encoding\n * @return {string}\n */\nexport function decodeBuffer(bytes, encoding) {\n  /** @type {Buffer} */\n  var b;\n  if (bytes instanceof Buffer) {\n    // @ts-ignore\n    b = bytes;\n  } else {\n    b = Buffer.from(bytes.buffer, bytes.byteOffset, bytes.byteLength);\n  }\n  return b.toString(/** @type {BufferEncoding} */(encoding));\n}\n\n\n/**\n * @param {string} string\n * @return {Uint8Array}\n */\nexport var encodeBuffer = (string) => Buffer.from(string);\n", "\n/**\n * @param {Uint8Array} bytes\n * @return {string}\n */\nexport function decodeFallback(bytes) {\n  var inputIndex = 0;\n\n  // Create a working buffer for UTF-16 code points, but don't generate one\n  // which is too large for small input sizes. UTF-8 to UCS-16 conversion is\n  // going to be at most 1:1, if all code points are ASCII. The other extreme\n  // is 4-byte UTF-8, which results in two UCS-16 points, but this is still 50%\n  // fewer entries in the output.\n  var pendingSize = Math.min(256 * 256, bytes.length + 1);\n  var pending = new Uint16Array(pendingSize);\n  var chunks = [];\n  var pendingIndex = 0;\n\n  for (; ;) {\n    var more = inputIndex < bytes.length;\n\n    // If there's no more data or there'd be no room for two UTF-16 values,\n    // create a chunk. This isn't done at the end by simply slicing the data\n    // into equal sized chunks as we might hit a surrogate pair.\n    if (!more || (pendingIndex >= pendingSize - 1)) {\n      // nb. .apply and friends are *really slow*. Low-hanging fruit is to\n      // expand this to literally pass pending[0], pending[1], ... etc, but\n      // the output code expands pretty fast in this case.\n      // These extra vars get compiled out: they're just to make TS happy.\n      // Turns out you can pass an ArrayLike to .apply().\n      var subarray = pending.subarray(0, pendingIndex);\n      var arraylike = /** @type {number[]} */ (/** @type {unknown} */ (subarray));\n      chunks.push(String.fromCharCode.apply(null, arraylike));\n\n      if (!more) {\n        return chunks.join('');\n      }\n\n      // Move the buffer forward and create another chunk.\n      bytes = bytes.subarray(inputIndex);\n      inputIndex = 0;\n      pendingIndex = 0;\n    }\n\n    // The native TextDecoder will generate \"REPLACEMENT CHARACTER\" where the\n    // input data is invalid. Here, we blindly parse the data even if it's\n    // wrong: e.g., if a 3-byte sequence doesn't have two valid continuations.\n\n    var byte1 = bytes[inputIndex++];\n    if ((byte1 & 0x80) === 0) {  // 1-byte or null\n      pending[pendingIndex++] = byte1;\n    } else if ((byte1 & 0xe0) === 0xc0) {  // 2-byte\n      var byte2 = bytes[inputIndex++] & 0x3f;\n      pending[pendingIndex++] = ((byte1 & 0x1f) << 6) | byte2;\n    } else if ((byte1 & 0xf0) === 0xe0) {  // 3-byte\n      var byte2 = bytes[inputIndex++] & 0x3f;\n      var byte3 = bytes[inputIndex++] & 0x3f;\n      pending[pendingIndex++] = ((byte1 & 0x1f) << 12) | (byte2 << 6) | byte3;\n    } else if ((byte1 & 0xf8) === 0xf0) {  // 4-byte\n      var byte2 = bytes[inputIndex++] & 0x3f;\n      var byte3 = bytes[inputIndex++] & 0x3f;\n      var byte4 = bytes[inputIndex++] & 0x3f;\n\n      // this can be > 0xffff, so possibly generate surrogates\n      var codepoint = ((byte1 & 0x07) << 0x12) | (byte2 << 0x0c) | (byte3 << 0x06) | byte4;\n      if (codepoint > 0xffff) {\n        // codepoint &= ~0x10000;\n        codepoint -= 0x10000;\n        pending[pendingIndex++] = (codepoint >>> 10) & 0x3ff | 0xd800;\n        codepoint = 0xdc00 | codepoint & 0x3ff;\n      }\n      pending[pendingIndex++] = codepoint;\n    } else {\n      // invalid initial byte\n    }\n  }\n}\n\n\n/**\n * @param {string} string\n * @return {Uint8Array}\n */\nexport function encodeFallback(string) {\n  var pos = 0;\n  var len = string.length;\n\n  var at = 0;  // output position\n  var tlen = Math.max(32, len + (len >>> 1) + 7);  // 1.5x size\n  var target = new Uint8Array((tlen >>> 3) << 3);  // ... but at 8 byte offset\n\n  while (pos < len) {\n    var value = string.charCodeAt(pos++);\n    if (value >= 0xd800 && value <= 0xdbff) {\n      // high surrogate\n      if (pos < len) {\n        var extra = string.charCodeAt(pos);\n        if ((extra & 0xfc00) === 0xdc00) {\n          ++pos;\n          value = ((value & 0x3ff) << 10) + (extra & 0x3ff) + 0x10000;\n        }\n      }\n      if (value >= 0xd800 && value <= 0xdbff) {\n        continue;  // drop lone surrogate\n      }\n    }\n\n    // expand the buffer if we couldn't write 4 bytes\n    if (at + 4 > target.length) {\n      tlen += 8;  // minimum extra\n      tlen *= (1.0 + (pos / string.length) * 2);  // take 2x the remaining\n      tlen = (tlen >>> 3) << 3;  // 8 byte offset\n\n      var update = new Uint8Array(tlen);\n      update.set(target);\n      target = update;\n    }\n\n    if ((value & 0xffffff80) === 0) {  // 1-byte\n      target[at++] = value;  // ASCII\n      continue;\n    } else if ((value & 0xfffff800) === 0) {  // 2-byte\n      target[at++] = ((value >>> 6) & 0x1f) | 0xc0;\n    } else if ((value & 0xffff0000) === 0) {  // 3-byte\n      target[at++] = ((value >>> 12) & 0x0f) | 0xe0;\n      target[at++] = ((value >>> 6) & 0x3f) | 0x80;\n    } else if ((value & 0xffe00000) === 0) {  // 4-byte\n      target[at++] = ((value >>> 18) & 0x07) | 0xf0;\n      target[at++] = ((value >>> 12) & 0x3f) | 0x80;\n      target[at++] = ((value >>> 6) & 0x3f) | 0x80;\n    } else {\n      continue;  // out of range\n    }\n\n    target[at++] = (value & 0x3f) | 0x80;\n  }\n\n  // Use subarray if slice isn't supported (IE11). This will use more memory\n  // because the original array still exists.\n  return target.slice ? target.slice(0, at) : target.subarray(0, at);\n}\n", "\nexport var failedToString = 'Failed to ';\n\n/**\n * @param {boolean|undefined} check \n * @param {string} operation \n * @param {string} fieldName \n */\nexport var maybeThrowFailedToOption = (check, operation, fieldName) => {\n  if (check) {\n    throw new Error(`${failedToString}${operation}: the '${fieldName}' option is unsupported.`);\n  }\n};", "\nexport var hasBufferFrom = (typeof Buffer === 'function' && Buffer.from);", "import { encodeBuffer } from './buffer.js';\nimport { encodeFallback } from './lowlevel.js';\nimport { maybeThrowFailedToOption } from './shared.js';\nimport { hasBufferFrom } from './support.js';\n\nexport var encodeImpl = hasBufferFrom ? encodeFallback : encodeBuffer;\n\n/**\n * @constructor\n */\nexport function FastTextEncoder() {\n  // This does not accept an encoding, and always uses UTF-8:\n  //   https://www.w3.org/TR/encoding/#dom-textencoder\n  this.encoding = 'utf-8';\n}\n\n/**\n * @param {string} string\n * @param {{stream: boolean}=} options\n * @return {Uint8Array}\n */\nFastTextEncoder.prototype.encode = function (string, options) {\n  maybeThrowFailedToOption(options && options.stream, 'encode', 'stream');\n  return encodeImpl(string);\n};\n", "\n/**\n * This is a horrible hack which works in some old browsers. We can tell them to decode bytes via\n * sync XHR.\n *\n * Throws if fails. Should be wrapped in something to check that.\n *\n * @param {Uint8Array} bytes\n * @return {string}\n */\nexport function decodeSyncXHR(bytes) {\n  var u;\n\n  // This hack will fail in non-Edgium Edge because sync XHRs are disabled (and\n  // possibly in other places), so ensure there's a fallback call.\n  try {\n    var b = new Blob([bytes], { type: 'text/plain;charset=UTF-8' });\n    u = URL.createObjectURL(b);\n\n    var x = new XMLHttpRequest();\n    x.open('GET', u, false);\n    x.send();\n    return x.responseText;\n  } finally {\n    if (u) {\n      URL.revokeObjectURL(u);\n    }\n  }\n}", "import { decodeBuffer } from './buffer.js';\nimport { decodeFallback } from './lowlevel.js';\nimport { failedToString, maybeThrowFailedToOption } from './shared.js';\nimport { hasBufferFrom } from './support.js';\nimport { decodeSyncXHR } from './xhr.js';\n\nvar trySyncXHR = !hasBufferFrom && (typeof Blob === 'function' && typeof URL === 'function' && typeof URL.createObjectURL === 'function');\nvar validUtfLabels = ['utf-8', 'utf8', 'unicode-1-1-utf-8'];\n\n/** @type {(bytes: Uint8Array, encoding: string) => string} */\nvar decodeImpl = decodeFallback;\nif (hasBufferFrom) {\n  decodeImpl = decodeBuffer;\n} else if (trySyncXHR) {\n  decodeImpl = (string) => {\n    try {\n      return decodeSyncXHR(string);\n    } catch (e) {\n      return decodeFallback(string);\n    }\n  };\n}\n\n\nvar ctorString = `construct 'TextDecoder'`;\nvar errorPrefix = `${failedToString} ${ctorString}: the `;\n\n\n/**\n * @constructor\n * @param {string=} utfLabel\n * @param {{fatal: boolean}=} options\n */\nexport function FastTextDecoder(utfLabel, options) {\n  maybeThrowFailedToOption(options && options.fatal, ctorString, 'fatal');\n\n  utfLabel = utfLabel || 'utf-8';\n\n  /** @type {boolean} */\n  var ok;\n  if (hasBufferFrom) {\n    ok = Buffer.isEncoding(utfLabel);\n  } else {\n    ok = validUtfLabels.indexOf(utfLabel.toLowerCase()) !== -1;\n  }\n  if (!ok) {\n    throw new RangeError(`${errorPrefix} encoding label provided ('${utfLabel}') is invalid.`);\n  }\n\n  this.encoding = utfLabel;\n  this.fatal = false;\n  this.ignoreBOM = false;\n}\n\n/**\n * @param {(ArrayBuffer|ArrayBufferView)} buffer\n * @param {{stream: boolean}=} options\n * @return {string}\n */\nFastTextDecoder.prototype.decode = function (buffer, options) {\n  maybeThrowFailedToOption(options && options.stream, 'decode', 'stream');\n\n  var bytes;\n\n  if (buffer instanceof Uint8Array) {\n    // Accept Uint8Array instances as-is. This is also a Node buffer.\n    bytes = buffer;\n  } else if (buffer['buffer'] instanceof ArrayBuffer) {\n    // Look for ArrayBufferView, which isn't a real type, but basically\n    // represents all the valid TypedArray types plus DataView. They all have\n    // \".buffer\" as an instance of ArrayBuffer.\n    bytes = new Uint8Array(/** @type {ArrayBufferView} */(buffer).buffer);\n  } else {\n    // The only other valid argument here is that \"buffer\" is an ArrayBuffer.\n    // We also try to convert anything else passed to a Uint8Array, as this\n    // catches anything that's array-like. Native code would throw here.\n    bytes = new Uint8Array(/** @type {any} */(buffer));\n  }\n\n  return decodeImpl(bytes, this.encoding);\n};\n", "\nimport { FastTextEncoder } from './o-encoder.js';\nimport { FastTextDecoder } from './o-decoder.js';\n\n// /** @type {object} */\n// const scope = typeof window !== 'undefined' ? window : (typeof global !== 'undefined' ? global : this);\n\nscope['TextEncoder'] = scope['TextEncoder'] || FastTextEncoder;\nscope['TextDecoder'] = scope['TextDecoder'] || FastTextDecoder;\n\n// export {};\n"], "mappings": ";AAMO,SAASA,EAAaC,EAAOC,EAAU,CAE5C,IAAIC,EACJ,OAAIF,aAAiB,OAEnBE,EAAIF,EAEJE,EAAI,OAAO,KAAKF,EAAM,OAAQA,EAAM,WAAYA,EAAM,UAAU,EAE3DE,EAAE,SAAuCD,CAAS,CAC3D,CAOO,IAAIE,EAAe,SAACC,EAAQ,CAAG,cAAO,KAAKA,CAAM,GClBjD,SAASC,EAAeC,EAAO,CAapC,QAZIC,EAAa,EAObC,EAAc,KAAK,IAAI,IAAM,IAAKF,EAAM,OAAS,CAAC,EAClDG,EAAU,IAAI,YAAYD,CAAW,EACrCE,EAAS,CAAC,EACVC,EAAe,IAET,CACR,IAAIC,EAAOL,EAAaD,EAAM,OAK9B,GAAI,CAACM,GAASD,GAAgBH,EAAc,EAAI,CAM9C,IAAIK,EAAWJ,EAAQ,SAAS,EAAGE,CAAY,EAC3CG,EAA6DD,EAGjE,GAFAH,EAAO,KAAK,OAAO,aAAa,MAAM,KAAMI,CAAS,CAAC,EAElD,CAACF,EACH,OAAOF,EAAO,KAAK,EAAE,EAIvBJ,EAAQA,EAAM,SAASC,CAAU,EACjCA,EAAa,EACbI,EAAe,CACjB,CAMA,IAAII,EAAQT,EAAMC,KAClB,IAAKQ,EAAQ,OAAU,EACrBN,EAAQE,KAAkBI,WAChBA,EAAQ,OAAU,IAAM,CAClC,IAAIC,EAAQV,EAAMC,KAAgB,GAClCE,EAAQE,MAAoBI,EAAQ,KAAS,EAAKC,CACpD,UAAYD,EAAQ,OAAU,IAAM,CAClC,IAAIC,EAAQV,EAAMC,KAAgB,GAC9BU,EAAQX,EAAMC,KAAgB,GAClCE,EAAQE,MAAoBI,EAAQ,KAAS,GAAOC,GAAS,EAAKC,CACpE,UAAYF,EAAQ,OAAU,IAAM,CAClC,IAAIC,EAAQV,EAAMC,KAAgB,GAC9BU,EAAQX,EAAMC,KAAgB,GAC9BW,EAAQZ,EAAMC,KAAgB,GAG9BY,GAAcJ,EAAQ,IAAS,GAASC,GAAS,GAASC,GAAS,EAAQC,EAC3EC,EAAY,QAEdA,GAAa,MACbV,EAAQE,KAAmBQ,IAAc,GAAM,KAAQ,MACvDA,EAAY,MAASA,EAAY,MAEnCV,EAAQE,KAAkBQ,CAC5B,CAGF,CACF,CAOO,SAASC,EAAeC,EAAQ,CAQrC,QAPIC,EAAM,EACNC,EAAMF,EAAO,OAEbG,EAAK,EACLC,EAAO,KAAK,IAAI,GAAIF,GAAOA,IAAQ,GAAK,CAAC,EACzCG,EAAS,IAAI,WAAYD,IAAS,GAAM,CAAC,EAEtCH,EAAMC,GAAK,CAChB,IAAII,EAAQN,EAAO,WAAWC,GAAK,EACnC,GAAIK,GAAS,OAAUA,GAAS,MAAQ,CAEtC,GAAIL,EAAMC,EAAK,CACb,IAAIK,EAAQP,EAAO,WAAWC,CAAG,GAC5BM,EAAQ,SAAY,QACvB,EAAEN,EACFK,IAAUA,EAAQ,OAAU,KAAOC,EAAQ,MAAS,MAExD,CACA,GAAID,GAAS,OAAUA,GAAS,MAC9B,QAEJ,CAGA,GAAIH,EAAK,EAAIE,EAAO,OAAQ,CAC1BD,GAAQ,EACRA,GAAS,EAAOH,EAAMD,EAAO,OAAU,EACvCI,EAAQA,IAAS,GAAM,EAEvB,IAAII,EAAS,IAAI,WAAWJ,CAAI,EAChCI,EAAO,IAAIH,CAAM,EACjBA,EAASG,CACX,CAEA,IAAKF,EAAQ,cAAgB,EAAG,CAC9BD,EAAOF,KAAQG,EACf,QACF,UAAYA,EAAQ,cAAgB,EAClCD,EAAOF,KAAUG,IAAU,EAAK,GAAQ,aAC9BA,EAAQ,cAAgB,EAClCD,EAAOF,KAAUG,IAAU,GAAM,GAAQ,IACzCD,EAAOF,KAAUG,IAAU,EAAK,GAAQ,aAC9BA,EAAQ,cAAgB,EAClCD,EAAOF,KAAUG,IAAU,GAAM,EAAQ,IACzCD,EAAOF,KAAUG,IAAU,GAAM,GAAQ,IACzCD,EAAOF,KAAUG,IAAU,EAAK,GAAQ,QAExC,UAGFD,EAAOF,KAASG,EAAQ,GAAQ,GAClC,CAIA,OAAOD,EAAO,MAAQA,EAAO,MAAM,EAAGF,CAAE,EAAIE,EAAO,SAAS,EAAGF,CAAE,CACnE,CC3IO,IAAIM,EAAiB,aAOjBC,EAA2B,SAACC,EAAOC,EAAWC,EAAc,CACrE,GAAIF,EACF,MAAM,IAAI,MAAM,GAAG,OAAAF,GAAiB,OAAAG,EAAS,WAAU,OAAAC,EAAS,2BAA0B,CAE9F,ECXO,IAAIC,EAAiB,OAAO,QAAW,YAAc,OAAO,KCI5D,IAAIC,EAAaC,EAAgBC,EAAiBC,EAKlD,SAASC,GAAkB,CAGhC,KAAK,SAAW,OAClB,CAOAA,EAAgB,UAAU,OAAS,SAAUC,EAAQC,EAAS,CAC5D,OAAAC,EAAyBD,GAAWA,EAAQ,OAAQ,SAAU,QAAQ,EAC/DN,EAAWK,CAAM,CAC1B,ECdO,SAASG,EAAcC,EAAO,CACnC,IAAIC,EAIJ,GAAI,CACF,IAAIC,EAAI,IAAI,KAAK,CAACF,CAAK,EAAG,CAAE,KAAM,0BAA2B,CAAC,EAC9DC,EAAI,IAAI,gBAAgBC,CAAC,EAEzB,IAAIC,EAAI,IAAI,eACZ,OAAAA,EAAE,KAAK,MAAOF,EAAG,EAAK,EACtBE,EAAE,KAAK,EACAA,EAAE,YACX,QAAE,CACIF,GACF,IAAI,gBAAgBA,CAAC,CAEzB,CACF,CCtBA,IAAIG,EAAa,CAACC,GAAkB,OAAO,MAAS,YAAc,OAAO,KAAQ,YAAc,OAAO,IAAI,iBAAoB,WAC1HC,EAAiB,CAAC,QAAS,OAAQ,mBAAmB,EAGtDC,EAAaC,EACbH,EACFE,EAAaE,EACJL,IACTG,EAAa,SAACG,EAAW,CACvB,GAAI,CACF,OAAOC,EAAcD,CAAM,CAC7B,OAAS,EAAP,CACA,OAAOF,EAAeE,CAAM,CAC9B,CACF,GAIF,IAAIE,EAAa,0BACbC,EAAc,GAAG,OAAAC,EAAc,KAAI,OAAAF,EAAU,UAQ1C,SAASG,EAAgBC,EAAUC,EAAS,CACjDC,EAAyBD,GAAWA,EAAQ,MAAOL,EAAY,OAAO,EAEtEI,EAAWA,GAAY,QAGvB,IAAIG,EAMJ,GALId,EACFc,EAAK,OAAO,WAAWH,CAAQ,EAE/BG,EAAKb,EAAe,QAAQU,EAAS,YAAY,CAAC,IAAM,GAEtD,CAACG,EACH,MAAM,IAAI,WAAW,GAAG,OAAAN,EAAW,+BAA8B,OAAAG,EAAQ,iBAAgB,EAG3F,KAAK,SAAWA,EAChB,KAAK,MAAQ,GACb,KAAK,UAAY,EACnB,CAOAD,EAAgB,UAAU,OAAS,SAAUK,EAAQH,EAAS,CAC5DC,EAAyBD,GAAWA,EAAQ,OAAQ,SAAU,QAAQ,EAEtE,IAAII,EAEJ,OAAID,aAAkB,WAEpBC,EAAQD,EACCA,EAAO,kBAAqB,YAIrCC,EAAQ,IAAI,WAA0CD,EAAQ,MAAM,EAKpEC,EAAQ,IAAI,WAA8BD,CAAO,EAG5Cb,EAAWc,EAAO,KAAK,QAAQ,CACxC,ECzEA,MAAM,YAAiB,MAAM,aAAkBC,EAC/C,MAAM,YAAiB,MAAM,aAAkBC", "names": ["decodeBuffer", "bytes", "encoding", "b", "encodeBuffer", "string", "decodeFallback", "bytes", "inputIndex", "pendingSize", "pending", "chunks", "pendingIndex", "more", "subarray", "arraylike", "byte1", "byte2", "byte3", "byte4", "codepoint", "encodeFallback", "string", "pos", "len", "at", "tlen", "target", "value", "extra", "update", "failedToString", "maybeThrowFailedToOption", "check", "operation", "fieldName", "hasBufferFrom", "encodeImpl", "hasBufferFrom", "encodeFallback", "encodeBuffer", "FastTextEncoder", "string", "options", "maybeThrowFailedToOption", "decodeSyncXHR", "bytes", "u", "b", "x", "trySyncXHR", "hasBufferFrom", "validUtfLabels", "decodeImpl", "decodeFallback", "decodeBuffer", "string", "decodeSyncXHR", "ctorString", "errorPrefix", "failedToString", "FastTextDecoder", "utfLabel", "options", "maybeThrowFailedToOption", "ok", "buffer", "bytes", "FastTextEncoder", "FastTextDecoder"]}