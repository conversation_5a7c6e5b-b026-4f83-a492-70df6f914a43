{"name": "loqseq-functional", "version": "0.1.0", "description": "Functional Logseq implementation using Freedom Application Platform (FAP)", "scripts": {"dev": "node playground/scripts/simple-server.js", "test": "node playground/scripts/test-browser.js", "new-version": "node playground/scripts/new-version.js", "list-versions": "ls -la playground/v*/", "open": "./playground/scripts/open-dev.sh"}, "dependencies": {"@isomorphic-git/lightning-fs": "^4.6.2", "@sqlite.org/sqlite-wasm": "^3.50.3-build1", "dompurify": "^3.2.6", "fuse.js": "^7.1.0", "marked": "^16.1.1", "pdfjs-dist": "^5.4.54"}, "type": "module", "private": true, "devDependencies": {"playwright": "^1.54.1"}}