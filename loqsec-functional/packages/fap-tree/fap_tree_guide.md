# FAP Tree View Component – Developer & AI Assistant Guide

## Overview

This guide documents how to build, extend, and manage a tree view UI component in the Freedom Application Platform (FAP) using only functional JavaScript and CSS. The goal is simplicity, reusability, and semantic structure across different tree-based UIs.

## Core Concepts

### Component Structure

- **Core Renderer (**``**)**: Pure function to render tree nodes from hierarchical data.
- **Adapters (**``**, **``**)**: Transform domain-specific data (file systems, menus, etc.) into core-compatible format.
- **Styling**:
  - `fap-tree-core.css`: Layout, indentation, nesting
  - `fap-tree-fs.css`: Folder/file icons and styles
  - `fap-tree-menu.css`: Menu navigation interaction

### Functional Design

Tree rendering is stateless and purely based on passed data:

```ts
fap.treeCore.renderTree(container, treeData, {
  getLabel: node => string,
  getChildren: node => node[]
});
```

### Tree Node Format

Each use case defines its own shape. Examples:

- File System:
  ```ts
  { name: string, kind: 'file' | 'directory', children?: Node[] }
  ```
- Menu:
  ```ts
  { label: string, items?: Node[] }
  ```

---

## Use Cases

### ✅ Implemented

- **File System Tree**: Expandable folders, file click logging.
- **Menu Tree**: Toggle menu groups, click-to-log items.

### 🧠 Additional Patterns

- Outline View (e.g. markdown headings)
- Settings Navigator
- Component/DOM hierarchy
- Tag/category explorers
- Database/table schema explorer

---

## Interactivity Features

### Expand/Collapse

Handled by toggling `.hidden` on `.fap-tree-children`. No internal state needed.

```js
el.onclick = () => childElement.hidden = !childElement.hidden;
```

### Click Handling

Add `.onclick` to leaf nodes (e.g. files, menu items):

```js
el.onclick = () => console.log("Clicked:", el.textContent);
```

### Styling

Use classes like `.file`, `.dir`, `.active` to decorate nodes for icons, colors, state.

---

## Best Practices

- Keep `fap-tree-core` logic minimal and generic
- Use separate JS modules per use case
- Use separate CSS per visual theme or context
- Don't maintain state in JS unless necessary – prefer DOM state (`.hidden`, `.classList`)
- Embrace pure functions and declarative rendering

---

## Optional Enhancements

- Keyboard navigation
- Drag and drop reordering
- Context menus
- Checkbox support
- Lazy-loading children
- Recursive search/filter UI

---

## Quick Setup Checklist

-

---

## Credits

Developed as part of the Freedom Application Platform (FAP) component system. For modular, DOM-native JS UI development.

