// File: fap_tree_filesystem.js

/**
 * FAP Filesystem Tree Component
 * Self-contained component that extends fap.treeCore for filesystem trees.
 * Renders inside <fap-tree-filesystem> elements.
 */

const isDir = node => node.kind === 'directory';

function renderFilesystemTree(container, fsTree) {
  window.fap.treeCore.renderTree(container, fsTree, {
    getLabel: node => node.name + (isDir(node) ? '/' : ''),
    getChildren: node => isDir(node) ? node.children || [] : []
  });
}

// Enhanced initialization that automatically finds and renders filesystem trees
function initFilesystemTrees(data) {
  document.querySelectorAll('fap-tree-filesystem').forEach(element => {
    if (!element.hasAttribute('data-initialized')) {
      renderFilesystemTree(element, data);
      element.setAttribute('data-initialized', 'true');
    }
  });
}

window.fap = window.fap || {};
Object.assign(window.fap, {
  treeFilesystem: Object.freeze({
    render: renderFilesystemTree,
    init: initFilesystemTrees
  })
});

// Keep backward compatibility 
window.fap.treeFs = window.fap.treeFilesystem;
