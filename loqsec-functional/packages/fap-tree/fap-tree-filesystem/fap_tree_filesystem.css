/* File: fap_tree_filesystem.css */

/* --- Filesystem Tree Component --- */
fap-tree-filesystem {
  display: block;
}

fap-tree-filesystem tree-node {
  margin-bottom: 0.25rem;
}

fap-tree-filesystem tree-label {
  cursor: pointer;
  padding: 0.25rem 0.5rem;
  transition: background 0.2s ease;
}

fap-tree-filesystem tree-label:hover {
  background: var(--accent);
  color: white;
}

fap-tree-filesystem tree-label::before {
  content: '\1F4C1'; /* 📁 default icon */
  display: inline-block;
  margin-right: 0.2em;
  width: 1.2em;
}

fap-tree-filesystem tree-label.file::before {
  content: '\1F4C4'; /* 📄 file icon */
}

/* --- Expand/Collapse Functionality --- */
fap-tree-filesystem tree-children {
  display: block;
  margin-left: 1.5rem;
  border-left: 1px solid var(--border, #ccc);
  padding-left: 0.5rem;
}

fap-tree-filesystem tree-children.collapsed {
  display: none;
}

fap-tree-filesystem tree-label.dir::before {
  content: '\1F4C1'; /* 📁 folder */
}