// File: fap_tree_menu.js

/**
 * FAP Menu Tree Component
 * Self-contained component that extends fap.treeCore for menu trees.
 * Renders inside <fap-tree-menu> elements.
 */

function renderMenuTree(container, menuTree) {
  window.fap.treeCore.renderTree(container, menuTree, {
    getLabel: node => node.label,
    getChildren: node => node.items || []
  });
}

// Enhanced initialization that automatically finds and renders menu trees
function initMenuTrees(data) {
  document.querySelectorAll('fap-tree-menu').forEach(element => {
    if (!element.hasAttribute('data-initialized')) {
      renderMenuTree(element, data);
      element.setAttribute('data-initialized', 'true');
    }
  });
}

window.fap = window.fap || {};
Object.assign(window.fap, {
  treeMenu: Object.freeze({
    render: renderMenuTree,
    init: initMenuTrees
  })
});
