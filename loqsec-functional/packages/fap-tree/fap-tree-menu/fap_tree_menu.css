/* File: fap_tree_menu.css */

/* --- Menu Tree Component --- */
fap-tree-menu {
  align-items: center;
  display: flex;
  gap: 0.5rem;
}

fap-tree-menu tree-node {
  display: inline-block;
  position: relative;
}

fap-tree-menu tree-label {
  background: gray;
  border: 1px solid darkgray;
  color: var(--fg);
  cursor: pointer;
  font-size: 0.875rem;
  padding: 0.5rem 1rem;
  transition: background 0.2s ease;
}

fap-tree-menu tree-label:hover {
  background: darkgray;
}

fap-tree-menu tree-children {
  background: var(--bg);
  border: 1px solid gray;
  box-shadow: 0 2px 8px rgba(0,0,0,0.3);
  display: none;
  left: 0;
  min-width: 150px;
  position: absolute;
  top: 100%;
  z-index: 200;
}

fap-tree-menu tree-children.show {
  display: block;
}

fap-tree-menu tree-children tree-label {
  border: none;
  border-bottom: 1px solid gray;
  display: block;
  padding: 0.5rem 1rem;
  text-align: left;
  width: 100%;
}

fap-tree-menu tree-children tree-label:hover {
  background: var(--accent);
  color: white;
}