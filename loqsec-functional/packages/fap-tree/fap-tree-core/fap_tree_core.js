/**
 * @fileoverview FAP Core Tree Renderer - Semantic tree structure generator
 * @module fap-tree-core
 * @version 1.0.0
 * <AUTHOR> Team
 * @since 2025-01-01
 */

/**
 * Creates a semantic tree node element with proper nesting and attributes.
 * Generates clean HTML using custom elements without CSS classes or inline styles.
 * 
 * @param {Object} node - The tree node data object
 * @param {Object} options - Rendering configuration object
 * @param {Function} options.getLabel - Function to extract display label from node
 * @param {Function} options.getChildren - Function to extract child nodes
 * @param {number} [level=0] - Nesting level for indentation (0-based)
 * @returns {HTMLElement} The created tree-node element
 * 
 * @example
 * const nodeEl = createNodeElement(
 *   { name: 'src', children: [...] },
 *   { 
 *     getLabel: node => node.name,
 *     getChildren: node => node.children || []
 *   },
 *   0
 * );
 */
function createNodeElement(node, options, level = 0) {
  const nodeEl = document.createElement('tree-node');
  nodeEl.setAttribute('level', level);

  const labelEl = document.createElement('tree-label');
  labelEl.textContent = options.getLabel(node);
  nodeEl.appendChild(labelEl);

  const children = options.getChildren(node);
  if (children?.length) {
    const childrenEl = document.createElement('tree-children');
    for (const child of children) {
      childrenEl.appendChild(createNodeElement(child, options, level + 1));
    }
    nodeEl.appendChild(childrenEl);
  }

  return nodeEl;
}

/**
 * Renders a complete tree structure into a container element.
 * Clears existing content and generates semantic HTML tree elements.
 * 
 * @param {HTMLElement} container - Target DOM element to render into
 * @param {Array<Object>} treeData - Array of root-level tree nodes
 * @param {Object} options - Rendering configuration object
 * @param {Function} options.getLabel - Function to extract display label from node
 * @param {Function} options.getChildren - Function to extract child nodes array
 * 
 * @example
 * renderTree(
 *   document.getElementById('my-tree'),
 *   [
 *     { name: 'folder1', children: [{ name: 'file1.txt' }] },
 *     { name: 'file2.txt' }
 *   ],
 *   {
 *     getLabel: node => node.name,
 *     getChildren: node => node.children || []
 *   }
 * );
 */
function renderTree(container, treeData, options) {
  container.innerHTML = '';
  for (const node of treeData) {
    container.appendChild(createNodeElement(node, options));
  }
}

// Initialize FAP namespace
window.fap = window.fap || {};

/**
 * FAP Core Tree component API
 * @namespace window.fap.treeCore
 * @memberof window.fap
 */
Object.assign(window.fap, {
  treeCore: Object.freeze({
    /**
     * @function renderTree
     * @memberof window.fap.treeCore
     * @description Renders semantic tree structure into DOM container
     */
    renderTree
  })
});
