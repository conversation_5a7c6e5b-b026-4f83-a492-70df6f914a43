<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>FAP Tree Component Demo</title>
    
  <link rel="stylesheet" href="../demo-css/fap-base.css">
  <link rel="stylesheet" href="./fap-tree-core/fap_tree_core.css">
  <link rel="stylesheet" href="./fap-tree-filesystem/fap_tree_filesystem.css">
  <link rel="stylesheet" href="./fap-tree-menu/fap_tree_menu.css">
</head>
<body>
  <header>
    <button onclick="window.fap.ui.toggleSidebar()">☰ Files</button>
    <h1>FAP Tree Demo</h1>
    <fap-tree-menu></fap-tree-menu>
    <fap-theme-toggle onclick="window.fap.ui.toggleTheme()">🌙 Theme</fap-theme-toggle>
  </header>

  <fap-sidebar>
    <h3>File System</h3>
    <fap-tree-filesystem></fap-tree-filesystem>
    <hr>
    <fap-demo-controls>
      <button onclick="window.fap.demo.resetTrees()">Reset Demo</button>
    </fap-demo-controls>
  </fap-sidebar>

  <main>
    <article>
      <h2>FAP Tree Component Integration</h2>
      <p>This demo shows how FAP tree components integrate into a real application layout:</p>
      <ul>
        <li>📁 <strong>File System Tree</strong>: Click "☰ Files" to open the sidebar containing a navigable file tree</li>
        <li>📋 <strong>Menu Tree</strong>: Application menus are integrated into the header for easy access</li>
        <li>🎨 <strong>Theme Toggle</strong>: Switch between light and dark themes</li>
        <li>🔄 <strong>Reset Demo</strong>: Use the reset button in the sidebar to reinitialize the trees</li>
      </ul>
      
      <h3>FAP Component Features</h3>
      <p>The tree components demonstrate key FAP principles:</p>
      <ul>
        <li>✅ <strong>Semantic HTML</strong>: Uses custom elements and proper HTML structure</li>
        <li>✅ <strong>CSS Variables</strong>: Theme-aware styling with CSS custom properties</li>
        <li>✅ <strong>Functional JavaScript</strong>: Pure functions with no classes or complex state</li>
        <li>✅ <strong>Progressive Enhancement</strong>: Works without JavaScript, enhanced with interaction</li>
        <li>✅ <strong>Zero Dependencies</strong>: No frameworks, libraries, or build tools required</li>
      </ul>

      <h3>Tree Interaction</h3>
      <p>Try these interactions:</p>
      <ul>
        <li>Click directory names to expand/collapse folders</li>
        <li>Click file names to see console log output</li>
        <li>Use the sidebar file tree for navigation</li>
        <li>Access application functions through the header menu</li>
      </ul>
    </article>
  </main>

  <footer>
    <p>&copy; 2025 FAP Tree Component Demo</p>
  </footer>

  <script src="./fap-tree-core/fap_tree_core.js"></script>
  <script src="./fap-tree-filesystem/fap_tree_filesystem.js"></script>
  <script src="./fap-tree-menu/fap_tree_menu.js"></script>
  <script>
    // --- FAP Namespace Setup ---
    window.fap = window.fap || {};
    
    // Extend existing window.fap with new functionality
    Object.assign(window.fap, {
      // Core utilities
      core: {
        version: '1.0.0',
        debug: true
      },

      // UI enhancement functions
      ui: {
        toggleSidebar() {
          const sidebar = document.querySelector('fap-sidebar')
          sidebar.classList.toggle('open')
        },

        toggleTheme() {
          document.documentElement.classList.toggle('light')
          const toggle = document.querySelector('fap-theme-toggle')
          const isLight = document.documentElement.classList.contains('light')
          toggle.textContent = isLight ? '🌙 Dark' : '☀️ Light'
          window.fap.state.theme = isLight ? 'light' : 'dark'
          window.fap.util.log(`Theme switched to ${window.fap.state.theme}`)
        }
      },

      // State management
      state: {
        sidebarOpen: false,
        theme: 'dark'
      },

      // Utility functions
      util: {
        log: (msg) => window.fap.core.debug && console.log('[FAP]', msg),
        clamp: (min, max) => x => Math.max(min, Math.min(x, max))
      },

      // Demo-specific functionality
      demo: {
        resetTrees() {
          window.fap.util.log('Resetting tree demos')
          window.fap.demo.initTrees()
        },

        initTrees() {
          // Sample file system data
          const fsTree = [
            {
              name: 'src', kind: 'directory', children: [
                { name: 'main.js', kind: 'file' },
                { name: 'components', kind: 'directory', children: [
                  { name: 'header.js', kind: 'file' },
                  { name: 'sidebar.js', kind: 'file' }
                ]},
                { name: 'lib', kind: 'directory', children: [
                  { name: 'utils.js', kind: 'file' },
                  { name: 'tree.js', kind: 'file' }
                ]}
              ]
            },
            { name: 'README.md', kind: 'file' },
            { name: 'package.json', kind: 'file' }
          ];

          // Sample menu data for header
          const menuTree = [
            {
              label: 'File', items: [
                { label: 'New File' },
                { label: 'Open File' },
                { label: 'Save' },
                { label: 'Exit' }
              ]
            },
            {
              label: 'Edit', items: [
                { label: 'Undo' },
                { label: 'Redo' },
                { label: 'Cut' },
                { label: 'Copy' },
                { label: 'Paste' }
              ]
            },
            {
              label: 'View', items: [
                { label: 'Toggle Sidebar' },
                { label: 'Toggle Theme' },
                { label: 'Full Screen' }
              ]
            }
          ];

          // Add file class to FS labels
          fsTree.forEach(node => this.markFsFiles(node));
          
          // Render file system tree in sidebar
          const filesystemElement = document.querySelector('fap-tree-filesystem');
          if (filesystemElement && window.fap.treeFilesystem) {
            window.fap.treeFilesystem.render(filesystemElement, fsTree);
            window.fap.util.log('Filesystem tree rendered in sidebar');
          }
          
          // Render menu tree in header
          const menuElement = document.querySelector('fap-tree-menu');
          if (menuElement && window.fap.treeMenu) {
            window.fap.treeMenu.render(menuElement, menuTree);
            window.fap.util.log('Menu tree rendered in header');
          }
        },

        markFsFiles(node) {
          if (node.kind === 'file') node.file = true;
          node.children?.forEach(child => this.markFsFiles(child));
        }
      }
    });

    // Enhanced tree rendering with interactivity
    function enhanceTreeInteractivity() {
      // Patch filesystem tree for interactivity
      if (window.fap.treeFilesystem && window.fap.treeFilesystem.render) {
        const origFilesystemRender = window.fap.treeFilesystem.render;
        window.fap.treeFilesystem.render = (container, data) => {
          origFilesystemRender(container, data);
          container.querySelectorAll('tree-label').forEach(el => {
            if (el.textContent && !el.textContent.endsWith('/')) {
              el.classList.add('file');
              el.onclick = () => window.fap.util.log('Clicked file: ' + el.textContent);
            } else {
              el.classList.add('dir');
              el.onclick = () => {
                const next = el.nextElementSibling;
                if (next && next.tagName.toLowerCase() === 'tree-children') {
                  next.classList.toggle('collapsed');
                }
              };
            }
          });

          container.querySelectorAll('tree-children').forEach(child => {
            child.classList.add('collapsed');
          });
        };
      }

      // Patch menu tree for dropdown functionality
      if (window.fap.treeMenu && window.fap.treeMenu.render) {
        const origMenuRender = window.fap.treeMenu.render;
        window.fap.treeMenu.render = (container, data) => {
          origMenuRender(container, data);
          
          // Close all dropdowns when clicking elsewhere
          document.addEventListener('click', (e) => {
            if (!container.contains(e.target)) {
              container.querySelectorAll('tree-children').forEach(dropdown => {
                dropdown.classList.remove('show');
              });
            }
          });
          
          container.querySelectorAll('tree-label').forEach(el => {
            const sibling = el.nextElementSibling;
            if (sibling && sibling.tagName.toLowerCase() === 'tree-children') {
              // Top-level menu button
              el.onclick = (e) => {
                e.stopPropagation();
                // Close all other dropdowns
                container.querySelectorAll('tree-children').forEach(dropdown => {
                  if (dropdown !== sibling) dropdown.classList.remove('show');
                });
                // Toggle this dropdown
                sibling.classList.toggle('show');
              };
            } else {
              // Menu item
              el.onclick = () => {
                window.fap.util.log('Selected menu: ' + el.textContent);
                // Close all dropdowns after selection
                container.querySelectorAll('tree-children').forEach(dropdown => {
                  dropdown.classList.remove('show');
                });
              };
            }
          });
        };
      }
    }

    // --- Enhancement on DOM Ready ---
    document.addEventListener('DOMContentLoaded', () => {
      try {
        window.fap.util.log('FAP Tree Demo initialized')
        
        // Setup tree interactivity first
        enhanceTreeInteractivity()
        
        // Initialize tree demos
        window.fap.demo.initTrees()

        // Close sidebar when clicking outside
        document.addEventListener('click', (e) => {
          const sidebar = document.querySelector('fap-sidebar')
          const isMenuButton = e.target.textContent && e.target.textContent.includes('☰')

          if (sidebar.classList.contains('open') &&
              !sidebar.contains(e.target) &&
              !isMenuButton) {
            sidebar.classList.remove('open')
          }
        })
        
        window.fap.util.log('Demo setup complete')
      } catch (error) {
        console.error('Failed to initialize FAP Tree Demo:', error)
      }
    })
  </script>
</body>
</html>
