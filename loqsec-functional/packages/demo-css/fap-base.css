    /* --- Base Theme Colors --- */
    :root {
      --accent: blue;
      --bg: Slategray;
      --fg: white;
      --footer-bg: darkgray;
      --header-bg: darkgray;
      --main-bg: lightgray;
    }

    :root.light {
      --accent: navy;
      --bg: white;
      --fg: black;
      --footer-bg: lightgray;
      --header-bg: lightgray;
      --main-bg: white;
    }

    /* --- CSS Reset --- */
    *, *::before, *::after {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    button, input, select, textarea {
      font: inherit;
    }

    ul, ol {
      list-style: none;
      padding: 0;
    }

    a {
      color: inherit;
      text-decoration: none;
    }

    img {
      display: block;
      max-width: 100%;
    }

    /* --- Custom FAP Elements --- */
    fap-sidebar {
      background: var(--bg);
      border-right: 1px solid gray;
      bottom: 1px;
      color: var(--fg);
      left: 0;
      padding: 1rem;
      position: fixed;
      top: 61px;
      transform: translateX(-100%);
      transition: transform 0.3s ease;
      width: 250px;
      z-index: 100;
    }

    fap-sidebar.open {
      transform: translateX(0);
    }


    fap-demo-controls {
      margin: 1rem 0;
      text-align: center;
    }

    fap-demo-controls button {
      background: var(--accent);
      border: none;
      color: white;
      cursor: pointer;
      padding: 0.5rem 1rem;
    }

    fap-demo-controls hr {
      border: 1px solid gray;
      margin: 1rem 0;
    }

    fap-theme-toggle {
      background: var(--accent);
      border: none;
      color: white;
      cursor: pointer;
      font-size: 0.875rem;
      padding: 0.5rem 1rem;
      transition: background 0.2s ease;
    }

    fap-theme-toggle:hover {
      background: darkblue;
    }

    /* --- Full Viewport Layout --- */
    html, body {
      background-color: var(--bg);
      bottom: 0;
      color: black;
      font-family: system-ui, sans-serif;
      font-size: 16px;
      left: 0;
      line-height: 1.5;
      overflow: hidden;
      position: absolute;
      right: 0;
      top: 0;
    }

    header, footer {
      align-items: center;
      background-color: var(--header-bg);
      display: flex;
      height: 60px;
      justify-content: space-between;
      left: 1px;
      padding: 0 1rem;
      position: absolute;
      right: 1px;
    }

    header {
      top: 1px;
    }

    header h1 {
      color: var(--fg);
      font-size: 1.5rem;
      font-weight: bold;
      margin: 0;
    }

    header button {
      background: gray;
      border: 1px solid darkgray;
      color: var(--fg);
      cursor: pointer;
      font-size: 0.875rem;
      padding: 0.5rem 1rem;
      transition: background 0.2s ease;
    }

    header button:hover {
      background: darkgray;
    }

    footer {
      bottom: 1px;
      color: var(--fg);
      font-size: 0.875rem;
      justify-content: center;
    }

    main {
      background-color: var(--main-bg);
      bottom: 62px;
      color: black;
      left: 1px;
      overflow-y: auto;
      padding: 2rem;
      position: absolute;
      right: 1px;
      top: 62px;
    }