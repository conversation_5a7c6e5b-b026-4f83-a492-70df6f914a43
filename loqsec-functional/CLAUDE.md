# CLAUDE.md - FAP Implementation

FAP-based functional reimplementation of Logseq with zero dependencies.

## Quick Demo

Open `packages/fap-tree/fap-tree-demo.html` to see working FAP components.

## Current Structure

```
├── packages/fap-tree/     # ✅ Tree components (working reference)
├── packages/demo-css/     # Base styling system
└── index.html            # Main application (planned)
```

## Development

**No Build Process**: Open HTML files directly in browser
- Live Server for development 
- Instant debugging with DevTools
- Visual testing with screenshots

## FAP Patterns

See `../../_fap/design_guides/fap_best_practices.md` for complete implementation patterns.

**Component Template:**
```js
window.fap = window.fap || {};
Object.assign(window.fap, {
  componentName: Object.freeze({
    render: (container, data) => { /* pure function */ }
  })
});
```

## Status

- ✅ **Tree Component** - File system & menu variants working
- ✅ **FAP Architecture** - Established patterns and safety
- 🔄 **Block Editor** - Next development priority
- ⏳ **Full Application** - Integration phase

**Reference Implementation**: `fap-tree` package demonstrates all FAP principles.