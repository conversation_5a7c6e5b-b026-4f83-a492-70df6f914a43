lockfileVersion: '9.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:

  .:
    dependencies:
      '@isomorphic-git/lightning-fs':
        specifier: ^4.6.2
        version: 4.6.2
      '@sqlite.org/sqlite-wasm':
        specifier: ^3.50.3-build1
        version: 3.50.3-build1
      dompurify:
        specifier: ^3.2.6
        version: 3.2.6
      fuse.js:
        specifier: ^7.1.0
        version: 7.1.0
      marked:
        specifier: ^16.1.1
        version: 16.1.1
      pdfjs-dist:
        specifier: ^5.4.54
        version: 5.4.54
    devDependencies:
      playwright:
        specifier: ^1.54.1
        version: 1.54.1

packages:

  '@isomorphic-git/idb-keyval@3.3.2':
    resolution: {integrity: sha512-r8/AdpiS0/WJCNR/t/gsgL+M8NMVj/ek7s60uz3LmpCaTF2mEVlZJlB01ZzalgYzRLXwSPC92o+pdzjM7PN/pA==}

  '@isomorphic-git/lightning-fs@4.6.2':
    resolution: {integrity: sha512-RS/oa1UBnoUFe56bsjOEgoUUReYKQzYUlQnbERRRNv9s9KmjyWuuylPV+YgsWirR2oONKaipWYMebVQ8SAe55Q==}
    hasBin: true

  '@napi-rs/canvas-android-arm64@0.1.74':
    resolution: {integrity: sha512-aq5ode+9Z/ZR0H485dI2jdRdttg/hl9Ob+iPCt0nj+QFiirpxDrbUHKeTZWQWEtkWyC7vI5R2dMTbDINBfl9eg==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [android]

  '@napi-rs/canvas-darwin-arm64@0.1.74':
    resolution: {integrity: sha512-eO5Miz+ef1dEQyUMWDdcbAb1Wr7yMyxD9/CL9d4frQxO4pTTaCiMBUWup8XDPLr/g7XkSkGCZLP47xiXiyXSpQ==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [darwin]

  '@napi-rs/canvas-darwin-x64@0.1.74':
    resolution: {integrity: sha512-0EkO0IFkps7C3JpKC7lbM3IL+QDUYeUKagHLDbUry4PeQTghxp6JcgccpmU32ZbpFZgPnm7o0tTJO0J1d8S2rA==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [darwin]

  '@napi-rs/canvas-linux-arm-gnueabihf@0.1.74':
    resolution: {integrity: sha512-qAVJEN2JqGayEI1kSpJy1Xr6ZmCFV9QhRyV35yWsS7e9X1jm+T4DAlCxI4PlKIlqVSzYMYhKrxchST20XBSzHg==}
    engines: {node: '>= 10'}
    cpu: [arm]
    os: [linux]

  '@napi-rs/canvas-linux-arm64-gnu@0.1.74':
    resolution: {integrity: sha512-lOnop22qy6MYxI94GGunMMjo6D80I//2W/6pqKUfwXaDQtOfvHsTcVVzDu5cFXUTNrb9ZRfMCeol5YEd+9FJvg==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [linux]

  '@napi-rs/canvas-linux-arm64-musl@0.1.74':
    resolution: {integrity: sha512-tfFqLHGtSEabBigOnPUfZviSTGmW2xHv5tYZYPBWmgGiTkoNJ7lEWFUxHjwvV5HXGqLs8ok/O7g1enSpxO6lmQ==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [linux]

  '@napi-rs/canvas-linux-riscv64-gnu@0.1.74':
    resolution: {integrity: sha512-j6H9dHTMtr1y3tu/zGm1ythYIL9vTl4EEv9f6CMx0n3Zn2M+OruUUwh9ylCj4afzSNEK9T8cr6zMnmTPzkpBvQ==}
    engines: {node: '>= 10'}
    cpu: [riscv64]
    os: [linux]

  '@napi-rs/canvas-linux-x64-gnu@0.1.74':
    resolution: {integrity: sha512-73DIV4E7Y9CpIJuUXVl9H6+MEQXyRy4VJQoUGA1tOlcKQiStxqhq6UErL4decI28NxjyQXBhtYZKj5q8AJEuOg==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [linux]

  '@napi-rs/canvas-linux-x64-musl@0.1.74':
    resolution: {integrity: sha512-FgDMEFdGIJT3I2xejflRJ82/ZgDphyirS43RgtoLaIXI6zihLiZcQ7rczpqeWgAwlJNjR0He2EustsKe1SkUOg==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [linux]

  '@napi-rs/canvas-win32-x64-msvc@0.1.74':
    resolution: {integrity: sha512-x6bhwlhn0wU7dfiP46mt5Bi6PowSUH4CJ4PTzGj58LRQ1HVasEIJgoMx7MLC48F738eJpzbfg3WR/D8+e9CeTA==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [win32]

  '@napi-rs/canvas@0.1.74':
    resolution: {integrity: sha512-pOIyzuS+5Bz1vAhD7tdhaw5/936mMJZUn4aVajojUdjYOGSWmfpDYSgt0nQLZPZVN5GLgWgutqXPOi7Jsm3k+Q==}
    engines: {node: '>= 10'}

  '@sqlite.org/sqlite-wasm@3.50.3-build1':
    resolution: {integrity: sha512-NU+I7KJ5kpMZNyZtJ9hOLlhQHJAA3fJhtkE7kf3C0SlGg4ayz6AkqxcaDcR4qOsrz1XP2+yM1yORaLCt55XDQg==}
    hasBin: true

  '@types/trusted-types@2.0.7':
    resolution: {integrity: sha512-ScaPdn1dQczgbl0QFTeTOmVHFULt394XJgOQNoyVhZ6r2vLnMLJfBPd53SB52T/3G36VI1/g2MZaX0cwDuXsfw==}

  dompurify@3.2.6:
    resolution: {integrity: sha512-/2GogDQlohXPZe6D6NOgQvXLPSYBqIWMnZ8zzOhn09REE4eyAzb+Hed3jhoM9OkuaJ8P6ZGTTVWQKAi8ieIzfQ==}

  fast-text-encoding@1.0.6:
    resolution: {integrity: sha512-VhXlQgj9ioXCqGstD37E/HBeqEGV/qOD/kmbVG8h5xKBYvM1L3lR1Zn4555cQ8GkYbJa8aJSipLPndE1k6zK2w==}

  fsevents@2.3.2:
    resolution: {integrity: sha512-xiqMQR4xAeHTuB9uWm+fFRcIOgKBMiOBP+eXiyT7jsgVCq1bkVygt00oASowB7EdtpOHaaPgKt812P9ab+DDKA==}
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]

  fuse.js@7.1.0:
    resolution: {integrity: sha512-trLf4SzuuUxfusZADLINj+dE8clK1frKdmqiJNb1Es75fmI5oY6X2mxLVUciLLjxqw/xr72Dhy+lER6dGd02FQ==}
    engines: {node: '>=10'}

  isomorphic-textencoder@1.0.1:
    resolution: {integrity: sha512-676hESgHullDdHDsj469hr+7t3i/neBKU9J7q1T4RHaWwLAsaQnywC0D1dIUId0YZ+JtVrShzuBk1soo0+GVcQ==}

  just-debounce-it@1.1.0:
    resolution: {integrity: sha512-87Nnc0qZKgBZuhFZjYVjSraic0x7zwjhaTMrCKlj0QYKH6lh0KbFzVnfu6LHan03NO7J8ygjeBeD0epejn5Zcg==}

  just-once@1.1.0:
    resolution: {integrity: sha512-+rZVpl+6VyTilK7vB/svlMPil4pxqIJZkbnN7DKZTOzyXfun6ZiFeq2Pk4EtCEHZ0VU4EkdFzG8ZK5F3PErcDw==}

  marked@16.1.1:
    resolution: {integrity: sha512-ij/2lXfCRT71L6u0M29tJPhP0bM5shLL3u5BePhFwPELj2blMJ6GDtD7PfJhRLhJ/c2UwrK17ySVcDzy2YHjHQ==}
    engines: {node: '>= 20'}
    hasBin: true

  pdfjs-dist@5.4.54:
    resolution: {integrity: sha512-TBAiTfQw89gU/Z4LW98Vahzd2/LoCFprVGvGbTgFt+QCB1F+woyOPmNNVgLa6djX9Z9GGTnj7qE1UzpOVJiINw==}
    engines: {node: '>=20.16.0 || >=22.3.0'}

  playwright-core@1.54.1:
    resolution: {integrity: sha512-Nbjs2zjj0htNhzgiy5wu+3w09YetDx5pkrpI/kZotDlDUaYk0HVA5xrBVPdow4SAUIlhgKcJeJg4GRKW6xHusA==}
    engines: {node: '>=18'}
    hasBin: true

  playwright@1.54.1:
    resolution: {integrity: sha512-peWpSwIBmSLi6aW2auvrUtf2DqY16YYcCMO8rTVx486jKmDTJg7UAhyrraP98GB8BoPURZP8+nxO7TSd4cPr5g==}
    engines: {node: '>=18'}
    hasBin: true

snapshots:

  '@isomorphic-git/idb-keyval@3.3.2': {}

  '@isomorphic-git/lightning-fs@4.6.2':
    dependencies:
      '@isomorphic-git/idb-keyval': 3.3.2
      isomorphic-textencoder: 1.0.1
      just-debounce-it: 1.1.0
      just-once: 1.1.0

  '@napi-rs/canvas-android-arm64@0.1.74':
    optional: true

  '@napi-rs/canvas-darwin-arm64@0.1.74':
    optional: true

  '@napi-rs/canvas-darwin-x64@0.1.74':
    optional: true

  '@napi-rs/canvas-linux-arm-gnueabihf@0.1.74':
    optional: true

  '@napi-rs/canvas-linux-arm64-gnu@0.1.74':
    optional: true

  '@napi-rs/canvas-linux-arm64-musl@0.1.74':
    optional: true

  '@napi-rs/canvas-linux-riscv64-gnu@0.1.74':
    optional: true

  '@napi-rs/canvas-linux-x64-gnu@0.1.74':
    optional: true

  '@napi-rs/canvas-linux-x64-musl@0.1.74':
    optional: true

  '@napi-rs/canvas-win32-x64-msvc@0.1.74':
    optional: true

  '@napi-rs/canvas@0.1.74':
    optionalDependencies:
      '@napi-rs/canvas-android-arm64': 0.1.74
      '@napi-rs/canvas-darwin-arm64': 0.1.74
      '@napi-rs/canvas-darwin-x64': 0.1.74
      '@napi-rs/canvas-linux-arm-gnueabihf': 0.1.74
      '@napi-rs/canvas-linux-arm64-gnu': 0.1.74
      '@napi-rs/canvas-linux-arm64-musl': 0.1.74
      '@napi-rs/canvas-linux-riscv64-gnu': 0.1.74
      '@napi-rs/canvas-linux-x64-gnu': 0.1.74
      '@napi-rs/canvas-linux-x64-musl': 0.1.74
      '@napi-rs/canvas-win32-x64-msvc': 0.1.74
    optional: true

  '@sqlite.org/sqlite-wasm@3.50.3-build1': {}

  '@types/trusted-types@2.0.7':
    optional: true

  dompurify@3.2.6:
    optionalDependencies:
      '@types/trusted-types': 2.0.7

  fast-text-encoding@1.0.6: {}

  fsevents@2.3.2:
    optional: true

  fuse.js@7.1.0: {}

  isomorphic-textencoder@1.0.1:
    dependencies:
      fast-text-encoding: 1.0.6

  just-debounce-it@1.1.0: {}

  just-once@1.1.0: {}

  marked@16.1.1: {}

  pdfjs-dist@5.4.54:
    optionalDependencies:
      '@napi-rs/canvas': 0.1.74

  playwright-core@1.54.1: {}

  playwright@1.54.1:
    dependencies:
      playwright-core: 1.54.1
    optionalDependencies:
      fsevents: 2.3.2
