/**
 * FAP Core Utilities
 * Shared utilities and patterns for all playground versions
 */

export class FAPCore {
  
  /**
   * Register a custom element with FAP patterns
   */
  static defineComponent(name, elementClass) {
    if (!customElements.get(name)) {
      customElements.define(name, elementClass);
      console.log(`✅ Registered component: ${name}`);
    }
  }
  
  /**
   * Create reactive state with simple observer pattern
   */
  static createReactiveState(initialState = {}) {
    const state = { ...initialState };
    const observers = new Set();
    
    return {
      get: (key) => state[key],
      set: (key, value) => {
        const oldValue = state[key];
        state[key] = value;
        observers.forEach(fn => fn(key, value, oldValue));
      },
      getAll: () => ({ ...state }),
      subscribe: (fn) => {
        observers.add(fn);
        return () => observers.delete(fn);
      },
      update: (updates) => {
        Object.entries(updates).forEach(([key, value]) => {
          this.set(key, value);
        });
      }
    };
  }
  
  /**
   * Simple event bus for component communication
   */
  static createEventBus() {
    const events = new Map();
    
    return {
      on: (event, handler) => {
        if (!events.has(event)) events.set(event, new Set());
        events.get(event).add(handler);
      },
      off: (event, handler) => {
        if (events.has(event)) {
          events.get(event).delete(handler);
        }
      },
      emit: (event, data) => {
        if (events.has(event)) {
          events.get(event).forEach(handler => handler(data));
        }
      }
    };
  }
  
  /**
   * CSS utility functions
   */
  static css = {
    // Create CSS custom properties for theming
    setTheme: (properties) => {
      const root = document.documentElement;
      Object.entries(properties).forEach(([key, value]) => {
        root.style.setProperty(`--${key}`, value);
      });
    },
    
    // Toggle dark/light theme
    toggleTheme: () => {
      const root = document.documentElement;
      const isDark = root.getAttribute('data-theme') === 'dark';
      root.setAttribute('data-theme', isDark ? 'light' : 'dark');
      return !isDark;
    },
    
    // Get current theme
    getTheme: () => {
      return document.documentElement.getAttribute('data-theme') || 'light';
    }
  };
  
  /**
   * DOM utilities following FAP patterns
   */
  static dom = {
    // Create element with attributes and children
    create: (tag, attrs = {}, children = []) => {
      const element = document.createElement(tag);
      
      Object.entries(attrs).forEach(([key, value]) => {
        if (key.startsWith('on')) {
          element.addEventListener(key.slice(2).toLowerCase(), value);
        } else {
          element.setAttribute(key, value);
        }
      });
      
      children.forEach(child => {
        if (typeof child === 'string') {
          element.appendChild(document.createTextNode(child));
        } else {
          element.appendChild(child);
        }
      });
      
      return element;
    },
    
    // Find component by custom element name
    findComponent: (selector, root = document) => {
      return root.querySelector(selector);
    },
    
    // Find all components
    findAllComponents: (selector, root = document) => {
      return Array.from(root.querySelectorAll(selector));
    }
  };
  
  /**
   * Validation utilities
   */
  static validate = {
    // Validate HTML element names
    isValidElementName: (name) => {
      return /^[a-z][a-z0-9]*(-[a-z0-9]+)*$/.test(name) && name.includes('-');
    },
    
    // Sanitize text content
    sanitizeText: (text) => {
      const div = document.createElement('div');
      div.textContent = text;
      return div.innerHTML;
    },
    
    // Validate block ID format
    isValidBlockId: (id) => {
      return /^[a-zA-Z0-9-_]+$/.test(id);
    }
  };
  
  /**
   * Storage utilities
   */
  static storage = {
    // Simple localStorage wrapper
    local: {
      get: (key, defaultValue = null) => {
        try {
          const value = localStorage.getItem(key);
          return value ? JSON.parse(value) : defaultValue;
        } catch {
          return defaultValue;
        }
      },
      set: (key, value) => {
        try {
          localStorage.setItem(key, JSON.stringify(value));
          return true;
        } catch {
          return false;
        }
      },
      remove: (key) => {
        localStorage.removeItem(key);
      }
    }
  };
  
  /**
   * Logging utilities with context
   */
  static log = {
    info: (component, message, data = null) => {
      console.log(`[${component}] ${message}`, data || '');
    },
    warn: (component, message, data = null) => {
      console.warn(`[${component}] ${message}`, data || '');
    },
    error: (component, message, error = null) => {
      console.error(`[${component}] ${message}`, error || '');
    }
  };
}

// Global state instance
export const GlobalState = FAPCore.createReactiveState({
  theme: 'light',
  sidebarOpen: false,
  currentPage: 'welcome',
  currentPageUuid: null,
  blocks: [],
  pages: [],
  database: null // Will hold SQLite instance
});

// Global event bus
export const EventBus = FAPCore.createEventBus();

// Version tracking
export const Version = {
  current: 'v001-basic-foundation',
  features: ['basic-blocks', 'simple-state', 'theme-system']
};