/**
 * Shared Dependencies Manager
 * Provides access to third-party libraries from the root node_modules
 */

// Core dependencies available to all playground versions
export const Dependencies = {
  // File system for browser storage
  LightningFS: null, // Will be loaded dynamically
  
  // HTML sanitization for security
  DOMPurify: null,
  
  // Markdown parsing
  marked: null,
  
  // Fuzzy search
  Fuse: null,
  
  // Load all dependencies
  async load() {
    try {
      // Import from CDN or local node_modules as fallback
      this.DOMPurify = await this.loadDOMPurify();
      this.marked = await this.loadMarked();
      this.Fuse = await this.loadFuse();
      this.LightningFS = await this.loadLightningFS();
      
      console.log('✅ All dependencies loaded successfully');
      return true;
    } catch (error) {
      console.error('❌ Failed to load dependencies:', error);
      return false;
    }
  },
  
  async loadDOMPurify() {
    try {
      // Try CDN first for playground ease
      const script = document.createElement('script');
      script.src = 'https://cdn.jsdelivr.net/npm/dompurify@3.0.0/dist/purify.min.js';
      document.head.appendChild(script);
      
      return new Promise((resolve) => {
        script.onload = () => resolve(window.DOMPurify);
        script.onerror = () => {
          console.warn('CDN failed, using fallback for DOMPurify');
          resolve(null); // Fallback to simple sanitization
        };
      });
    } catch (error) {
      console.warn('DOMPurify not available, using simple sanitization');
      return null;
    }
  },
  
  async loadMarked() {
    try {
      const script = document.createElement('script');
      script.src = 'https://cdn.jsdelivr.net/npm/marked@4.3.0/marked.min.js';
      document.head.appendChild(script);
      
      return new Promise((resolve) => {
        script.onload = () => resolve(window.marked);
        script.onerror = () => {
          console.warn('CDN failed for marked, using simple markdown');
          resolve(this.simpleMarkdown);
        };
      });
    } catch (error) {
      return this.simpleMarkdown;
    }
  },
  
  async loadFuse() {
    try {
      const script = document.createElement('script');
      script.src = 'https://cdn.jsdelivr.net/npm/fuse.js@6.6.2/dist/fuse.min.js';
      document.head.appendChild(script);
      
      return new Promise((resolve) => {
        script.onload = () => resolve(window.Fuse);
        script.onerror = () => {
          console.warn('CDN failed for Fuse.js, using simple search');
          resolve(this.simpleSearch);
        };
      });
    } catch (error) {
      return this.simpleSearch;
    }
  },
  
  async loadLightningFS() {
    try {
      const script = document.createElement('script');
      script.src = 'https://cdn.jsdelivr.net/npm/@isomorphic-git/lightning-fs@4.6.0/dist/lightning-fs.min.js';
      document.head.appendChild(script);
      
      return new Promise((resolve) => {
        script.onload = () => resolve(window.LightningFS);
        script.onerror = () => {
          console.warn('CDN failed for LightningFS, using simple storage');
          resolve(this.simpleStorage);
        };
      });
    } catch (error) {
      return this.simpleStorage;
    }
  },
  
  // Fallback implementations
  simpleMarkdown: {
    parse: (text) => {
      return text
        .replace(/^# (.*$)/gim, '<h1>$1</h1>')
        .replace(/^## (.*$)/gim, '<h2>$1</h2>')
        .replace(/\*\*(.*)\*\*/gim, '<strong>$1</strong>')
        .replace(/\*(.*)\*/gim, '<em>$1</em>')
        .replace(/`(.*)`/gim, '<code>$1</code>');
    }
  },
  
  simpleSearch: class {
    constructor(data) {
      this.data = data;
    }
    search(query) {
      return this.data.filter(item => 
        JSON.stringify(item).toLowerCase().includes(query.toLowerCase())
      );
    }
  },
  
  simpleStorage: {
    promises: {
      writeFile: async (path, content) => {
        localStorage.setItem(`fs:${path}`, content);
      },
      readFile: async (path) => {
        const content = localStorage.getItem(`fs:${path}`);
        if (content === null) throw new Error('File not found');
        return content;
      },
      readdir: async (path) => {
        const keys = Object.keys(localStorage)
          .filter(key => key.startsWith(`fs:${path}`))
          .map(key => key.replace(`fs:${path}/`, ''));
        return keys;
      }
    }
  }
};

// Auto-load when imported
Dependencies.load();