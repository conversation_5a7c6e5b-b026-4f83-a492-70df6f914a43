/**
 * Shared Dependencies Manager
 * Provides access to third-party libraries from the root node_modules
 */

// Core dependencies available to all playground versions
export const Dependencies = {
  // Database for persistence
  SQLite: null, // Will be loaded dynamically
  
  // HTML sanitization for security
  DOMPurify: null,
  
  // Markdown parsing
  marked: null,
  
  // Fuzzy search
  Fuse: null,
  
  // Load all dependencies
  async load() {
    try {
      // Import from CDN or local node_modules as fallback
      this.DOMPurify = await this.loadDOMPurify();
      this.marked = await this.loadMarked();
      this.Fuse = await this.loadFuse();
      this.SQLite = await this.loadSQLiteWASM();
      
      console.log('✅ All dependencies loaded successfully');
      return true;
    } catch (error) {
      console.error('❌ Failed to load dependencies:', error);
      return false;
    }
  },
  
  async loadDOMPurify() {
    try {
      // Try CDN first for playground ease
      const script = document.createElement('script');
      script.src = 'https://cdn.jsdelivr.net/npm/dompurify@3.0.0/dist/purify.min.js';
      document.head.appendChild(script);
      
      return new Promise((resolve) => {
        script.onload = () => resolve(window.DOMPurify);
        script.onerror = () => {
          console.warn('CDN failed, using fallback for DOMPurify');
          resolve(null); // Fallback to simple sanitization
        };
      });
    } catch (error) {
      console.warn('DOMPurify not available, using simple sanitization');
      return null;
    }
  },
  
  async loadMarked() {
    try {
      const script = document.createElement('script');
      script.src = 'https://cdn.jsdelivr.net/npm/marked@4.3.0/marked.min.js';
      document.head.appendChild(script);
      
      return new Promise((resolve) => {
        script.onload = () => resolve(window.marked);
        script.onerror = () => {
          console.warn('CDN failed for marked, using simple markdown');
          resolve(this.simpleMarkdown);
        };
      });
    } catch (error) {
      return this.simpleMarkdown;
    }
  },
  
  async loadFuse() {
    try {
      const script = document.createElement('script');
      script.src = 'https://cdn.jsdelivr.net/npm/fuse.js@6.6.2/dist/fuse.min.js';
      document.head.appendChild(script);
      
      return new Promise((resolve) => {
        script.onload = () => resolve(window.Fuse);
        script.onerror = () => {
          console.warn('CDN failed for Fuse.js, using simple search');
          resolve(this.simpleSearch);
        };
      });
    } catch (error) {
      return this.simpleSearch;
    }
  },
  
  async loadSQLiteWASM() {
    try {
      // Load SQLite WASM from CDN
      const script = document.createElement('script');
      script.src = 'https://cdn.jsdelivr.net/npm/@sqlite.org/sqlite-wasm@3.45.0/sqlite-wasm.js';
      script.type = 'module';
      document.head.appendChild(script);
      
      return new Promise((resolve) => {
        script.onload = async () => {
          // Initialize SQLite WASM
          const sqlite3InitModule = window.sqlite3InitModule;
          if (sqlite3InitModule) {
            const sqlite3 = await sqlite3InitModule();
            resolve(new FAPSQLiteWrapper(sqlite3));
          } else {
            console.warn('SQLite WASM failed to load, using simple storage');
            resolve(this.simpleStorage);
          }
        };
        script.onerror = () => {
          console.warn('CDN failed for SQLite WASM, using simple storage');
          resolve(this.simpleStorage);
        };
      });
    } catch (error) {
      console.warn('SQLite WASM error:', error);
      return this.simpleStorage;
    }
  },
  
  // Fallback implementations
  simpleMarkdown: {
    parse: (text) => {
      return text
        .replace(/^# (.*$)/gim, '<h1>$1</h1>')
        .replace(/^## (.*$)/gim, '<h2>$1</h2>')
        .replace(/\*\*(.*)\*\*/gim, '<strong>$1</strong>')
        .replace(/\*(.*)\*/gim, '<em>$1</em>')
        .replace(/`(.*)`/gim, '<code>$1</code>');
    }
  },
  
  simpleSearch: class {
    constructor(data) {
      this.data = data;
    }
    search(query) {
      return this.data.filter(item => 
        JSON.stringify(item).toLowerCase().includes(query.toLowerCase())
      );
    }
  },
  
  simpleStorage: {
    promises: {
      writeFile: async (path, content) => {
        localStorage.setItem(`fs:${path}`, content);
      },
      readFile: async (path) => {
        const content = localStorage.getItem(`fs:${path}`);
        if (content === null) throw new Error('File not found');
        return content;
      },
      readdir: async (path) => {
        const keys = Object.keys(localStorage)
          .filter(key => key.startsWith(`fs:${path}`))
          .map(key => key.replace(`fs:${path}/`, ''));
        return keys;
      }
    }
  }
};

/**
 * FAP SQLite Wrapper
 * Provides simple interface for SQLite WASM with FAP patterns
 */
class FAPSQLiteWrapper {
  constructor(sqlite3) {
    this.sqlite3 = sqlite3;
    this.db = null;
    this.subscribers = new Map();
    this.isInitialized = false;
  }
  
  async init() {
    if (this.isInitialized) return;
    
    try {
      // Create database in OPFS (Origin Private File System)
      if (this.sqlite3.opfs && this.sqlite3.opfs.OpfsDb) {
        this.db = new this.sqlite3.opfs.OpfsDb('/fap-logseq.db');
      } else {
        // Fallback to in-memory database
        this.db = new this.sqlite3.oo1.DB();
      }
      
      // Initialize schema
      await this.initSchema();
      this.isInitialized = true;
      console.log('✅ SQLite WASM database initialized');
    } catch (error) {
      console.error('❌ Failed to initialize SQLite:', error);
      throw error;
    }
  }
  
  async initSchema() {
    const schema = `
      -- Blocks table for content
      CREATE TABLE IF NOT EXISTS blocks (
        uuid TEXT PRIMARY KEY,
        content TEXT NOT NULL,
        parent_uuid TEXT,
        page_uuid TEXT,
        properties TEXT, -- JSON string
        marker TEXT, -- TODO, DONE, etc.
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL,
        FOREIGN KEY (parent_uuid) REFERENCES blocks(uuid),
        FOREIGN KEY (page_uuid) REFERENCES pages(uuid)
      );
      
      -- Pages table
      CREATE TABLE IF NOT EXISTS pages (
        uuid TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        properties TEXT, -- JSON string
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL
      );
      
      -- Full-text search for blocks
      CREATE VIRTUAL TABLE IF NOT EXISTS blocks_fts USING fts5(
        uuid UNINDEXED,
        content,
        page_title,
        content='blocks',
        content_rowid='rowid'
      );
      
      -- Triggers to keep FTS in sync
      CREATE TRIGGER IF NOT EXISTS blocks_fts_insert AFTER INSERT ON blocks BEGIN
        INSERT INTO blocks_fts(uuid, content, page_title) 
        SELECT new.uuid, new.content, COALESCE(p.title, '') 
        FROM pages p WHERE p.uuid = new.page_uuid;
      END;
      
      CREATE TRIGGER IF NOT EXISTS blocks_fts_update AFTER UPDATE ON blocks BEGIN
        UPDATE blocks_fts SET content = new.content WHERE uuid = new.uuid;
      END;
      
      CREATE TRIGGER IF NOT EXISTS blocks_fts_delete AFTER DELETE ON blocks BEGIN
        DELETE FROM blocks_fts WHERE uuid = old.uuid;
      END;
      
      -- Indexes for performance
      CREATE INDEX IF NOT EXISTS idx_blocks_page ON blocks(page_uuid);
      CREATE INDEX IF NOT EXISTS idx_blocks_parent ON blocks(parent_uuid);
      CREATE INDEX IF NOT EXISTS idx_blocks_updated ON blocks(updated_at);
    `;
    
    this.db.exec(schema);
  }
  
  // Simple query interface
  async query(sql, params = []) {
    if (!this.isInitialized) await this.init();
    
    try {
      const result = this.db.exec({
        sql: sql,
        bind: params,
        returnValue: 'resultRows'
      });
      
      this.notifySubscribers(sql);
      return result;
    } catch (error) {
      console.error('SQL Error:', error, { sql, params });
      throw error;
    }
  }
  
  // Create block
  async createBlock(content, parentUuid = null, pageUuid = null) {
    const uuid = crypto.randomUUID();
    const now = Date.now();
    
    await this.query(`
      INSERT INTO blocks (uuid, content, parent_uuid, page_uuid, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?)
    `, [uuid, content, parentUuid, pageUuid, now, now]);
    
    return uuid;
  }
  
  // Update block
  async updateBlock(uuid, content) {
    const now = Date.now();
    await this.query(`
      UPDATE blocks SET content = ?, updated_at = ? WHERE uuid = ?
    `, [content, now, uuid]);
  }
  
  // Get blocks for a page
  async getPageBlocks(pageUuid) {
    return await this.query(`
      WITH RECURSIVE block_tree AS (
        -- Root blocks (no parent)
        SELECT uuid, content, parent_uuid, page_uuid, 0 as level
        FROM blocks 
        WHERE page_uuid = ? AND parent_uuid IS NULL
        
        UNION ALL
        
        -- Child blocks
        SELECT b.uuid, b.content, b.parent_uuid, b.page_uuid, bt.level + 1
        FROM blocks b
        JOIN block_tree bt ON b.parent_uuid = bt.uuid
      )
      SELECT * FROM block_tree ORDER BY level, created_at
    `, [pageUuid]);
  }
  
  // Search blocks
  async searchBlocks(query) {
    return await this.query(`
      SELECT b.uuid, b.content, p.title as page_title,
             highlight(blocks_fts, 1, '<mark>', '</mark>') as highlighted_content
      FROM blocks_fts 
      JOIN blocks b ON blocks_fts.uuid = b.uuid
      LEFT JOIN pages p ON b.page_uuid = p.uuid
      WHERE blocks_fts MATCH ?
      ORDER BY rank
      LIMIT 50
    `, [query]);
  }
  
  // Create page
  async createPage(title) {
    const uuid = crypto.randomUUID();
    const now = Date.now();
    
    await this.query(`
      INSERT INTO pages (uuid, title, created_at, updated_at)
      VALUES (?, ?, ?, ?)
    `, [uuid, title, now, now]);
    
    return uuid;
  }
  
  // Get all pages
  async getPages() {
    return await this.query(`
      SELECT uuid, title, created_at, updated_at
      FROM pages 
      ORDER BY title
    `);
  }
  
  // Reactive subscriptions
  subscribe(queryPattern, callback) {
    if (!this.subscribers.has(queryPattern)) {
      this.subscribers.set(queryPattern, new Set());
    }
    this.subscribers.get(queryPattern).add(callback);
    
    return () => {
      const subs = this.subscribers.get(queryPattern);
      if (subs) {
        subs.delete(callback);
        if (subs.size === 0) {
          this.subscribers.delete(queryPattern);
        }
      }
    };
  }
  
  notifySubscribers(sql) {
    // Simple pattern matching for subscriptions
    this.subscribers.forEach((callbacks, pattern) => {
      if (sql.toLowerCase().includes(pattern.toLowerCase())) {
        callbacks.forEach(callback => callback());
      }
    });
  }
}

// Make FAPSQLiteWrapper available globally
window.FAPSQLiteWrapper = FAPSQLiteWrapper;

// Auto-load when imported
Dependencies.load();