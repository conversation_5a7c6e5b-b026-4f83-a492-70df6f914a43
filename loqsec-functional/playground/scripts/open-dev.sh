#!/bin/bash

# Simple development script
# Opens current playground version in default browser

PLAYGROUND_DIR="$(dirname "$0")/.."
CURRENT_DIR="$PLAYGROUND_DIR/current"

if [ ! -L "$CURRENT_DIR" ]; then
    echo "❌ No current playground version found."
    echo "Run: npm run new-version basic-foundation"
    exit 1
fi

INDEX_FILE="$CURRENT_DIR/index.html"

if [ ! -f "$INDEX_FILE" ]; then
    echo "❌ index.html not found in current version"
    exit 1
fi

echo "🚀 Opening $(readlink $CURRENT_DIR) in browser..."

# Open in default browser (works on macOS, Linux with xdg-open)
if command -v open >/dev/null 2>&1; then
    open "$INDEX_FILE"
elif command -v xdg-open >/dev/null 2>&1; then
    xdg-open "$INDEX_FILE"
else
    echo "📁 Open this file in your browser:"
    echo "   file://$(realpath "$INDEX_FILE")"
fi