#!/usr/bin/env node

/**
 * Version Cloning Script
 * Creates a new playground version by copying an existing one
 * 
 * Usage: npm run new-version <source-version> <new-version-name>
 * Example: npm run new-version v001-basic-foundation sidebar-navigation
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));
const playgroundDir = path.join(__dirname, '..');

function getNextVersionNumber() {
  const versions = fs.readdirSync(playgroundDir)
    .filter(name => name.startsWith('v') && name.match(/^v\d{3}-/))
    .map(name => parseInt(name.substring(1, 4)))
    .sort((a, b) => b - a);
  
  return versions.length > 0 ? versions[0] + 1 : 1;
}

function copyDirectory(src, dest) {
  if (!fs.existsSync(dest)) {
    fs.mkdirSync(dest, { recursive: true });
  }
  
  const entries = fs.readdirSync(src, { withFileTypes: true });
  
  entries.forEach(entry => {
    const srcPath = path.join(src, entry.name);
    const destPath = path.join(dest, entry.name);
    
    if (entry.isDirectory()) {
      copyDirectory(srcPath, destPath);
    } else {
      fs.copyFileSync(srcPath, destPath);
    }
  });
}

function updateSymlink(newVersionDir) {
  const symlinkPath = path.join(playgroundDir, 'current');
  
  // Remove existing symlink
  if (fs.existsSync(symlinkPath)) {
    fs.unlinkSync(symlinkPath);
  }
  
  // Create new symlink
  fs.symlinkSync(path.basename(newVersionDir), symlinkPath);
  console.log(`✅ Updated 'current' symlink to point to ${path.basename(newVersionDir)}`);
}

function updateReadme(newVersionDir, versionName, sourceVersion, features) {
  const readmePath = path.join(newVersionDir, 'README.md');
  const content = `# ${versionName}

## Overview
This version explores: **${features}**

## Cloned From
- Source: ${sourceVersion}
- Created: ${new Date().toISOString().split('T')[0]}

## What's New in This Version
- [ ] TODO: Document new features
- [ ] TODO: Document changes from source version

## Features
- Basic block editor functionality
- Theme system (light/dark)
- Simple state management
- TODO: Add new features here

## Running This Version

\`\`\`bash
# From project root
npm run dev

# Or serve this version directly
cd playground/${versionName}
python -m http.server 8080
\`\`\`

## File Structure
- \`index.html\` - Main page
- \`components.js\` - Version-specific components
- \`styles.css\` - Version-specific styles
- \`README.md\` - This documentation

## Development Notes
TODO: Add development notes and lessons learned
`;

  fs.writeFileSync(readmePath, content);
}

function main() {
  const args = process.argv.slice(2);
  
  if (args.length < 1) {
    console.error('Usage: npm run new-version <feature-name> [source-version]');
    console.error('Example: npm run new-version sidebar-navigation');
    console.error('Example: npm run new-version state-management v001-basic-foundation');
    process.exit(1);
  }
  
  const featureName = args[0];
  const sourceVersion = args[1] || 'current';
  
  // Validate feature name
  if (!/^[a-z][a-z0-9-]*$/.test(featureName)) {
    console.error('❌ Feature name must be lowercase with hyphens (e.g., sidebar-navigation)');
    process.exit(1);
  }
  
  // Get next version number and create version name
  const versionNumber = getNextVersionNumber();
  const versionName = `v${versionNumber.toString().padStart(3, '0')}-${featureName}`;
  
  // Resolve source directory
  let sourceDir;
  if (sourceVersion === 'current') {
    sourceDir = path.join(playgroundDir, 'current');
    if (!fs.existsSync(sourceDir)) {
      console.error('❌ No current version found. Create v001-basic-foundation first.');
      process.exit(1);
    }
    // Resolve symlink
    sourceDir = fs.readlinkSync(sourceDir);
    sourceDir = path.join(playgroundDir, sourceDir);
  } else {
    sourceDir = path.join(playgroundDir, sourceVersion);
  }
  
  if (!fs.existsSync(sourceDir)) {
    console.error(`❌ Source version '${sourceVersion}' not found`);
    process.exit(1);
  }
  
  const newVersionDir = path.join(playgroundDir, versionName);
  
  if (fs.existsSync(newVersionDir)) {
    console.error(`❌ Version '${versionName}' already exists`);
    process.exit(1);
  }
  
  try {
    // Copy source to new version
    console.log(`📋 Copying ${path.basename(sourceDir)} to ${versionName}...`);
    copyDirectory(sourceDir, newVersionDir);
    
    // Update README with new version info
    updateReadme(newVersionDir, versionName, path.basename(sourceDir), featureName);
    
    // Update symlink to point to new version
    updateSymlink(newVersionDir);
    
    console.log(`✨ Created new version: ${versionName}`);
    console.log(`📁 Location: playground/${versionName}/`);
    console.log(`🔗 Current symlink updated`);
    console.log(`🚀 Run 'npm run dev' to start development`);
    
  } catch (error) {
    console.error('❌ Failed to create new version:', error.message);
    process.exit(1);
  }
}

main();