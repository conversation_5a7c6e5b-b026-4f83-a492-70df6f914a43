#!/usr/bin/env node

/**
 * Development Server
 * Serves the current playground version with live reload
 */

import express from 'express';
import chokidar from 'chokidar';
import path from 'path';
import { fileURLToPath } from 'url';
import fs from 'fs';

const __dirname = path.dirname(fileURLToPath(import.meta.url));
const playgroundDir = path.join(__dirname, '..');
const projectRoot = path.join(__dirname, '../..');

const app = express();
const PORT = 3000;

// Serve static files from current version
const currentPath = path.join(playgroundDir, 'current');
if (!fs.existsSync(currentPath)) {
  console.error('❌ No current version found. Run "npm run new-version" first.');
  process.exit(1);
}

// Resolve symlink to get actual directory
const currentDir = fs.readlinkSync(currentPath);
const currentVersionPath = path.join(playgroundDir, currentDir);

console.log(`🎯 Serving version: ${currentDir}`);
console.log(`📁 Path: ${currentVersionPath}`);

// Serve current version files
app.use(express.static(currentVersionPath));

// Serve shared dependencies
app.use('/shared', express.static(path.join(playgroundDir, 'shared')));

// Serve node_modules for local development
app.use('/node_modules', express.static(path.join(projectRoot, 'node_modules')));

// Serve packages directory
app.use('/packages', express.static(path.join(projectRoot, 'packages')));

// Add CORS headers for development
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');
  next();
});

// Simple live reload functionality
let clients = [];

// SSE endpoint for live reload
app.get('/dev/reload', (req, res) => {
  res.writeHead(200, {
    'Content-Type': 'text/event-stream',
    'Cache-Control': 'no-cache',
    'Connection': 'keep-alive',
    'Access-Control-Allow-Origin': '*'
  });
  
  clients.push(res);
  
  req.on('close', () => {
    clients = clients.filter(client => client !== res);
  });
});

// Watch for file changes in current version
const watcher = chokidar.watch(currentVersionPath, {
  ignored: /node_modules/,
  persistent: true
});

watcher.on('change', (filePath) => {
  const relativePath = path.relative(currentVersionPath, filePath);
  console.log(`🔄 Changed: ${relativePath}`);
  
  // Notify all connected clients
  clients.forEach(client => {
    try {
      client.write(`data: ${JSON.stringify({ type: 'reload', file: relativePath })}\n\n`);
    } catch (error) {
      // Client disconnected
    }
  });
});

// Inject live reload script into HTML files
app.get('*.html', (req, res, next) => {
  const filePath = path.join(currentVersionPath, req.path);
  
  if (fs.existsSync(filePath)) {
    let content = fs.readFileSync(filePath, 'utf8');
    
    // Inject live reload script before closing body tag
    const liveReloadScript = `
    <script>
      // Simple live reload for development
      const eventSource = new EventSource('/dev/reload');
      eventSource.onmessage = function(event) {
        const data = JSON.parse(event.data);
        if (data.type === 'reload') {
          console.log('🔄 Reloading due to change in:', data.file);
          window.location.reload();
        }
      };
      console.log('🔄 Live reload enabled');
    </script>
    `;
    
    content = content.replace('</body>', `${liveReloadScript}</body>`);
    res.send(content);
  } else {
    next();
  }
});

// Version switching endpoint
app.get('/dev/versions', (req, res) => {
  const versions = fs.readdirSync(playgroundDir)
    .filter(name => name.startsWith('v') && fs.statSync(path.join(playgroundDir, name)).isDirectory())
    .map(name => ({
      name,
      current: name === currentDir,
      path: `/dev/switch/${name}`
    }));
  
  res.json(versions);
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Development server running at http://localhost:${PORT}`);
  console.log(`📖 Version: ${currentDir}`);
  console.log(`🔄 Live reload enabled`);
  console.log(`📁 Files: ${currentVersionPath}`);
  console.log('');
  console.log('Available commands:');
  console.log('  npm run new-version <feature>  - Create new version');
  console.log('  npm run list-versions          - List all versions');
  console.log('');
  console.log('Press Ctrl+C to stop');
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n👋 Shutting down development server...');
  watcher.close();
  process.exit(0);
});