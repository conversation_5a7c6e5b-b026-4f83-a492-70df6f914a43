/**
 * FAP Logseq v001 - Basic Foundation Components
 * 
 * This version focuses on:
 * - Basic block editor functionality
 * - Simple page navigation
 * - Theme system
 * - Minimal state management
 */

import { FAPCore, GlobalState, EventBus } from '../shared/fap-core.js';
import { Dependencies } from '../shared/dependencies.js';

// Wait for dependencies to load
await Dependencies.load();

console.log('🚀 FAP Logseq v001 - Basic Foundation starting...');

// Initialize database
let database = Dependencies.SQLite;
if (database) {
  try {
    await database.init();
    GlobalState.set('database', database);
    
    // Initialize default pages if database is empty
    const pages = await database.getPages();
    if (pages.length === 0) {
      console.log('📝 Creating default pages...');
      const welcomePageUuid = await database.createPage('Welcome');
      const journalPageUuid = await database.createPage('Daily Journal');
      const todoPageUuid = await database.createPage('TODO List');
      
      // Create some default blocks
      await database.createBlock('Welcome to FAP Logseq! This is your first block.', null, welcomePageUuid);
      await database.createBlock('Try editing this text, or create new blocks with Enter.', null, welcomePageUuid);
      await database.createBlock('Use Tab to indent blocks and create hierarchy.', null, welcomePageUuid);
      
      GlobalState.set('currentPageUuid', welcomePageUuid);
    } else {
      // Load existing pages
      GlobalState.set('pages', pages);
      GlobalState.set('currentPageUuid', pages[0].uuid);
    }
  } catch (error) {
    console.warn('⚠️ Database initialization failed, using localStorage fallback:', error);
  }
}

/**
 * Theme Toggle Component
 */
class FAPThemeToggle extends HTMLElement {
  connectedCallback() {
    this.addEventListener('click', this.toggleTheme.bind(this));
    this.updateIcon();
  }
  
  toggleTheme() {
    const isDark = FAPCore.css.toggleTheme();
    GlobalState.set('theme', isDark ? 'dark' : 'light');
    this.updateIcon();
    EventBus.emit('theme-changed', { theme: GlobalState.get('theme') });
  }
  
  updateIcon() {
    const theme = FAPCore.css.getTheme();
    this.textContent = theme === 'dark' ? '☀️' : '🌙';
  }
}

/**
 * Search Modal Component
 */
class FAPSearchModal extends HTMLElement {
  connectedCallback() {
    this.addEventListener('click', this.handleBackdropClick.bind(this));
    
    // Close on Escape key
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && !this.classList.contains('hidden')) {
        this.hide();
      }
    });
    
    // Set up search input
    const input = this.querySelector('fap-search-input');
    if (input) {
      input.addEventListener('input', this.handleSearch.bind(this));
    }
  }
  
  handleBackdropClick(e) {
    if (e.target === this) {
      this.hide();
    }
  }
  
  show() {
    this.classList.remove('hidden');
    const input = this.querySelector('fap-search-input');
    if (input) {
      setTimeout(() => input.focus(), 100);
    }
  }
  
  hide() {
    this.classList.add('hidden');
  }
  
  async handleSearch(e) {
    const query = e.target.value.trim();
    const results = this.querySelector('fap-search-results');
    
    if (!query || !results) return;
    
    // Simple search through all block content
    const blocks = document.querySelectorAll('fap-block-content');
    const matches = Array.from(blocks)
      .filter(block => block.textContent.toLowerCase().includes(query.toLowerCase()))
      .map(block => ({
        content: block.textContent,
        blockId: block.closest('fap-block')?.getAttribute('data-block-id'),
        page: block.closest('fap-page-view')?.getAttribute('data-page')
      }));
    
    this.renderSearchResults(matches, query);
  }
  
  renderSearchResults(matches, query) {
    const results = this.querySelector('fap-search-results');
    if (!results) return;
    
    if (matches.length === 0) {
      results.innerHTML = '<div style="padding: 1rem; color: var(--color-text-secondary);">No results found</div>';
      return;
    }
    
    results.innerHTML = matches.map(match => `
      <div style="padding: 0.75rem; border-bottom: 1px solid var(--color-border); cursor: pointer;"
           onclick="FAPComponents.navigateToBlock('${match.blockId}', '${match.page}')">
        <div style="font-size: 0.875rem; color: var(--color-text-secondary);">${match.page}</div>
        <div>${this.highlightText(match.content, query)}</div>
      </div>
    `).join('');
  }
  
  highlightText(text, query) {
    const regex = new RegExp(`(${query})`, 'gi');
    return text.replace(regex, '<mark style="background-color: var(--color-accent); color: white;">$1</mark>');
  }
}

/**
 * Search Trigger Component
 */
class FAPSearchTrigger extends HTMLElement {
  connectedCallback() {
    this.addEventListener('click', this.openSearch.bind(this));
    
    // Global keyboard shortcut
    document.addEventListener('keydown', (e) => {
      if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
        e.preventDefault();
        this.openSearch();
      }
    });
  }
  
  openSearch() {
    const modal = document.querySelector('fap-search-modal');
    if (modal) {
      modal.show();
    }
  }
}

/**
 * Navigation Component
 */
class FAPNavigation extends HTMLElement {
  connectedCallback() {
    this.addEventListener('click', this.handleNavClick.bind(this));
  }
  
  handleNavClick(e) {
    const navItem = e.target.closest('fap-nav-item');
    if (!navItem) return;
    
    const target = navItem.getAttribute('data-target');
    if (target) {
      this.setActiveNav(navItem);
      EventBus.emit('navigation-changed', { target });
    }
  }
  
  setActiveNav(activeItem) {
    this.querySelectorAll('fap-nav-item').forEach(item => {
      item.classList.remove('active');
    });
    activeItem.classList.add('active');
  }
}

/**
 * Page List Component
 */
class FAPPageList extends HTMLElement {
  connectedCallback() {
    this.addEventListener('click', this.handlePageClick.bind(this));
  }
  
  handlePageClick(e) {
    const pageItem = e.target.closest('fap-page-item');
    if (!pageItem) return;
    
    const page = pageItem.getAttribute('data-page');
    if (page) {
      this.setActivePage(pageItem);
      this.showPage(page);
      GlobalState.set('currentPage', page);
    }
  }
  
  setActivePage(activeItem) {
    this.querySelectorAll('fap-page-item').forEach(item => {
      item.classList.remove('active');
    });
    activeItem.classList.add('active');
  }
  
  showPage(pageId) {
    document.querySelectorAll('fap-page-view').forEach(view => {
      view.classList.remove('active');
    });
    
    const targetPage = document.querySelector(`fap-page-view[data-page="${pageId}"]`);
    if (targetPage) {
      targetPage.classList.add('active');
    }
  }
}

/**
 * Block Editor Component
 */
class FAPBlockEditor extends HTMLElement {
  connectedCallback() {
    this.addEventListener('keydown', this.handleKeydown.bind(this));
    this.addEventListener('input', this.handleInput.bind(this));
    this.setupBlocks();
  }
  
  setupBlocks() {
    // Make all block content editable and set up event handlers
    this.querySelectorAll('fap-block-content').forEach(block => {
      block.setAttribute('contenteditable', 'true');
      block.addEventListener('focus', this.handleBlockFocus.bind(this));
      block.addEventListener('blur', this.handleBlockBlur.bind(this));
    });
  }
  
  handleKeydown(e) {
    const block = e.target.closest('fap-block-content');
    if (!block) return;
    
    switch (e.key) {
      case 'Enter':
        if (!e.shiftKey) {
          e.preventDefault();
          this.createNewBlock(block);
        }
        break;
      case 'Tab':
        e.preventDefault();
        if (e.shiftKey) {
          this.outdentBlock(block);
        } else {
          this.indentBlock(block);
        }
        break;
      case 'Backspace':
        if (block.textContent.trim() === '' && this.getSelection().toString() === '') {
          e.preventDefault();
          this.deleteBlock(block);
        }
        break;
    }
  }
  
  handleInput(e) {
    const block = e.target.closest('fap-block-content');
    if (!block) return;
    
    // Simple markdown-style formatting
    this.processInlineFormatting(block);
    
    // Auto-save to state
    this.saveBlockContent(block);
  }
  
  handleBlockFocus(e) {
    const block = e.target.closest('fap-block');
    if (block) {
      GlobalState.set('activeBlock', block.getAttribute('data-block-id'));
    }
  }
  
  handleBlockBlur(e) {
    // Could implement auto-save here
  }
  
  createNewBlock(currentBlock) {
    const newBlockId = 'block-' + Date.now();
    const newBlockHtml = `
      <fap-block data-block-id="${newBlockId}">
        <fap-block-bullet>•</fap-block-bullet>
        <fap-block-content contenteditable></fap-block-content>
      </fap-block>
    `;
    
    const currentBlockElement = currentBlock.closest('fap-block');
    currentBlockElement.insertAdjacentHTML('afterend', newBlockHtml);
    
    // Focus new block
    const newBlock = currentBlockElement.nextElementSibling;
    const newContent = newBlock.querySelector('fap-block-content');
    newContent.focus();
    
    this.setupBlocks(); // Re-setup event handlers
  }
  
  indentBlock(blockContent) {
    const block = blockContent.closest('fap-block');
    const prevBlock = block.previousElementSibling;
    
    if (prevBlock && prevBlock.tagName === 'FAP-BLOCK') {
      let children = prevBlock.querySelector('fap-block-children');
      if (!children) {
        children = document.createElement('fap-block-children');
        prevBlock.appendChild(children);
      }
      
      // Update bullet style for nested blocks
      const bullet = block.querySelector('fap-block-bullet');
      if (bullet) bullet.textContent = '◦';
      
      children.appendChild(block);
    }
  }
  
  outdentBlock(blockContent) {
    const block = blockContent.closest('fap-block');
    const parent = block.closest('fap-block-children');
    
    if (parent) {
      const parentBlock = parent.closest('fap-block');
      
      // Update bullet style back to top level
      const bullet = block.querySelector('fap-block-bullet');
      if (bullet) bullet.textContent = '•';
      
      parentBlock.insertAdjacentElement('afterend', block);
      
      // Remove empty children container
      if (parent.children.length === 0) {
        parent.remove();
      }
    }
  }
  
  deleteBlock(blockContent) {
    const block = blockContent.closest('fap-block');
    const prevBlock = this.findPreviousBlock(block);
    
    if (prevBlock) {
      const prevContent = prevBlock.querySelector('fap-block-content');
      const cursorPos = prevContent.textContent.length;
      
      block.remove();
      prevContent.focus();
      
      // Set cursor to end of previous block
      this.setCursorPosition(prevContent, cursorPos);
    } else if (block.nextElementSibling) {
      // Don't delete if it's the only block
      const nextBlock = block.nextElementSibling.querySelector('fap-block-content');
      block.remove();
      if (nextBlock) nextBlock.focus();
    }
  }
  
  findPreviousBlock(block) {
    let prev = block.previousElementSibling;
    
    // Look for nested blocks in previous sibling
    while (prev && prev.tagName === 'FAP-BLOCK') {
      const children = prev.querySelector('fap-block-children');
      if (children && children.lastElementChild) {
        return children.lastElementChild;
      }
      return prev;
    }
    
    // Look up the parent chain
    const parent = block.closest('fap-block-children');
    if (parent) {
      return parent.closest('fap-block');
    }
    
    return null;
  }
  
  processInlineFormatting(block) {
    // Simple bold/italic detection (could be enhanced)
    const content = block.textContent;
    
    // This is a placeholder - real implementation would need more sophisticated parsing
    if (content.includes('**') || content.includes('*')) {
      // Mark as needing formatting update
      block.setAttribute('data-needs-formatting', 'true');
    }
  }
  
  saveBlockContent(block) {
    const blockId = block.closest('fap-block').getAttribute('data-block-id');
    const content = block.textContent;
    
    // Save to global state
    const blocks = GlobalState.get('blocks') || {};
    blocks[blockId] = {
      content,
      lastModified: Date.now()
    };
    GlobalState.set('blocks', blocks);
  }
  
  setCursorPosition(element, position) {
    const range = document.createRange();
    const selection = window.getSelection();
    
    if (element.firstChild) {
      range.setStart(element.firstChild, Math.min(position, element.textContent.length));
      range.collapse(true);
      selection.removeAllRanges();
      selection.addRange(range);
    }
  }
  
  getSelection() {
    return window.getSelection();
  }
}

// Global component utilities
window.FAPComponents = {
  navigateToBlock(blockId, pageId) {
    // Switch to page
    const pageItem = document.querySelector(`fap-page-item[data-page="${pageId}"]`);
    if (pageItem) {
      pageItem.click();
    }
    
    // Focus block
    const block = document.querySelector(`fap-block[data-block-id="${blockId}"] fap-block-content`);
    if (block) {
      setTimeout(() => {
        block.focus();
        block.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }, 100);
    }
    
    // Hide search modal
    const modal = document.querySelector('fap-search-modal');
    if (modal) {
      modal.hide();
    }
  }
};

// Register all components
FAPCore.defineComponent('fap-theme-toggle', FAPThemeToggle);
FAPCore.defineComponent('fap-search-modal', FAPSearchModal);
FAPCore.defineComponent('fap-search-trigger', FAPSearchTrigger);
FAPCore.defineComponent('fap-navigation', FAPNavigation);
FAPCore.defineComponent('fap-page-list', FAPPageList);
FAPCore.defineComponent('fap-block-editor', FAPBlockEditor);

// Set up global event listeners
document.addEventListener('DOMContentLoaded', () => {
  console.log('✅ FAP Logseq v001 components loaded');
  
  // Initialize theme from localStorage
  const savedTheme = FAPCore.storage.local.get('theme', 'light');
  if (savedTheme === 'dark') {
    document.documentElement.setAttribute('data-theme', 'dark');
  }
  GlobalState.set('theme', savedTheme);
  
  // Update theme toggle icon
  const themeToggle = document.querySelector('fap-theme-toggle');
  if (themeToggle) {
    themeToggle.updateIcon();
  }
  
  // Save theme changes to localStorage
  EventBus.on('theme-changed', (data) => {
    FAPCore.storage.local.set('theme', data.theme);
  });
  
  // Auto-save state periodically
  setInterval(() => {
    const state = GlobalState.getAll();
    FAPCore.storage.local.set('fap-logseq-state', state);
  }, 5000);
});

console.log('🎯 FAP Logseq v001 - Basic Foundation ready!');