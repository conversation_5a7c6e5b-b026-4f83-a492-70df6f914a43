<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>FAP Logseq v001 - Basic Foundation</title>
  <link rel="stylesheet" href="styles.css">
</head>
<body>
  <fap-logseq>
    <fap-header>
      <fap-logo>📚 FAP Logseq</fap-logo>
      <fap-search-trigger>🔍</fap-search-trigger>
      <fap-theme-toggle>🌙</fap-theme-toggle>
    </fap-header>

    <fap-sidebar>
      <fap-navigation>
        <fap-nav-item data-target="blocks" class="active">📝 Blocks</fap-nav-item>
        <fap-nav-item data-target="pages">📄 Pages</fap-nav-item>
        <fap-nav-item data-target="search">🔍 Search</fap-nav-item>
        <fap-nav-item data-target="graph">🔗 Graph</fap-nav-item>
      </fap-navigation>
      
      <fap-page-list>
        <fap-page-item data-page="welcome" class="active">Welcome</fap-page-item>
        <fap-page-item data-page="journal">Daily Journal</fap-page-item>
        <fap-page-item data-page="todo">TODO List</fap-page-item>
      </fap-page-list>
    </fap-sidebar>

    <fap-main>
      <fap-page-view class="active" data-page="welcome">
        <fap-page-header>
          <fap-page-title contenteditable>Welcome to FAP Logseq</fap-page-title>
        </fap-page-header>
        
        <fap-block-editor>
          <fap-block data-block-id="block-1">
            <fap-block-bullet>•</fap-block-bullet>
            <fap-block-content contenteditable>This is the first block. Edit me!</fap-block-content>
          </fap-block>
          
          <fap-block data-block-id="block-2">
            <fap-block-bullet>•</fap-block-bullet>
            <fap-block-content contenteditable>Second block with **bold** and *italic* text</fap-block-content>
            <fap-block-children>
              <fap-block data-block-id="block-2-1">
                <fap-block-bullet>◦</fap-block-bullet>
                <fap-block-content contenteditable>Nested block example</fap-block-content>
              </fap-block>
            </fap-block-children>
          </fap-block>
          
          <fap-block data-block-id="block-3">
            <fap-block-bullet>•</fap-block-bullet>
            <fap-block-content contenteditable>Try adding links: [[Page Name]] or ((block reference))</fap-block-content>
          </fap-block>
        </fap-block-editor>
      </fap-page-view>

      <fap-page-view data-page="journal">
        <fap-page-header>
          <fap-page-title contenteditable>Daily Journal - ${new Date().toDateString()}</fap-page-title>
        </fap-page-header>
        
        <fap-block-editor>
          <fap-block data-block-id="journal-1">
            <fap-block-bullet>•</fap-block-bullet>
            <fap-block-content contenteditable>What happened today?</fap-block-content>
          </fap-block>
        </fap-block-editor>
      </fap-page-view>

      <fap-page-view data-page="todo">
        <fap-page-header>
          <fap-page-title contenteditable>TODO List</fap-page-title>
        </fap-page-header>
        
        <fap-block-editor>
          <fap-block data-block-id="todo-1">
            <fap-block-bullet>☐</fap-block-bullet>
            <fap-block-content contenteditable>Implement block editor drag and drop</fap-block-content>
          </fap-block>
          
          <fap-block data-block-id="todo-2">
            <fap-block-bullet>☑</fap-block-bullet>
            <fap-block-content contenteditable>Create basic FAP components</fap-block-content>
          </fap-block>
        </fap-block-editor>
      </fap-page-view>
    </fap-main>

    <fap-search-modal class="hidden">
      <fap-search-input placeholder="Search blocks and pages..."></fap-search-input>
      <fap-search-results></fap-search-results>
    </fap-search-modal>
  </fap-logseq>

  <!-- Load shared dependencies and core utilities -->
  <script type="module" src="../shared/dependencies.js"></script>
  <script type="module" src="../shared/fap-core.js"></script>
  <script type="module" src="components.js"></script>
</body>
</html>