# v001-basic-foundation

## Overview
This version explores: **Basic block editor with hierarchical editing, simple page navigation, theme system, and minimal state management**

## What's New in This Version
- ✅ Hierarchical block editor with contenteditable blocks
- ✅ Block creation with Enter key
- ✅ Block indentation/outdentation with Tab/Shift+Tab
- ✅ Block deletion with Backspace on empty blocks
- ✅ Page navigation system
- ✅ Light/dark theme toggle
- ✅ Global search with Ctrl+K/Cmd+K
- ✅ Simple state management with reactive updates
- ✅ Auto-save functionality

## Key Features

### Block Editor
- **Hierarchical Structure**: Blocks can be nested with Tab/Shift+Tab
- **Keyboard Navigation**: Enter creates new blocks, Backspace deletes empty blocks
- **Visual Hierarchy**: Different bullet styles for nested levels (• ◦)
- **Focus Management**: Proper cursor positioning and block focus

### Navigation
- **Page Switching**: Click page names in sidebar to switch between pages
- **Search Functionality**: Global search across all blocks with Ctrl+K
- **Responsive Layout**: Works on desktop and mobile

### Theme System
- **Light/Dark Modes**: Toggle with moon/sun icon in header
- **CSS Custom Properties**: Systematic color and spacing variables
- **Smooth Transitions**: Animated theme switching

### State Management
- **Reactive State**: Global state with observer pattern
- **Auto-save**: Block content saved to localStorage every 5 seconds
- **Event Bus**: Component communication via events

## Technical Implementation

### Dependencies
- **Zero Build Process**: Runs directly in browser
- **ES6 Modules**: Uses modern JavaScript module system
- **Custom Elements**: Semantic HTML with Web Components API
- **CSS Custom Properties**: Systematic theming approach

### Architecture
```
components.js           # Main component definitions
styles.css             # Complete styling system
../shared/fap-core.js  # Core utilities and patterns
../shared/dependencies.js # Third-party library management
```

### FAP Principles Demonstrated
1. **Semantic HTML**: `<fap-block-editor>`, `<fap-block>` describe purpose
2. **Progressive Enhancement**: Works without JavaScript for basic content
3. **Zero Dependencies**: No external libraries required for core functionality
4. **Functional Components**: Pure functions for rendering and state updates

## Running This Version

```bash
# From project root
npm run dev

# Or serve this version directly
cd playground/v001-basic-foundation
python -m http.server 8080
```

## Development Notes

### Lessons Learned
1. **Contenteditable Complexity**: Managing cursor position and formatting is tricky
2. **Event Delegation**: Using event delegation on containers works better than individual handlers
3. **State Synchronization**: Simple reactive state is sufficient for basic functionality
4. **CSS Architecture**: Custom properties make theming straightforward

### Known Limitations
1. **No Persistence**: Data only saved to localStorage
2. **Basic Formatting**: Limited markdown support
3. **No Drag & Drop**: Block reordering requires keyboard only
4. **Simple Search**: No fuzzy matching or advanced queries

### Next Version Ideas
- Add drag & drop for block reordering
- Implement [[page links]] and ((block references))
- Add file system integration
- Enhanced markdown formatting
- Graph view for connections

## File Structure
- `index.html` - Main page with semantic FAP components
- `components.js` - Web Components implementation
- `styles.css` - Complete CSS with custom properties
- `README.md` - This documentation

This version establishes the core foundation for a Logseq-like block editor using pure FAP principles.