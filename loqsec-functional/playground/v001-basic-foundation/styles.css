/* FAP Logseq v001 - Basic Foundation Styles */

/* CSS Custom Properties for Theming */
:root {
  /* Light theme colors */
  --color-bg-primary: #ffffff;
  --color-bg-secondary: #f8f9fa;
  --color-bg-tertiary: #e9ecef;
  --color-text-primary: #212529;
  --color-text-secondary: #6c757d;
  --color-border: #dee2e6;
  --color-accent: #0d6efd;
  --color-accent-hover: #0b5ed7;
  
  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  
  /* Typography */
  --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  
  /* Layout */
  --sidebar-width: 280px;
  --header-height: 60px;
  --border-radius: 6px;
}

/* Dark theme */
[data-theme="dark"] {
  --color-bg-primary: #212529;
  --color-bg-secondary: #343a40;
  --color-bg-tertiary: #495057;
  --color-text-primary: #f8f9fa;
  --color-text-secondary: #adb5bd;
  --color-border: #495057;
  --color-accent: #0d6efd;
  --color-accent-hover: #3d8bfd;
}

/* Reset and base styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  line-height: 1.5;
  color: var(--color-text-primary);
  background-color: var(--color-bg-primary);
  transition: color 0.2s ease, background-color 0.2s ease;
}

/* FAP Component Styles */

/* Main Layout */
fap-logseq {
  display: grid;
  grid-template-areas: 
    "header header"
    "sidebar main";
  grid-template-columns: var(--sidebar-width) 1fr;
  grid-template-rows: var(--header-height) 1fr;
  height: 100vh;
}

/* Header */
fap-header {
  grid-area: header;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--spacing-md);
  background-color: var(--color-bg-secondary);
  border-bottom: 1px solid var(--color-border);
}

fap-logo {
  font-size: var(--font-size-lg);
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

fap-search-trigger,
fap-theme-toggle {
  background: none;
  border: none;
  font-size: var(--font-size-lg);
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: var(--border-radius);
  transition: background-color 0.2s ease;
}

fap-search-trigger:hover,
fap-theme-toggle:hover {
  background-color: var(--color-bg-tertiary);
}

/* Sidebar */
fap-sidebar {
  grid-area: sidebar;
  background-color: var(--color-bg-secondary);
  border-right: 1px solid var(--color-border);
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

fap-navigation {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--color-border);
}

fap-nav-item {
  display: block;
  padding: var(--spacing-sm) var(--spacing-md);
  cursor: pointer;
  border-radius: var(--border-radius);
  margin-bottom: var(--spacing-xs);
  transition: background-color 0.2s ease;
}

fap-nav-item:hover {
  background-color: var(--color-bg-tertiary);
}

fap-nav-item.active {
  background-color: var(--color-accent);
  color: white;
}

fap-page-list {
  padding: var(--spacing-md);
  flex: 1;
}

fap-page-item {
  display: block;
  padding: var(--spacing-sm) var(--spacing-md);
  cursor: pointer;
  border-radius: var(--border-radius);
  margin-bottom: var(--spacing-xs);
  transition: background-color 0.2s ease;
}

fap-page-item:hover {
  background-color: var(--color-bg-tertiary);
}

fap-page-item.active {
  background-color: var(--color-accent);
  color: white;
}

/* Main Content */
fap-main {
  grid-area: main;
  overflow-y: auto;
  background-color: var(--color-bg-primary);
}

fap-page-view {
  display: none;
  padding: var(--spacing-xl);
  max-width: 800px;
  margin: 0 auto;
}

fap-page-view.active {
  display: block;
}

fap-page-header {
  margin-bottom: var(--spacing-xl);
}

fap-page-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  border: none;
  background: transparent;
  color: var(--color-text-primary);
  outline: none;
  width: 100%;
  padding: var(--spacing-sm);
  border-radius: var(--border-radius);
  transition: background-color 0.2s ease;
}

fap-page-title:focus {
  background-color: var(--color-bg-secondary);
}

/* Block Editor */
fap-block-editor {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

fap-block {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-sm);
  margin-left: 0;
  position: relative;
}

fap-block-bullet {
  color: var(--color-text-secondary);
  font-weight: bold;
  min-width: 1rem;
  text-align: center;
  margin-top: 2px;
  user-select: none;
}

fap-block-content {
  flex: 1;
  border: none;
  background: transparent;
  color: var(--color-text-primary);
  outline: none;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius);
  transition: background-color 0.2s ease;
  min-height: 1.5em;
}

fap-block-content:hover {
  background-color: var(--color-bg-secondary);
}

fap-block-content:focus {
  background-color: var(--color-bg-secondary);
  box-shadow: 0 0 0 2px var(--color-accent);
}

fap-block-children {
  margin-left: var(--spacing-lg);
  margin-top: var(--spacing-sm);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

/* Search Modal */
fap-search-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding-top: 20vh;
  z-index: 1000;
}

fap-search-modal.hidden {
  display: none;
}

fap-search-input {
  background-color: var(--color-bg-primary);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius);
  padding: var(--spacing-md);
  font-size: var(--font-size-base);
  width: 500px;
  max-width: 90vw;
  outline: none;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

fap-search-input:focus {
  border-color: var(--color-accent);
  box-shadow: 0 0 0 2px var(--color-accent), 0 4px 12px rgba(0, 0, 0, 0.15);
}

fap-search-results {
  margin-top: var(--spacing-md);
  background-color: var(--color-bg-primary);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius);
  max-height: 300px;
  overflow-y: auto;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Utility Classes */
.hidden {
  display: none !important;
}

.active {
  /* Handled by specific component styles */
}

/* Responsive Design */
@media (max-width: 768px) {
  fap-logseq {
    grid-template-areas: 
      "header"
      "main";
    grid-template-columns: 1fr;
  }
  
  fap-sidebar {
    position: fixed;
    left: -var(--sidebar-width);
    top: var(--header-height);
    bottom: 0;
    width: var(--sidebar-width);
    z-index: 100;
    transition: left 0.3s ease;
  }
  
  fap-sidebar.open {
    left: 0;
  }
  
  fap-page-view {
    padding: var(--spacing-md);
  }
}

/* Focus Styles for Accessibility */
*:focus {
  outline: 2px solid var(--color-accent);
  outline-offset: 2px;
}

fap-block-content:focus,
fap-page-title:focus,
fap-search-input:focus {
  outline: none; /* Custom focus styles applied above */
}

/* Animation for theme transitions */
* {
  transition: background-color 0.2s ease, color 0.2s ease, border-color 0.2s ease;
}