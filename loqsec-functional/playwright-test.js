#!/usr/bin/env node

/**
 * Playwright Test for localhost:3000
 * Tests if the playground loads correctly and captures JavaScript errors
 */

import { chromium } from 'playwright';
import { existsSync, mkdirSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __dirname = dirname(fileURLToPath(import.meta.url));

async function testPlayground() {
  console.log('🚀 Starting Playwright test for localhost:3000...');
  
  // Create screenshots directory if it doesn't exist
  const screenshotsDir = join(__dirname, 'screenshots');
  if (!existsSync(screenshotsDir)) {
    mkdirSync(screenshotsDir, { recursive: true });
  }

  const browser = await chromium.launch();
  const context = await browser.newContext();
  const page = await context.newPage();

  // Collect console logs and errors
  const consoleLogs = [];
  const jsErrors = [];
  const sqliteErrors = [];
  const es6ModuleErrors = [];

  // Listen to console events
  page.on('console', msg => {
    const text = msg.text();
    consoleLogs.push({
      type: msg.type(),
      text: text,
      timestamp: new Date().toISOString()
    });

    // Check for SQLite WASM errors
    if (text.toLowerCase().includes('sqlite') && 
        (text.toLowerCase().includes('error') || text.toLowerCase().includes('failed'))) {
      sqliteErrors.push(text);
    }

    // Check for ES6 module errors
    if (text.toLowerCase().includes('module') && 
        (text.toLowerCase().includes('error') || text.toLowerCase().includes('failed'))) {
      es6ModuleErrors.push(text);
    }
  });

  // Listen to page errors
  page.on('pageerror', error => {
    const errorText = error.message;
    jsErrors.push({
      message: errorText,
      stack: error.stack,
      timestamp: new Date().toISOString()
    });

    // Check for SQLite WASM errors in page errors
    if (errorText.toLowerCase().includes('sqlite')) {
      sqliteErrors.push(errorText);
    }

    // Check for ES6 module errors in page errors
    if (errorText.toLowerCase().includes('module')) {
      es6ModuleErrors.push(errorText);
    }
  });

  try {
    console.log('🌐 Navigating to http://localhost:3000...');
    
    // Navigate to the page with a longer timeout
    await page.goto('http://localhost:3000', { 
      waitUntil: 'networkidle',
      timeout: 30000 
    });

    console.log('📸 Taking screenshot...');
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const screenshotPath = join(screenshotsDir, `localhost-3000-${timestamp}.png`);
    await page.screenshot({ 
      path: screenshotPath, 
      fullPage: true 
    });

    console.log(`✅ Screenshot saved to: ${screenshotPath}`);

    // Wait a bit for any dynamic content to load
    console.log('⏱️  Waiting 5 seconds for dynamic content...');
    await page.waitForTimeout(5000);

    // Check if page loaded successfully
    const title = await page.title();
    const url = page.url();
    
    console.log(`📄 Page title: "${title}"`);
    console.log(`🔗 Current URL: ${url}`);

    // Check for specific elements that should be present
    const bodyText = await page.textContent('body');
    const hasContent = bodyText && bodyText.trim().length > 0;

    // Try to detect specific elements or content
    const hasLogseqContent = bodyText?.toLowerCase().includes('logseq') || 
                            bodyText?.toLowerCase().includes('fap') ||
                            await page.locator('[id*="fap"], [class*="fap"], [id*="logseq"], [class*="logseq"]').count() > 0;

    // Final screenshot after waiting
    const finalScreenshotPath = join(screenshotsDir, `localhost-3000-final-${timestamp}.png`);
    await page.screenshot({ 
      path: finalScreenshotPath, 
      fullPage: true 
    });

    console.log('\n📊 Test Results:');
    console.log('================');
    console.log(`✅ Page loaded: ${hasContent ? 'Yes' : 'No'}`);
    console.log(`✅ Has expected content: ${hasLogseqContent ? 'Yes' : 'No'}`);
    console.log(`📝 Console messages: ${consoleLogs.length}`);
    console.log(`❌ JavaScript errors: ${jsErrors.length}`);
    console.log(`🗄️  SQLite WASM errors: ${sqliteErrors.length}`);
    console.log(`📦 ES6 module errors: ${es6ModuleErrors.length}`);

    if (consoleLogs.length > 0) {
      console.log('\n📝 Console Logs:');
      console.log('================');
      consoleLogs.forEach((log, index) => {
        console.log(`${index + 1}. [${log.type.toUpperCase()}] ${log.text}`);
      });
    }

    if (jsErrors.length > 0) {
      console.log('\n❌ JavaScript Errors:');
      console.log('======================');
      jsErrors.forEach((error, index) => {
        console.log(`${index + 1}. ${error.message}`);
        if (error.stack) {
          console.log(`   Stack: ${error.stack.split('\n')[0]}`);
        }
      });
    }

    if (sqliteErrors.length > 0) {
      console.log('\n🗄️  SQLite WASM Related Errors:');
      console.log('===============================');
      sqliteErrors.forEach((error, index) => {
        console.log(`${index + 1}. ${error}`);
      });
    }

    if (es6ModuleErrors.length > 0) {
      console.log('\n📦 ES6 Module Related Errors:');
      console.log('=============================');
      es6ModuleErrors.forEach((error, index) => {
        console.log(`${index + 1}. ${error}`);
      });
    }

    return {
      success: hasContent,
      hasExpectedContent: hasLogseqContent,
      consoleLogs,
      jsErrors,
      sqliteErrors,
      es6ModuleErrors,
      screenshots: [screenshotPath, finalScreenshotPath]
    };

  } catch (error) {
    console.error('❌ Error during test:', error.message);
    
    // Try to take a screenshot even if there was an error
    try {
      const errorScreenshotPath = join(screenshotsDir, `localhost-3000-error-${Date.now()}.png`);
      await page.screenshot({ path: errorScreenshotPath, fullPage: true });
      console.log(`📸 Error screenshot saved to: ${errorScreenshotPath}`);
    } catch (screenshotError) {
      console.log('Could not take error screenshot');
    }

    return {
      success: false,
      error: error.message,
      consoleLogs,
      jsErrors,
      sqliteErrors,
      es6ModuleErrors
    };
  } finally {
    await browser.close();
  }
}

// Run the test
testPlayground()
  .then(result => {
    if (result.success) {
      console.log('\n🎉 Test completed successfully!');
      if (result.screenshots) {
        console.log(`📸 Screenshots available at:`);
        result.screenshots.forEach(path => console.log(`   ${path}`));
      }
    } else {
      console.log('\n⚠️  Test completed with issues');
      if (result.error) {
        console.log(`❌ Main error: ${result.error}`);
      }
    }

    // Exit with appropriate code
    const hasErrors = result.jsErrors?.length > 0 || 
                     result.sqliteErrors?.length > 0 || 
                     result.es6ModuleErrors?.length > 0 || 
                     !result.success;
    process.exit(hasErrors ? 1 : 0);
  })
  .catch(error => {
    console.error('💥 Fatal error:', error);
    process.exit(1);
  });