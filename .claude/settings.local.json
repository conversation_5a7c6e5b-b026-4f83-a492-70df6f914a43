{"permissions": {"allow": ["Bash(yarn install)", "Bash(ls:*)", "Bash(yarn postinstall)", "Bash(yarn watch:*)", "<PERSON><PERSON>(timeout 30s yarn watch)", "Ba<PERSON>(clojure:*)", "Bash(node:*)", "Bash(yarn --version)", "Bash(yarn test)", "Bash(yarn release-app)", "<PERSON><PERSON>(curl:*)", "Bash(yarn dev-electron-app:*)", "<PERSON><PERSON>(open:*)", "WebFetch(domain:localhost)", "<PERSON><PERSON>(env)", "Bash(pgrep:*)", "Bash(npx shadow-cljs compile:*)", "<PERSON><PERSON>(npx shadow-cljs watch:*)", "<PERSON><PERSON>(playwright show:*)", "<PERSON><PERSON>(playwright --help)", "<PERSON><PERSON>(playwright screenshot:*)", "Bash(npx:*)", "<PERSON><PERSON>(playwright open:*)", "<PERSON><PERSON>(playwright codegen:*)", "Bash(npm init:*)", "Bash(npm install:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(mv:*)", "Bash(find:*)", "Bash(tree:*)", "Bash(ln:*)", "WebFetch(domain:docs.anthropic.com)", "<PERSON><PERSON>(claude mcp:*)", "Bash(rm:*)", "<PERSON><PERSON>(chmod:*)"], "deny": []}}