# CLAUDE.md - Project Overview

Research project reimplementing Logseq using **Freedom Application Platform (FAP)** functional programming principles.

## Quick Start

- **Current Focus**: `loqsec-functional/` - FAP-based implementation 
- **Reference**: `loqsec-clone/` - Original codebase analysis
- **FAP Guides**: See `_fap/design_guides/` for architecture patterns

## Structure

```
├── _fap/                    # FAP design guides and patterns
├── loqsec-functional/       # ✅ Functional implementation (active)
├── loqsec-clone/           # Original Logseq for reference
├── docs_md/                # Architecture analysis and planning
├── tests/integration/      # Component testing
└── tools/development/      # Development utilities
```

## Status

- ✅ **FAP Architecture** - Established design patterns
- ✅ **Tree Component** - Working reference implementation
- 🔄 **Block Editor** - Next component development
- ⏳ **Graph View** - Planned

For detailed implementation see `loqsec-functional/CLAUDE.md` and FAP patterns in `_fap/design_guides/fap_best_practices.md`.